import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import useActions from "../../../../../common/redux/data/useActions";
import { useAppSelector } from "../../../../../common/redux/store";
import { posOrderSelector } from "../../../redux/selector";
import usePosActions from "../../../redux/usePosActions";
import { PosOrderService } from "../PosOrderService";
import { GiMoneyStack } from "react-icons/gi";
import { BiMobileVibration } from "react-icons/bi";
import { PaymentsEnum } from "../../../../../common/enums/DataEnums";
import { FC, useEffect, useMemo, useState } from "react";
import NumpadComponent, { useClearNumpad } from "../../../../../common/components/NumpadComponent";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import { orderUtils } from "../../../../../common/utils/OrderUtils";
import { fixedNumber } from "../../../../../common/utils/numberUtils";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { PiCalculator } from "react-icons/pi";
import PriceComponent from "../../../../../common/components/PriceComponent";
import { PosInvoicesService } from "../../../pages/invoices/PosInvoicesService";
import { IPosOrderModel } from "../../../../../common/models/PosOrderModel";
import { IPosReturnedOrderModel } from "../../../../../common/models/PosReturnedOrderModel";
import { LuNotebookPen } from "react-icons/lu";

interface IProps {
    isReturnedOrder?: boolean;
    oldOrder?: IPosOrderModel;
    returnedOrder?: IPosReturnedOrderModel;
    getNumbersOnly?: {
        onSubmit?: (cash: number, network: number, deferred: number) => void;
        amount?: number;
    }
}

const PosOrderPaymentModal: FC<IProps> = ({
    isReturnedOrder = false,
    oldOrder,
    returnedOrder,
    getNumbersOnly,
}) => {
    const posActions = usePosActions();
    const actions = useActions();
    const order = useAppSelector(posOrderSelector);
    const [isSplitModalType, setIsSplitModalType] = useState(false);
    const { clear, clearNumpad } = useClearNumpad();
    const [cash, setCash] = useState("0");
    const [network, setNetwork] = useState("0");

    const { total } = useMemo(() => {
        if (getNumbersOnly) return { total: getNumbersOnly.amount ?? 0 };
        return orderUtils.getPosOrderData(order)
    }, [order]);

    useEffect(() => {
        if (isSplitModalType) {
            setCash(total.toString());
            actions.openModal({ size: "sm" });
        }
    }, [isSplitModalType, total]);

    const handleChangePaymentType = (paymentType: PaymentsEnum) => {
        const isCash = paymentType === PaymentsEnum.CASH;
        setCash(isCash ? total.toString() : "0");
        setNetwork(isCash ? "0" : total.toString());
    };

    const handleOnNumpadChange = (text: string) => {
        const val = fixedNumber(text);
        const fraction = text.split(".")[1];
        const isValidFraction = fraction?.length <= 2;

        if (text === "0") {
            setNetwork(total.toString());
            setCash("0");
            return;
        }

        if (text === "0.") {
            setNetwork(fixedNumber(total).toString());
            setCash("0.");
            return;
        }

        if (val === 0) {
            setNetwork("0");
            setCash(total.toString());
            return;
        }

        if (val > total || (fraction && !isValidFraction)) clearNumpad();
        else {
            setNetwork(fixedNumber(total - val).toString());
            setCash(
                val.toString() +
                (isValidFraction && fixedNumber(fraction) === 0 ? "." + fraction : "")
            );
        }
    };

    const handleOrderSubmit = (
        cashValue?: number,
        networkValue?: number,
        deferredValue: number = 0
    ) => {
        if (getNumbersOnly) {
            getNumbersOnly.onSubmit?.(
                cashValue ?? fixedNumber(cash),
                networkValue ?? fixedNumber(network),
                deferredValue
            );
            return;
        }

        if (isReturnedOrder) {
            PosInvoicesService.handleOnSubmitReturnOrder(
                oldOrder!,
                returnedOrder!,
                cashValue ?? fixedNumber(cash),
                networkValue ?? fixedNumber(network),
                deferredValue,
                actions,
                posActions
            );
            return;
        }

        PosOrderService.handleSubmitOrder(
            order,
            cashValue ?? fixedNumber(cash),
            networkValue ?? fixedNumber(network),
            deferredValue,
            actions,
            posActions
        );
    };

    return (
        <>
            {!isSplitModalType && (
                <div className="flex justify-between items-center px-2 gap-2">
                    <IconButtonComponent
                        icon={<GiMoneyStack />}
                        text={TranslateConstants.CASH}
                        onClick={() => handleOrderSubmit(fixedNumber(total), 0)}
                        height="h-20"
                        textSize="text-lg"
                        iconSize="text-3xl"
                    />
                    <IconButtonComponent
                        icon={<BiMobileVibration />}
                        text={TranslateConstants.NETWORK}
                        onClick={() => handleOrderSubmit(0, fixedNumber(total))}
                        height="h-20"
                        textSize="text-lg"
                        iconSize="text-3xl"
                    />
                    {(!getNumbersOnly && order.customer && (!returnedOrder || order.deferred > 0)) && (
                        <IconButtonComponent
                            icon={<LuNotebookPen />}
                            text={TranslateConstants.CREDIT}
                            onClick={() => handleOrderSubmit(0, 0, fixedNumber(total))}
                            height="h-20"
                            textSize="text-lg"
                            iconSize="text-3xl"
                        />
                    )}
                    <IconButtonComponent
                        icon={<PiCalculator />}
                        text={TranslateConstants.OTHER}
                        onClick={() => setIsSplitModalType(true)}
                        bgColor="transparent"
                        borderColor="primary"
                        textColor="primary"
                        height="h-20"
                        textSize="text-lg"
                        iconSize="text-3xl"
                    />
                </div>
            )}
            {isSplitModalType && (
                <div className="flex flex-col gap-2 px-2">
                    <div className="flex justify-between items-center gap-2">
                        <div className="w-full flex flex-col gap-2">
                            <ButtonComponent
                                text={TranslateHelper.t(TranslateConstants.CASH)}
                                iconComponent={<GiMoneyStack className="text-xl" />}
                                bgColor="transparent"
                                borderColor="slate-600"
                                textColor="slate-600"
                                onClick={() => handleChangePaymentType(PaymentsEnum.CASH)}
                            />
                            <div
                                className={
                                    "w-full h-16 font-tajawal-medium flex justify-center items-center border rounded text-2xl" +
                                    " " +
                                    (fixedNumber(cash) > 0
                                        ? "border-[#226bb2]"
                                        : "border-gray-400") +
                                    " " +
                                    (fixedNumber(cash) > 0 ? "text-[#226bb2]" : "text-gray-400")
                                }
                            >
                                {cash}
                            </div>
                        </div>
                        <div className="w-full flex flex-col gap-2">
                            <ButtonComponent
                                text={TranslateHelper.t(TranslateConstants.NETWORK)}
                                iconComponent={<BiMobileVibration className="text-xl" />}
                                bgColor="transparent"
                                borderColor="slate-600"
                                textColor="slate-600"
                                onClick={() => handleChangePaymentType(PaymentsEnum.BANK)}
                            />
                            <div
                                className={
                                    "w-full h-16 font-tajawal-medium flex justify-center items-center border rounded text-2xl" +
                                    " " +
                                    (fixedNumber(network) > 0
                                        ? "border-[#226bb2]"
                                        : "border-gray-400") +
                                    " " +
                                    (fixedNumber(network) > 0
                                        ? "text-[#226bb2]"
                                        : "text-gray-400")
                                }
                            >
                                {network}
                            </div>
                        </div>
                    </div>
                    <NumpadComponent
                        showResult={false}
                        padding="p-2"
                        allowDot={true}
                        allowZeroAtFirst={true}
                        onChange={handleOnNumpadChange}
                        clear={clear}
                    />
                </div>
            )}
            <ModalButtonsComponent
                {...(isSplitModalType && {
                    component: <PriceComponent price={total} color="white" />,
                })}
                onClick={() => handleOrderSubmit()}
            />
        </>
    );
};

export default PosOrderPaymentModal;
