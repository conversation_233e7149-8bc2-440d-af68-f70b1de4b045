import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminTableInputs } from "./AdminTablesInterface";

export class AdminTablesValidation {
    static inputsValidation = (values: IAdminTableInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        }

        return isValid;
    };
}
