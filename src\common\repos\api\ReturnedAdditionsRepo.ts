import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IMostSellingProductsModel } from "../../models/MostSellingProductsModel";
import { debug } from "../../utils/CommonUtils";

export class ReturnedAdditionsApiRepo {
    static async getSum (startTime: number, endTime: number) {
        try {
            const res = await AxiosHelper.get<IMostSellingProductsModel[]>(
                EndPointsEnums.RETURNED_ORDER_PRODUCTS_ADDITIONS_SUM,
                { params: { startTime, endTime } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReturnedAdditionsApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}
