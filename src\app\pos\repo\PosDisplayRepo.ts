import axios from "axios";
import { DisplayType } from "../../../common/enums/DataEnums";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { debug } from "../../../common/utils/CommonUtils";
import { PrinterServerHost } from "../../../common/constants/CommonConstants";
import { SmallScreenHelper } from "../../../common/helpers/SmallScreenHelper";

export class PosDisplayRepo {
    static async connect(path: string, baudRate: number, isActive: boolean = false) {
        if (!SmallScreenHelper.get().isActive && !isActive) return;
        try {
            const { data } = await axios.get<{ success: boolean; message: string }>(
                `${PrinterServerHost}/${EndPointsEnums.DISPLAY_CONNECT}`,
                {
                    params: { path, baudRate },
                }
            );
            if (!data.success) throw new Error(data.message);
        } catch (error) {
            debug(`PosDisplayRepo [connect] Error: `, error);
            throw error;
        }
    }

    static async isConnected() {
        if (!SmallScreenHelper.get().isActive) return;
        try {
            const { data } = await axios.get<{ success: boolean; data: boolean }>(
                `${PrinterServerHost}/${EndPointsEnums.DISPLAY_IS_CONNECTED}`
            );
            if (!data.success) throw new Error("Not connected");
            return data.data;
        } catch (error) {
            debug(`PosDisplayRepo [isConnected] Error: `, error);
            throw error;
        }
    }

    static async disconnect() {
        if (!SmallScreenHelper.get().isActive) return;
        try {
            const connected = await this.isConnected();
            if (!connected) return;

            const { data } = await axios.get<{ success: boolean; message: string }>(
                `${PrinterServerHost}/${EndPointsEnums.DISPLAY_DISCONNECT}`
            );
            if (!data.success) throw new Error(data.message);
        } catch (error) {
            debug(`PosDisplayRepo [disconnect] Error: `, error);
            throw error;
        }
    }

    static async getPorts() {
        if (!SmallScreenHelper.get().isActive) return;
        try {
            const { data } = await axios.get<{ success: boolean; data: { path: string }[] }>(
                `${PrinterServerHost}/${EndPointsEnums.DISPLAY_PORTS}`
            );
            if (!data.success) throw new Error("Failed to get ports");
            return data.data;
        } catch (error) {
            debug(`PosDisplayRepo [getPorts] Error: `, error);
            throw error;
        }
    }

    static async showPrice(price: number | string, type: DisplayType = "total") {
        if (!SmallScreenHelper.get().isActive) return;
        try {
            const connected = await this.isConnected();
            if (!connected) return;

            const { data } = await axios.get<{ success: boolean; message: string }>(
                `${PrinterServerHost}/${EndPointsEnums.DISPLAY_PRICE}`,
                {
                    params: { price, type },
                }
            );
            if (!data.success) throw new Error(data.message);
        } catch (error) {
            debug(`PosDisplayRepo [showPrice] Error: `, error);
            throw error;
        }
    }
}
