import { useEffect, useState } from "react";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { IPrintingSettings } from "../../../../../common/interfaces";
import { PrintersHelper } from "../../../../../common/helpers/PrintersHelper";
import useActions from "../../../../../common/redux/data/useActions";
import { ToastHelper } from "../../../../../common/helpers/ToastHelper";

const PosPrinterSettingsModal = () => {
    const actions = useActions();
    const [isDisabled, setIsDisabled] = useState(true);
    const [state, setState] = useState<IPrintingSettings>(
        PrintersHelper.getPrintingSettings()
    );

    useEffect(() => {
        const printingSettings = PrintersHelper.getPrintingSettings();
        setIsDisabled(
            state.isInvoicePrintingActive === printingSettings.isInvoicePrintingActive &&
            state.isKitchenPrintingActive === printingSettings.isKitchenPrintingActive &&
            state.isLogoPrintingActive === printingSettings.isLogoPrintingActive &&
            state.isShiftPrintingActive === printingSettings.isShiftPrintingActive &&
            state.isVouchersPrintingActive === printingSettings.isVouchersPrintingActive
        );
    }, [state]);

    const handleOnSave = async () => {
        PrintersHelper.setPrintingSettings(state);
        setIsDisabled(true);
        actions.closeModal();
        ToastHelper.success();
    }

    return (
        <>
            <div className="px-2 font-tajawal-bold">
                <div className={"flex justify-between"}>
                    <label>{TranslateHelper.t(TranslateConstants.ACTIVATE_INVOICE_PRINTING)}</label>
                    <ToggleButtonComponent
                        value={state.isInvoicePrintingActive}
                        onChange={(isInvoicePrintingActive) => setState({ ...state, isInvoicePrintingActive })}
                    />
                </div>
                <DividerComponent className="my-3" />
                <div className={"flex justify-between"}>
                    <label>{TranslateHelper.t(TranslateConstants.ACTIVATE_KITCHEN_PRINTING)}</label>
                    <ToggleButtonComponent
                        value={state.isKitchenPrintingActive}
                        onChange={(isKitchenPrintingActive) => setState({ ...state, isKitchenPrintingActive })}
                    />
                </div>
                <DividerComponent className="my-3" />
                <div className={"flex justify-between"}>
                    <label>{TranslateHelper.t(TranslateConstants.ACTIVATE_SHIFT_PRINTING)}</label>
                    <ToggleButtonComponent
                        value={state.isShiftPrintingActive}
                        onChange={(isShiftPrintingActive) => setState({ ...state, isShiftPrintingActive })}
                    />
                </div>
                <DividerComponent className="my-3" />
                <div className={"flex justify-between"}>
                    <label>{TranslateHelper.t(TranslateConstants.ACTIVATE_VOUCHERS_PRINTING)}</label>
                    <ToggleButtonComponent
                        value={state.isVouchersPrintingActive}
                        onChange={(isVouchersPrintingActive) => setState({ ...state, isVouchersPrintingActive })}
                    />
                </div>
                <DividerComponent className="my-3" />
                <div className={"flex justify-between"}>
                    <label>{TranslateHelper.t(TranslateConstants.ACTIVATE_LOGO_PRINTING)}</label>
                    <ToggleButtonComponent
                        value={state.isLogoPrintingActive}
                        onChange={(isLogoPrintingActive) => setState({ ...state, isLogoPrintingActive })}
                    />
                </div>
            </div>
            <ModalButtonsComponent
                text={TranslateConstants.SAVE}
                isDisabled={isDisabled}
                onClick={handleOnSave}
            />
        </>
    );
}

export default PosPrinterSettingsModal;
