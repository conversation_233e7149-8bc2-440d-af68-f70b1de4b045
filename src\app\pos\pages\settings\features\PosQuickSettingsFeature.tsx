import ButtonComponent from "../../../../../common/components/ButtonComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { SlScreenDesktop } from "react-icons/sl";
import { RiLogoutCircleRLine } from "react-icons/ri";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import PosSyncButtonComponent from "../../../components/PosSyncButtonComponent";
import PosEndDayButtonComponent from "../../../components/PosEndDayButtonComponent";
import { RoleService } from "../../../../role/RoleService";
import { useNavigate } from "react-router-dom";
import { AuthService } from "../../../../auth/AuthService";
import { MdOutlineGTranslate } from "react-icons/md";
import { ShowLanguageModal } from "../../../../../common/components/LanguageComponent";
import { AppVersion } from "../../../../../common/constants/CommonConstants";
import useActions from "../../../../../common/redux/data/useActions";
import PosSmallScreenSettingsModal from "../modals/PosSmallScreenSettingsModal";
import { SlPrinter } from "react-icons/sl";
import PosPrinterSettingsModal from "../modals/PosPrinterSettingsModal";

const PosQuickSettingsFeature = () => {
    const actions = useActions();
    const { translate } = useTranslate();
    const navigate = useNavigate();

    const handleOnDashboardClick = () => RoleService.redirectToRolePage(navigate);
    const handleOnSignOutClick = () => AuthService.logout(navigate);

    const handleOnSmallScreenSettingsClick = () => {
        actions.openModal({
            component: <PosSmallScreenSettingsModal />,
            title: TranslateConstants.DIGITAL_SCREEN_SETTINGS,
            showButtons: false,
            size: "sm",
        });
    }

    const handleOnPrinterSettingsClick = () => {
        actions.openModal({
            component: <PosPrinterSettingsModal />,
            title: TranslateConstants.ACTIVATE_PRINTING_SETTINGS,
            showButtons: false,
            size: "sm",
        });
    }

    return (
        <>
            <div className="text-2xl font-tajawal-bold">
                {translate(TranslateConstants.QUICK_SETTINGS)}
            </div>
            <DividerComponent />
            <ButtonComponent
                text={TranslateConstants.DASHBOARD}
                iconComponent={<SlScreenDesktop className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={handleOnDashboardClick}
            />
            <PosSyncButtonComponent />
            <PosEndDayButtonComponent />
            <ButtonComponent
                text={TranslateConstants.LANGUAGE}
                iconComponent={<MdOutlineGTranslate className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={ShowLanguageModal}
            />
            <ButtonComponent
                text={TranslateConstants.DIGITAL_SCREEN_SETTINGS}
                iconComponent={<SlScreenDesktop className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={handleOnSmallScreenSettingsClick}
            />
            <ButtonComponent
                text={TranslateConstants.ACTIVATE_PRINTING_SETTINGS}
                iconComponent={<SlPrinter className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={handleOnPrinterSettingsClick}
            />
            <div className="mt-auto">
                <div className="bg-inherit active:text-inherit flex justify-center mb-2 font-tajawal-bold">
                    v {AppVersion}
                </div>
                <ButtonComponent
                    text={TranslateConstants.SIGN_OUT}
                    iconComponent={<RiLogoutCircleRLine className="text-xl" />}
                    bgColor="warning"
                    isCentered={false}
                    className="flex-row-reverse py-3 rounded-lg"
                    onClick={handleOnSignOutClick}
                />
            </div>
        </>
    );
};

export default PosQuickSettingsFeature;
