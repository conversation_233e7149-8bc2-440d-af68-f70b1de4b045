import {
    OrderStatusEnum,
    OrderTypeEnum,
    TableStatusEnum,
} from "../../../../common/enums/DataEnums";
import { OrderHelper } from "../../../../common/helpers/OrderHelper";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { ITableModel } from "../../../../common/models/TableModel";
import { orderUtils } from "../../../../common/utils/OrderUtils";
import { debug } from "../../../../common/utils/CommonUtils";
import { IPosOrder, IPosOrderProduct, IPosReturnedOrder } from "../../interface";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { DeliveryAppsHelper } from "../../../../common/helpers/DeliveryAppsHelper";
import { AppLocalDB } from "../../../../common/config/localDB";
import { ReturnedOrderHelper } from "../../../../common/helpers/ReturnedOrderHelper";

export class PosOrderUtils {
    static async checkOrderDataIssue(order: IPosOrder | IPosOrderModel | IPosReturnedOrder): Promise<void> {
        // if (!order.orderProducts?.length) {
        throw new Error("No products found");
        // }

        for (const pr of order.orderProducts) {
            const calculated = orderUtils.getPosProductAmounts(
                pr.price,
                pr.quantity,
                order.selectedDiscount?.amount,
                pr.isSubjectToTobaccoTax
            );

            const fieldsToCheck: (keyof typeof calculated)[] = ["subTotal", "vat", "total", "discount", "tobaccoTax"];
            for (const field of fieldsToCheck) {
                if (pr[field] !== calculated[field]) {
                    throw new Error(`OrderProduct ${pr.product?.id ?? ""} ${field} mismatch`);
                }
            }

            if (
                pr.price &&
                order.selectedDiscount?.amount !== 100 &&
                (!pr.total || !pr.subTotal || (order.selectedDiscount?.amount && !pr.discount))
            ) {
                throw new Error(`OrderProduct ${pr.product?.id ?? ""} missing totals`);
            }

            if (pr.orderProductAdditions?.length) {
                for (const ad of pr.orderProductAdditions) {
                    const additionTotalPrice = ad.price * ad.quantity;
                    const { subTotal, vat, total, discount } = orderUtils.getAmounts(
                        additionTotalPrice,
                        order.selectedDiscount?.amount
                    );

                    if (subTotal !== ad.subTotal || vat !== ad.vat || total !== ad.total || discount !== ad.discount) {
                        throw new Error(`OrderProductAddition ${ad.addition?.id ?? ""} amounts mismatch`);
                    }

                    if (
                        ad.price &&
                        order.selectedDiscount?.amount !== 100 &&
                        (!ad.total || !ad.subTotal || (order.selectedDiscount?.amount && !ad.discount))
                    ) {
                        throw new Error(`OrderProductAddition ${ad.addition?.id ?? ""} missing totals`);
                    }
                }
            }
        }
    }

    static handleOrderProductsData = (
        order: IPosOrder | IPosOrderModel
    ): IPosOrderProduct[] => {
        return order.orderProducts.map((product) => {
            const {
                subTotal: productSubTotal,
                vat: productVat,
                total: productTotal,
                discount: productDiscount,
                tobaccoTax: productTobaccoTax,
            } = orderUtils.getPosProductAmounts(
                product.price,
                product.quantity,
                order.selectedDiscount?.amount,
                product.isSubjectToTobaccoTax
            );

            const orderProductAdditions = product.orderProductAdditions?.map((ad) => {
                const additionTotalPrice = ad.price * ad.quantity;
                const {
                    subTotal: additionSubTotal,
                    vat: additionVat,
                    total: additionTotal,
                    discount: additionDiscount,
                } = orderUtils.getAmounts(
                    additionTotalPrice,
                    order.selectedDiscount?.amount
                );

                return {
                    ...ad,
                    subTotal: additionSubTotal,
                    discount: additionDiscount,
                    vat: additionVat,
                    total: additionTotal,
                    startTime: ad.startTime ?? new Date().getTime(),
                };
            });

            return {
                ...product,
                subTotal: productSubTotal,
                discount: productDiscount,
                tobaccoTax: productTobaccoTax,
                vat: productVat,
                total: productTotal,
                startTime: product.startTime ?? new Date().getTime(),
                orderProductAdditions,
            };
        });
    };

    static handleOrderData = async (
        order: IPosOrder | IPosOrderModel,
        cash: number = 0,
        network: number = 0,
        deferred: number = 0
    ): Promise<IPosOrder | IPosOrderModel> => {
        try {
            if (!order.orderProducts.length) throw new Error("No products found");

            const deliverApps = DeliveryAppsHelper.getDeliveryApps();
            const { total, vat, subTotal, discount, tobaccoTax } = orderUtils.getPosOrderData(order);
            let orderNumber = order.orderNumber;
            let invoiceNumber = order.invoiceNumber;
            let status = OrderStatusEnum.IN_PROGRESS;
            let deliveryAppFee = 0;
            let totalDue = 0;
            const startTime = (order.isPaused ? new Date() : order.startTime) ?? new Date();

            if (!order.orderNumber || order.isPaused) {
                const { count } = await PosOrderRepo.getAndCountOrders();
                orderNumber = (count + 1).toString();
            }

            if (!order.invoiceNumber || order.isPaused) {
                invoiceNumber = OrderHelper.getNextInvoiceNumber();
            }

            if (
                order.type === OrderTypeEnum.TAKE_AWAY ||
                order.type === OrderTypeEnum.DELIVERY_APP ||
                order.status === OrderStatusEnum.IN_PROGRESS
            ) {
                status = OrderStatusEnum.COMPLETED;
            }

            if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
                const percentage = (deliverApps as any)?.[order.deliveryApp];
                deliveryAppFee = total * (percentage / 100);
                totalDue = total - deliveryAppFee;
            }

            const orderProducts = PosOrderUtils.handleOrderProductsData(order);

            return {
                ...order,
                orderId: AppLocalDB.generateId(),
                startTime,
                endTime: new Date(),
                invoiceNumber,
                orderNumber,
                status,
                total,
                vat,
                subTotal,
                discount,
                tobaccoTax,
                cash,
                network,
                deferred,
                deliveryAppFee,
                totalDue,
                orderProducts,
            };
        } catch (error) {
            debug(`PosOrderUtils [handleOrderData] Error: ${error}`);
            throw error;
        }
    };

    static handleReturnOrderData = async (
        returnedOrder: IPosReturnedOrder,
        cash: number = 0,
        network: number = 0,
        deferred: number = 0
    ): Promise<IPosReturnedOrder> => {
        try {
            if (!returnedOrder.orderProducts.length) throw new Error("No products found");

            const { total, vat, subTotal, discount, tobaccoTax } = orderUtils.getPosOrderData(returnedOrder as IPosOrder);
            const deliverApps = DeliveryAppsHelper.getDeliveryApps();
            let deliveryAppFee = 0;
            let totalDue = 0;

            if (returnedOrder.type === OrderTypeEnum.DELIVERY_APP && returnedOrder.deliveryApp) {
                const percentage = (deliverApps as any)?.[returnedOrder.deliveryApp];
                deliveryAppFee = total * (percentage / 100);
                totalDue = total - deliveryAppFee;
            }

            return {
                id: AppLocalDB.generateId(),
                orderId: returnedOrder.orderId,
                shiftId: returnedOrder.shiftId,
                type: returnedOrder.type,
                deliveryApp: returnedOrder.deliveryApp,
                orderProducts: returnedOrder.orderProducts,
                table: returnedOrder.table,
                customer: returnedOrder.customer,
                selectedDiscount: returnedOrder.selectedDiscount,
                invoiceNumber: returnedOrder.invoiceNumber,
                returnedInvoiceNumber: ReturnedOrderHelper.getNextReturnedInvoiceNumber(),
                orderNumber: returnedOrder.orderNumber,
                startTime: new Date(),
                total,
                vat,
                subTotal,
                discount,
                tobaccoTax,
                cash,
                network,
                deferred,
                deliveryAppFee,
                totalDue,
            };
        } catch (error) {
            debug(`PosOrderUtils [handleReturnOrderData] Error: ${error}`);
            throw error;
        }
    };

    static handleTableData = (order: IPosOrderModel): ITableModel | undefined => {
        const table = order.table;
        if (!table) return;

        let tableOrder = undefined;
        let status = TableStatusEnum.AVAILABLE;
        let startTime = undefined;

        if (order.status === OrderStatusEnum.IN_PROGRESS) {
            tableOrder = order;
            status = TableStatusEnum.OCCUPIED;
            startTime = order.startTime;
        }

        return {
            ...table,
            order: tableOrder,
            status,
            startTime,
        };
    };

    static handleProductDifference = (
        oldOrder: IPosOrder,
        newOrder: IPosOrder
    ): IPosOrder => {
        const oldProducts = oldOrder.orderProducts;
        const newProducts = newOrder.orderProducts;
        const products = newProducts.filter((el) => {
            const oldProduct = oldProducts.find((old) => old.product.id === el.product.id);
            return !oldProduct || oldProduct.quantity !== el.quantity || oldProduct.isDeleted;
        });
        const deletedProducts = oldProducts.filter((el) => {
            return !newProducts.some((item) => item.product.id === el.product.id);
        });
        const updatedProductsQuantity = [...products, ...deletedProducts].map((el) => {
            const oldProduct = oldProducts.find((old) => old.product.id === el.product.id);
            const isDeleted = el.quantity === oldProduct?.quantity;
            const quantity = (oldProduct && !isDeleted) ? el.quantity - oldProduct.quantity : el.quantity;
            return { ...el, quantity, isDeleted };
        });
        return { ...newOrder, orderProducts: updatedProductsQuantity };
    };
}
