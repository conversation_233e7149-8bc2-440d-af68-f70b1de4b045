import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IPaymentVoucherSumModel } from "../../../../../common/models/PaymentVoucherSumModel";

interface IProps {
    paymentVoucherSum?: IPaymentVoucherSumModel;
}

const AdminPaymentVoucherTotalInfoFeature: FC<IProps> = ({ paymentVoucherSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.ORDERS_COUNT, value: paymentVoucherSum?.count, fixedVal: 0 },
                { text: TranslateConstants.CASH, value: paymentVoucherSum?.cash },
                { text: TranslateConstants.NETWORK, value: paymentVoucherSum?.network },
            ]}
        />
    );
};

export default AdminPaymentVoucherTotalInfoFeature;
