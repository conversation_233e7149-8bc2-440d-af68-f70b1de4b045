import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";

interface IProps {
    orderProductSum?: IOrderSumModel;
}

const AdminTobaccoTaxReportTotalInfoFeature: FC<IProps> = ({
    orderProductSum,
}) => {
    return (
        <ReportTotalInfoComponent
            items={[
                {
                    text: TranslateConstants.SUB_TOTAL,
                    value: orderProductSum?.subTotal,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.DISCOUNT,
                    value: orderProductSum?.discount,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.TOBACCO_TAX,
                    value: orderProductSum?.tobaccoTax,
                },
                { text: TranslateConstants.TAX, value: orderProductSum?.vat },
                { text: TranslateConstants.TOTAL, value: orderProductSum?.total },
            ]}
        />
    );
};

export default AdminTobaccoTaxReportTotalInfoFeature;
