import { <PERSON>r<PERSON><PERSON><PERSON> } from "../../../../common/helpers/QrHelper";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { PurchaseOrdersPdfPrinterContent } from "../../../../common/pdf-make/slices/purchaseOrders/PurchaseOrdersPdfPrinterContent";
import { IPurchaseOrdersPdfPrinterModel } from "../../../../common/pdf-make/slices/purchaseOrders/PurchaseOrdersReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { fixedNumber } from "../../../../common/utils/numberUtils";

export class AdminPurchaseOrderService {
    static async handlePreview(purchaseOrder: IPurchaseOrderModel) {
        const model: IPurchaseOrdersPdfPrinterModel = {
            items: purchaseOrder.purchaseOrderProducts?.map((el) => ({
                name: el.name,
                quantity: fixedNumber(el.quantity),
                price: fixedNumber(el.price),
                discount: fixedNumber(el.discount),
                subTotal: el.subTotal,
                vat: el.vat,
                total: fixedNumber(el.total),
            })),
            note: purchaseOrder.note,
            totals: {
                nativeTotal: purchaseOrder.nativeTotal,
                productsDiscount: purchaseOrder.productsDiscount,
                subTotal: purchaseOrder.subTotal,
                vat: purchaseOrder.vat,
                total: purchaseOrder.total,
            },
        };

        PdfMakeUtils.preview(PurchaseOrdersPdfPrinterContent(model), {
            headerType: "invoice",
            header: await PdfMakeHeaders.invoice({
                InvoiceNumber: purchaseOrder.number.toString(),
                titleAr: "أمر شراء",
                titleEn: "Purchase Order",
                startDate: DateUtils.format(purchaseOrder.date, "dd/MM/yyyy"),
                endDate: DateUtils.format(purchaseOrder.dueDate, "dd/MM/yyyy"),
                supplierName: purchaseOrder.supplier?.name || "",
                supplierMobile: purchaseOrder.supplier?.mobile || "",
                supplierTaxNumber: purchaseOrder.supplier?.taxNumber || "",
                supplierAddress: purchaseOrder.supplier?.address || "",
                qrCode: QrHelper.vatQrData(purchaseOrder.total, purchaseOrder.vat),
            }),
        });
    }
}
