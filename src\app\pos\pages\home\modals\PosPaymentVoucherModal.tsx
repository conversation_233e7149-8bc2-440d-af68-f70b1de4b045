import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import useCustomState from "../../../../../common/hooks/useCustomState";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminPaymentVoucherValidation } from "../../../../admin/pages/paymentVoucher/AdminPaymentVoucherValidation";
import { IAdminPaymentVoucherInputs } from "../../../../admin/pages/paymentVoucher/AdminPaymentVoucherInterface";
import { AdminPaymentVoucherInputs } from "../../../../admin/pages/paymentVoucher/AdminPaymentVoucherConstants";
import { PaymentVoucherDestinationEnum } from "../../../../../common/enums/DataEnums";
import { PosPaymentVoucherRepo } from "../../../repo/PosPaymentVoucherRepo";
import { FC, useState } from "react";
import { IShiftPdfPrinterBodyModel } from "../../../../../common/pdf-make/slices/shift/ShiftPdfPrinterModel";
import NumpadComponent from "../../../../../common/components/NumpadComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { PosVoucherViewEnum } from "../PosHomeConstants";
import { PosInvoicesService } from "../../invoices/PosInvoicesService";
import useActions from "../../../../../common/redux/data/useActions";

interface IProps {
    shift?: IShiftPdfPrinterBodyModel;
}

const PosPaymentVoucherModal: FC<IProps> = ({ shift }) => {
    const actions = useActions();
    const [view, setView] = useState(PosVoucherViewEnum.NUMPAD);
    const [state, setState] = useCustomState<IAdminPaymentVoucherInputs>({
        ...AdminPaymentVoucherInputs(),
        destination: PaymentVoucherDestinationEnum.pullFromShift,
    });
    const addPaymentVoucher = useFlatMutate(
        PosPaymentVoucherRepo.addPaymentVoucher, {
        showDefaultSuccessToast: true,
        closeModalOnSuccess: true,
        onSuccess: ({ args }) => PosInvoicesService.handleOnPrintPaymentVoucher(actions, args[0]),
    });

    const onClick = () => {
        if (view === PosVoucherViewEnum.NUMPAD) {
            if (!AdminPaymentVoucherValidation.inputsValidation(state, shift?.cashNetAmount)) return;
            setView(PosVoucherViewEnum.STATEMENT);
            return;
        }

        addPaymentVoucher(state);
    };

    return (
        <>
            <form className="px-2">
                {
                    view === PosVoucherViewEnum.NUMPAD && (
                        <NumpadComponent
                            type="fixed"
                            onChange={(cash) => setState({ ...state, cash: Number(cash) })}
                            placeHolder={!shift?.cashNetAmount ? "0" :
                                TranslateHelper.t(TranslateConstants.AVAILABLE_BALANCE) +
                                " " +
                                shift?.cashNetAmount.toString()
                            }
                            placeholderSize={shift?.cashNetAmount ? "text-xl" : undefined}
                        />
                    )
                }
                {
                    view === PosVoucherViewEnum.STATEMENT && (
                        <InputComponent
                            label={TranslateConstants.STATEMENT}
                            type="textarea"
                            value={state.note || ""}
                            onChange={(note) => setState({ ...state, note })}
                            textAreaRows={2}
                        />
                    )
                }
            </form>
            <ModalButtonsComponent
                text={
                    view === PosVoucherViewEnum.NUMPAD
                        ? TranslateConstants.NEXT
                        : TranslateConstants.SAVE
                }
                onClick={onClick}
            />
        </>
    );
};

export default PosPaymentVoucherModal;
