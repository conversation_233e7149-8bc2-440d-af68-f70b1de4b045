import { FC, useMemo } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";
import { IReturnedOrderSumModel } from "../../../../../common/models/ReturnedOrderSumModel";
import { IPaymentVoucherSumModel } from "../../../../../common/models/PaymentVoucherSumModel";

interface IProps {
    orderSum?: IOrderSumModel;
    returnedOrderSum?: IReturnedOrderSumModel;
    receiptVoucherSum?: IPaymentVoucherSumModel;
}

const AdminDeliveryAppsReportTotalInfoFeature: FC<IProps> = ({
    orderSum,
    returnedOrderSum,
    receiptVoucherSum
}) => {
    const data = useMemo(() => {
        const totalReceiptVoucher = (receiptVoucherSum?.cash ?? 0) + (receiptVoucherSum?.network ?? 0);
        return {
            count: orderSum?.count ?? 0,
            returnedCount: returnedOrderSum?.count ?? 0,
            vat: (orderSum?.vat ?? 0) - (returnedOrderSum?.vat ?? 0),
            total: (orderSum?.total ?? 0) - (returnedOrderSum?.total ?? 0),
            deliveryAppFee: (orderSum?.deliveryAppFee ?? 0) - (returnedOrderSum?.deliveryAppFee ?? 0),
            totalDue: (orderSum?.totalDue ?? 0) - (returnedOrderSum?.totalDue ?? 0) - totalReceiptVoucher,
        };
    }, [orderSum, returnedOrderSum, receiptVoucherSum]);

    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.ORDERS_COUNT, value: data?.count, fixedVal: 0 },
                { text: TranslateConstants.RETURNED_ORDERS_COUNT, value: data?.returnedCount, fixedVal: 0 },
                { text: TranslateConstants.TAX, value: data?.vat, isHiddenInSm: true, },
                { text: TranslateConstants.TOTAL, value: data?.total },
                {
                    text: TranslateConstants.RATIO,
                    value: data?.deliveryAppFee,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.DUE,
                    value: data?.totalDue,
                    isHiddenInSm: true,
                },
            ]}
        />
    );
};

export default AdminDeliveryAppsReportTotalInfoFeature;
