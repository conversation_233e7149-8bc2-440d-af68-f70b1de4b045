import { useState } from "react";
import { IAuthRegisterInputs, IAuthRegisterState } from "../AuthInterface";
import { AuthRegisterInitialState } from "../AuthConstants";
import { AuthService } from "../AuthService";
import { useTranslate } from "../../../common/hooks/useTranslate";
import InputComponent from "../../../common/components/InputComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import ButtonComponent from "../../../common/components/ButtonComponent";

const AuthRegisterFormFeature = () => {
    const { translate } = useTranslate()
    const [state, setState] = useState<IAuthRegisterState>(
        AuthRegisterInitialState
    );

    const handleOnInputChange = (
        key: keyof IAuthRegisterInputs,
        value: string
    ) => {
        setState({
            ...state,
            inputs: { ...state.inputs, [key]: value },
        });
    };

    const onSubmit = async () => AuthService.register(state.inputs, setState);

    return (
        <form className="w-full">
            <InputComponent
                type="text"
                placeholder={translate(TranslateConstants.ORGANIZATION_NAME)}
                value={state.inputs.name}
                className="border-b-0 bg-white mb-1"
                onChange={(val) => handleOnInputChange("name", val)}
            />
            <InputComponent
                type="email"
                placeholder={translate(TranslateConstants.EMAIL)}
                value={state.inputs.email}
                className="border-b-0 bg-white mb-1"
                onChange={(val) => handleOnInputChange("email", val)}
            />
            <InputComponent
                type="number"
                placeholder={translate(TranslateConstants.MOBILE)}
                value={state.inputs.mobile}
                className="border-b-0 bg-white mb-1"
                onChange={(val) => handleOnInputChange("mobile", val)}
            />
            <InputComponent
                type="password"
                placeholder={translate(TranslateConstants.PASSWORD)}
                value={state.inputs.password}
                className="bg-white"
                onChange={(val) => handleOnInputChange("password", val)}
            />
            {!!state.error && (
                <p className="text-red-500 text-center w-full mt-1">{translate(state.error)}</p>
            )}
            <ButtonComponent
                text={translate(TranslateConstants.NEW_REGISTER)}
                isLoading={state.loading}
                onClick={onSubmit}
                isDisabled={
                    !state.inputs.name ||
                    !state.inputs.email ||
                    !state.inputs.mobile ||
                    !state.inputs.password
                }
                className="mt-4"
                type="submit"
            />
        </form>
    );
};

export default AuthRegisterFormFeature;
