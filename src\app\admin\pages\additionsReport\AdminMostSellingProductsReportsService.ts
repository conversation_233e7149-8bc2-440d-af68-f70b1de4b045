import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { AdditionsReportPdfPrinterContent } from "../../../../common/pdf-make/slices/additionsReport/AdditionsReportPdfPrinterContent";
import { IAdditionsReportPdfPrinterModel } from "../../../../common/pdf-make/slices/additionsReport/AdditionsReportPdfPrinterModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";

export class AdminAdditionsReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: IMostSellingProductsModel[] | undefined,
    ) {
        const model: IAdditionsReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: data?.map((el) => ({
                name: el.name,
                quantity: fixedNumber(el.quantity),
                discount: fixedNumber(el.discount),
                subTotal: fixedNumber(el.subTotal),
                vat: fixedNumber(el.vat),
                total: fixedNumber(el.total),
            })),
        };

        PdfMakeUtils.download(AdditionsReportPdfPrinterContent(model), "Additions Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleData(
        additionsData: IMostSellingProductsModel[] = [],
        returnedData: IMostSellingProductsModel[] = []
    ): IMostSellingProductsModel[] {
        return additionsData.map((el) => {
            const returned = returnedData.find((el2) => el2.name === el.name);
            return {
                name: el.name,
                price: el.price,
                quantity: el.quantity - (returned?.quantity ?? 0),
                subTotal: el.subTotal - (returned?.subTotal ?? 0),
                discount: el.discount - (returned?.discount ?? 0),
                tobaccoTax: el.tobaccoTax - (returned?.tobaccoTax ?? 0),
                vat: el.vat - (returned?.vat ?? 0),
                total: el.total - (returned?.total ?? 0),
            };

        });
    }
}