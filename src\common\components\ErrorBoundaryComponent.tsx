import { Component, ErrorInfo, ReactNode } from "react";
import RandomBackGroundImageComponent from "./RandomBackGroundImageComponent";
import logo from "/logo.svg";
import { BsTools } from "react-icons/bs";
import { WhatsAppLink } from "../constants/CommonConstants";

interface Props {
    children: ReactNode;
}

interface State {
    hasError: boolean;
    error: Error | null;
}

export class ErrorBoundaryComponent extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, info: ErrorInfo) {
        console.error("ErrorBoundary caught an error:", error, info);
    }

    render() {
        if (this.state.hasError) {
            return (
                <RandomBackGroundImageComponent>
                    <div className="h-s w-screen flex flex-col items-center justify-center gap-6 p-4 text-center">
                        <div className="flex flex-col justify-center items-center">
                            <img src={logo} alt="logo" className="animate-h_spin w-28 mb-2" />
                            <h1 className={`text-2xl text-white`}>الرؤيا الرقمية</h1>
                        </div>
                        <div className="bg-white p-2 py-5 rounded-md flex flex-col items-center justify-center gap-7 w-full sm:w-1/2 lg:w-1/3">
                            <BsTools className="text-7xl text-red-500" />
                            <div className="flex flex-col items-center justify-center gap-1">
                                <h1 className="text-xl font-bold">عفواً حدث خطأ ما</h1>
                                <p className="text-gray-500">يرجى تحديث الصفحة</p>
                                <p className="text-gray-500">
                                    اذا استمرت المشكلة يرجى الاتصال بالدعم الفني
                                </p>
                            </div>
                        </div>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-[#226bb2] text-white px-4 rounded w-full sm:w-1/2 lg:w-1/3 py-4 text-lg"
                        >
                            تحديث الصفحة
                        </button>
                        <p className="text-white flex gap-2">
                            <span>للتواصل مع الدعم الفني</span>
                            <a
                                href={WhatsAppLink(
                                    "مرحباً لدي مشكلة في تطبيق مطعم الرؤيا الرقمية. (حدث خطأ ما)"
                                )}
                                className="text-blue-400 underline"
                                target="_blank"
                            >
                                اضغط هنا
                            </a>
                        </p>
                    </div>
                </RandomBackGroundImageComponent>
            );
        }

        return this.props.children;
    }
}
