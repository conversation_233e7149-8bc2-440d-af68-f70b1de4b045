import { useEffect, useState } from "react";
import AdminPurchaseOrderInputsFeature from "./features/AdminPurchaseOrderInputsFeature";
import AdminPurchaseOrderViewFeature from "./features/AdminPurchaseOrderViewFeature";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";

const AdminPurchaseOrderPage = () => {
    const [isView, setIsView] = useState(true);
    const [selectedItem, setSelectedItem] = useState<
        IPurchaseOrderModel | undefined
    >(undefined);

    useEffect(() => {
        if (isView) setSelectedItem(undefined);
    }, [isView, setSelectedItem]);

    return (
        <>
            {isView && (
                <AdminPurchaseOrderViewFeature
                    setIsView={setIsView}
                    setSelectedItem={setSelectedItem}
                />
            )}
            {!isView && (
                <AdminPurchaseOrderInputsFeature
                    setIsView={setIsView}
                    setSelectedItem={setSelectedItem}
                    selectedItem={selectedItem}
                />
            )}
        </>
    );
};

export default AdminPurchaseOrderPage;
