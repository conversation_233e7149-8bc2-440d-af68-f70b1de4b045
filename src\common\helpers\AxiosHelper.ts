import { AxiosRequestConfig } from "axios";
import api from "../config/axios";
import { EndPointsEnums } from "../enums/EndPointsEnums";
import { IResponse } from "../interfaces";

export class AxiosHelper {
    static async get<T>(
        url: EndPointsEnums,
        config?: AxiosRequestConfig<any> | undefined
    ): Promise<IResponse<T>> {
        try {
            const response = await api.get(url, {
                params: config?.params,
            });
            return response.data as IResponse<T>;
        } catch (error: any) {
            throw error;
        }
    }

    static async post<T>(
        url: EndPointsEnums,
        data: any,
        config?: AxiosRequestConfig<any> | undefined
    ): Promise<IResponse<T>> {
        try {
            const refactorData = data;
            if (typeof refactorData === "object") {
                for (const key in refactorData) {
                    if (refactorData[key] === "") {
                        refactorData[key] = null;
                    }
                }
            }
            const response = await api.post(url, refactorData, config);
            return response.data as IResponse<T>;
        } catch (error: any) {
            throw error;
        }
    }

    static async patch<T>(
        url: EndPointsEnums,
        id: number | undefined,
        data: any,
        config?: AxiosRequestConfig<any> | undefined
    ): Promise<IResponse<T>> {
        try {
            const refactorData = data;
            if (typeof refactorData === "object") {
                for (const key in refactorData) {
                    if (refactorData[key] === "") {
                        refactorData[key] = null;
                    }
                }
            }
            const handleUrl = id ? url + "/" + id : url;
            const response = await api.patch(handleUrl, refactorData, config);
            return { ...response.data } as IResponse<T>;
        } catch (error: any) {
            throw error;
        }
    }

    static async delete<T>(
        url: EndPointsEnums,
        id: number
    ): Promise<IResponse<T>> {
        try {
            const response = await api.delete(url + "/" + id);
            return response.data as IResponse<T>;
        } catch (error: any) {
            throw error;
        }
    }
}
