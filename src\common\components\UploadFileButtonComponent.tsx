import { FC } from "react";
import ButtonComponent, { IButtonComponentProps } from "./ButtonComponent";

export type FileAcceptedTypes =
    | "application/json"
    | "image/*"
    | "video/*"
    | "audio/*"
    | "text/*";

interface ICommonFileProps {
    button: IButtonComponentProps;
    accept?: FileAcceptedTypes | FileAcceptedTypes[];
}

// Props for single file upload
interface ISingleFileProps extends ICommonFileProps {
    onFile?: (file: File | null) => void;
    isMultiple?: false;
}

// Props for multiple file upload
interface IMultipleFileProps extends ICommonFileProps {
    onFile?: (fileList: FileList | null) => void;
    isMultiple: true;
}

type IProps = ISingleFileProps | IMultipleFileProps;

const UploadFileButtonComponent: FC<IProps> = ({
    button,
    onFile,
    accept = "*",
    isMultiple = false,
}) => {
    const triggerFileInput = () => {
        const fileInput = document.createElement("input");
        fileInput.type = "file";
        fileInput.accept = typeof accept === "string" ? accept : accept.join(",");
        fileInput.multiple = isMultiple;
        fileInput.onchange = (e: Event) => {
            const target = e.target as HTMLInputElement;
            const files = target.files;

            if (isMultiple) {
                (onFile as IMultipleFileProps["onFile"])?.(files);
            } else {
                (onFile as ISingleFileProps["onFile"])?.(files?.[0] || null);
            }
        };
        fileInput.click();
    };

    return <ButtonComponent {...button} onClick={triggerFileInput} />;
};

export default UploadFileButtonComponent;
