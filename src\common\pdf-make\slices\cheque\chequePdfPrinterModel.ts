interface IChequeAdditionModel {
    name: string;
    secondName?: string;
    quantity: string;
    price: string;
}

export interface IChequePrinterItemModel {
    quantity: string;
    name: string;
    secondName?: string;
    price: string;
    additions?: IChequeAdditionModel[];
}

export interface IChequePdfPrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    invoiceNumber: string;
    orderNumber: string;
    items: IChequePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
    invoiceDate: string;
}