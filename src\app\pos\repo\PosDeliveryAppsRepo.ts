import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { DeliveryAppsHelper } from "../../../common/helpers/DeliveryAppsHelper";
import { IDeliveryAppModel } from "../../../common/models/DeliveryAppModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosDeliveryAppsRepo {
    static async getAndCacheDeliveryApps() {
        try {
            const res = await AxiosHelper.get<IDeliveryAppModel>(
                EndPointsEnums.DELIVERY_APPS,
            );

            if (!res.success) throw new Error(res.message);
            if (res.data) DeliveryAppsHelper.setDeliveryApps(res.data);
        } catch (error) {
            debug(`PosDeliveryAppsRepo [getAndCacheDeliveryApps] Error: `, error);
            throw error;
        }
    }
}
