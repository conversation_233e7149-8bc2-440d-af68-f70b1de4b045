import { Content } from "pdfmake/interfaces";
import { IBankReportPdfPrinterModel } from "./BankReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const BankReportPdfPrinterContent = (model: IBankReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "كشف حساب بنكي", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: "*",
                body: [
                    [
                        { text: "إجمالي البنك", style: "tableHeader" },
                        { text: "نوع العملية", style: "tableHeader" },
                        { text: "رقم العملية", style: "tableHeader" },
                        { text: "التاريخ", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.bank,
                        PdfMakeHelper.reverseTextIfArabic(el.type),
                        el.number,
                        el.date,
                    ]),
                ],
            },
        },
        {
            marginTop: 40,
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { text: model.totals.ordersCount.toString(), margin: [0, 3, 0, 0] },
                                { text: "عدد المبيعات:", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { text: model.totals.returnedCount.toString(), margin: [0, 3, 0, 0] },
                                { text: "عدد المرتجعات: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { text: model.totals.paymentVoucherCount.toString(), margin: [0, 3, 0, 0] },
                                { text: "عدد سندات الصرف: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { text: model.totals.receiptVoucherCount.toString(), margin: [0, 3, 0, 0] },
                                { text: "عدد سندات القبض: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { svg: RsSvg("#1F618D"), width: 14 },
                                { text: model.totals.total.toFixed(2), margin: [0, 3, 0, 0] },
                                { text: "الإجمالي: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
    ];
}
