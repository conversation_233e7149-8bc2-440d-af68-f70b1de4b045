import { FC } from "react";
import DividerComponent from "../../../../../common/components/DividerComponent";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import ImageComponent from "../../../../../common/components/ImageComponent";
import { LuPlus, LuMinus } from "react-icons/lu";
import { LuTrash2 } from "react-icons/lu";

interface IProps {
    image?: string;
    showImage?: boolean;
    name: string;
    quantity: number;
    oldQuantity: number;
    className?: string;
    onAdd?: () => void;
    onSubtract?: () => void;
    onRemove?: () => void;
}

const PosInvoicesReturnedItemComponent: FC<IProps> = ({
    image,
    showImage = true,
    name,
    quantity,
    oldQuantity,
    className,
    onAdd,
    onSubtract,
    onRemove,
}) => {
    return (
        <div
            className={
                "flex justify-between items-center border p-1 rounded border-gray-400 w-full" +
                " " +
                (quantity === oldQuantity ? "bg-gray-100" : "") +
                " " +
                className
            }
        >
            <div className="flex items-center gap-1 font-tajawal-bold">
                {showImage && (
                    <ImageComponent
                        src={image}
                        className="w-10 h-10 rounded"
                        onErrorClassName="w-10 h-10 rounded"
                    />
                )}
                <span>{name}</span>
            </div>
            <div className="flex gap-1">
                <IconButtonComponent
                    icon={<LuPlus />}
                    bgColor="primary"
                    className="!w-12 !h-10"
                    onClick={onAdd}
                    isDisabled={quantity === oldQuantity}
                />
                <div className="flex items-center justify-center w-14 border border-gray-400 rounded">
                    <span className="font-tajawal-bold text-center">{oldQuantity} / {quantity}</span>
                </div>
                <IconButtonComponent
                    icon={<LuMinus />}
                    bgColor="primary"
                    className="!w-12 !h-10"
                    onClick={onSubtract}
                    isDisabled={quantity === 0}
                />
                <DividerComponent isVertical={true} className="!h-10" />
                <IconButtonComponent
                    icon={<LuTrash2 />}
                    bgColor="red"
                    className="!w-12 !h-10"
                    onClick={onRemove}
                    isDisabled={quantity === oldQuantity}
                />
            </div>
        </div>
    );
};

export default PosInvoicesReturnedItemComponent;
