import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { ICustomersReportTotalInfoInterface } from "../AdminCustomersReportInterface";

interface IProps {
    totals: ICustomersReportTotalInfoInterface;
}

const AdminCustomersReportTotalInfoFeature: FC<IProps> = ({ totals }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                {
                    text: TranslateConstants.TOTAL,
                    value: totals?.total,
                },
                {
                    text: TranslateConstants.TOTAL_RETURNED,
                    value: totals?.totalReturned,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.CASH,
                    value: totals?.cash,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.NETWORK,
                    value: totals?.network,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.PAID,
                    value: totals?.pay,
                },
                {
                    text: TranslateConstants.CREDIT,
                    value: totals?.credit,
                },
            ]}
        />
    );
};

export default AdminCustomersReportTotalInfoFeature;
