import { FC, useMemo } from "react";
import { ITaxReportTotalInfoInterface } from "../TaxReportInterface";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { fixedNumber } from "../../../../../common/utils/numberUtils";
import MultiColumnsDataViewerComponent, {
    IMultiColumnsDataViewerComponentItem,
} from "../../../../../common/components/MultiColumnsDataViewerComponent";

interface IProps {
    data: ITaxReportTotalInfoInterface;
}

const AdminTaxReportDataFeature: FC<IProps> = ({ data }) => {
    const formatData: IMultiColumnsDataViewerComponentItem[] = useMemo(() => {
        return [
            {
                title: TranslateHelper.t(TranslateConstants.SALES),
                header: [
                    TranslateHelper.t(TranslateConstants.SALES_SUB_TOTAL),
                    TranslateHelper.t(TranslateConstants.TOTAL_RETURNED_SALES),
                    TranslateHelper.t(TranslateConstants.SALES_NET),
                    TranslateHelper.t(TranslateConstants.SALES_TAX),
                ],
                values: [
                    data.ordersSubTotal,
                    data.returnedOrdersSubTotal,
                    data.ordersNet,
                    data.orderTax,
                ],
            },
            {
                title: TranslateHelper.t(TranslateConstants.PURCHASES),
                header: [
                    TranslateHelper.t(TranslateConstants.PURCHASES_SUB_TOTAL),
                    TranslateHelper.t(
                        TranslateConstants.TOTAL_RETURNED_PURCHASE_INVOICES
                    ),
                    TranslateHelper.t(TranslateConstants.PURCHASES_NET),
                    TranslateHelper.t(TranslateConstants.PURCHASES_TAX),
                ],
                values: [
                    data.purchaseInvoicesSubTotal,
                    data.returnedPurchaseInvoicesSubTotal,
                    data.purchaseInvoicesNet,
                    data.purchaseInvoicesTax,
                ],
            },
            {
                title: TranslateHelper.t(TranslateConstants.TAX_DUE),
                header: [TranslateHelper.t(TranslateConstants.TAX_NET)],
                values: [fixedNumber(data.orderTax - data.purchaseInvoicesTax, 3)],
                isFullWidth: true,
                className: "sm:w-1/2 sm:mx-auto sm:mt-5",
            },
        ];
    }, [data]);

    return <MultiColumnsDataViewerComponent items={formatData} />;
};

export default AdminTaxReportDataFeature;
