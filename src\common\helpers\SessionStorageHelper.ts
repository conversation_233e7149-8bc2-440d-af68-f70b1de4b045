export class SessionStorageHelper {
    static set(key: string, value: string) {
        sessionStorage.setItem(key, value);
    }

    static get(key: string) {
        return sessionStorage.getItem(key);
    }

    static has(key: string) {
        return sessionStorage.getItem(key) !== null;
    }

    static remove(key: string | string[]) {
        if (Array.isArray(key)) {
            key.forEach((k) => {
                sessionStorage.removeItem(k);
            });
            return;
        }
        sessionStorage.removeItem(key);
    }

    static clear() {
        sessionStorage.clear();
    }
}