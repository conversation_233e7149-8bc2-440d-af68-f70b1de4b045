import { FC } from "react";
import { IAdminTableInputs } from "../AdminTablesInterface";
import { AdminTableInputs } from "../AdminTablesConstants";
import { ITableModel } from "../../../../../common/models/TableModel";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import useCustomState from "../../../../../common/hooks/useCustomState";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { TablesApiRepo } from "../../../../../common/repos/api/TablesApiRepo";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { AdminTablesValidation } from "../AdminTablesValidation";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";

interface IProps {
    isEdit?: boolean;
    item?: ITableModel;
}

const AdminTablesModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminTableInputs>(
        AdminTableInputs,
        { updateState: item }
    );
    const addTable = useFlatMutate(TablesApiRepo.addTable, {
        updateCached: { key: EndPointsEnums.TABLES, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateTable = useFlatMutate(TablesApiRepo.updateTable, {
        updateCached: {
            key: EndPointsEnums.TABLES,
            operation: "update",
            selector: (data: ITableModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminTablesValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateTable(item.id, state);
        addTable(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(value) => setState({ ...state, name: value })}
                    value={state.name}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminTablesModal;
