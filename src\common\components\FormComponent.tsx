import { FC } from "react";

interface IProps {
    children?: React.ReactNode;
    height?: number;
}

const FormComponent: FC<IProps> = ({ children, height = 136 }) => {
    return (
        <form
            className={
                "flex flex-col gap-2 sm:gap-4 border px-3 py-2 rounded-md bg-base-100 border-slate-400"
            }
            style={{ height: `calc(100svh - ${height}px)` }}
        >
            {children}
        </form>
    );
};

export default FormComponent;
