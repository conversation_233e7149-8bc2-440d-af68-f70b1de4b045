export interface IShiftPdfPrinterBodyModel {
    startTime: string;
    endTime: string;
    startAmount: number;
    endAmount: number;
    ordersCount: number;
    returnedOrdersCount: number;
    totalAmount: number;
    totalReturnedAmount: number;
    totalNetAmount: number; // the totalAmount - totalReturnedAmount
    discountAmount: number;
    vatAmount: number;
    additionAmount: number;
    shortageAmount: number;
    cashAmount: number;
    networkAmount: number;
    deferredAmount: number;
    addedToShiftAmount: number;
    customerReceiptsAmount: number;
    pullsAmount: number;
    cashNetAmount: number;
    networkNetAmount: number;
    paymentAmounts: {
        name: string;
        amount: number;
        nameEn: string;
    }[]
}