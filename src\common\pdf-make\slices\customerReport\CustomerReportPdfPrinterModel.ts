export interface ICustomerReportPdfPrinterItemModel {
    date: string;
    number: string;
    operationType: string;
    orderType: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    cash: number;
    network: number;
    credit: number;
}

export interface ICustomerReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: ICustomerReportPdfPrinterItemModel[];
    totals: {
        count: number;
        returnedCount: number;
        vat: number;
        total: number;
        totalReturned: number;
        cash: number;
        network: number;
        pay: number;
        credit: number;
    };
    customer: {
        name: string;
        mobile: string;
        credit: string;
        taxNumber?: string;
    }
}