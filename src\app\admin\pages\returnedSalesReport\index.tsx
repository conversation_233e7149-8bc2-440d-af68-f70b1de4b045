import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminReturnedSalesReportTableHeaders } from "./AdminReturnedSalesReportsConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminReturnedSalesReportTotalInfoFeature from "./features/AdminReturnedSalesReportTotalInfoFeature";
import { AdminReturnedSalesReportService } from "./ReturnedSalesReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";

const AdminReturnedSalesReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
        ) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime),
        { autoFetchOnMount: true }
    );

    const {
        data: returnedOrders,
        isLoading: returnedOrdersLoading,
        isError: returnedOrdersError,
        refetch: refetchReturnedOrders,
    } = useFetch(
        "report-" + EndPointsEnums.RETURNED_ORDERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
        ) => ReturnedOrdersApiRepo.getReturnedOrders(startTime, endTime),
        { autoFetchOnMount: true }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchReturnedOrderSum(start, end);
        refetchReturnedOrders(start, end);
    };

    const onDownload = () =>
        AdminReturnedSalesReportService.handleDownload(dateRange, returnedOrders, returnedOrderSum);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!returnedOrders?.length || !returnedOrderSum}
            />
            <AdminReturnedSalesReportTotalInfoFeature returnedOrderSum={returnedOrderSum} />
            <StatusComponent
                isEmpty={!returnedOrders?.length}
                isLoading={returnedOrderSumLoading || returnedOrdersLoading}
                isError={returnedOrderSumError || returnedOrdersError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminReturnedSalesReportTableHeaders}
                    items={returnedOrders || []}
                    selectors={(item: IReturnedOrderModel) => [
                        item.returnedInvoiceNumber,
                        item.invoiceNumber,
                        TranslateHelper.t(item.deliveryApp ?? item.type),
                        item.total.toFixed(2),
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        ((item.deferred ?? 0) + (item.deliveryApp ? item.total : 0)).toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminReturnedSalesReportPage;
