import { isDesktop } from "react-device-detect";

export class FullScreenHelper {
    static start(
        isDesktopOnly: boolean = true
    ) {
        if (isDesktopOnly && !isDesktop) return;

        const elem: any = document.documentElement; // Fullscreen for the entire document
        if (elem.requestFullscreen) {
            elem.requestFullscreen();
        } else if (elem.mozRequestFullScreen) { // Firefox
            elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullscreen) { // Chrome, Safari and Opera
            elem.webkitRequestFullscreen();
        }
    }

    static exit(
        isDesktopOnly: boolean = true
    ) {
        if (isDesktopOnly && !isDesktop) return;

        const d: any = document;
        if (d.exitFullscreen) {
            d.exitFullscreen();
        } else if (d.mozCancelFullScreen) { // Firefox
            d.mozCancelFullScreen();
        } else if (d.webkitExitFullscreen) { // Chrome, Safari and Opera
            d.webkitExitFullscreen();
        }
    }

    static isFullScreen() {
        const d: any = document;
        return d.fullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || d.msFullscreenElement;
    }

    static toggle(
        isDesktopOnly: boolean = true
    ) {
        if (isDesktopOnly && !isDesktop) return;

        if (this.isFullScreen()) {
            this.exit();
        } else {
            this.start();
        }
    }
}
