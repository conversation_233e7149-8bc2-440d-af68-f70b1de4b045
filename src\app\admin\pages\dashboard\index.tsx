import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Filler,
    BarElement,
    ArcElement,
} from "chart.js";
import AdminDashboardTotalInoFeature from "./features/AdminDashboardTotalInoFeature";
import AdminDashboardOperationsSummaryFeature from "./features/AdminDashboardOperationsSummaryFeature";
import AdminDashboardRushHoursFeature from "./features/AdminDashboardRushHoursFeature";
import AdminDashboardMostSellingFeature from "./features/AdminDashboardMostSellingFeature";
import AdminDashboardPaymentMethodsFeature from "./features/AdminDashboardPaymentMethodsFeature";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { FiPrinter } from "react-icons/fi";
import { useMemo, useRef } from "react";
import { DateUtils } from "../../../../common/utils/DateUtils";
import useFetch from "../../../../common/asyncController/useFetch";
import { PurchaseInvoiceApiRepo } from "../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import { IPaymentVoucherDestinationSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { OrderProductsApiRepo } from "../../../../common/repos/api/OrderProductsApi";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { AdminProfitAndLossReportReportService } from "../profitAndLossReport/ProfitAndLossReportService";
import { ReceiptVouchersApiRepo } from "../../../../common/repos/api/ReceiptVouchersApiRepo";
import { useReactToPrint } from "react-to-print";
import PrintWrapperComponent from "../../../../common/components/PrintWrapperComponent";
import { useTranslate } from "../../../../common/hooks/useTranslate";

ChartJS.register(
    CategoryScale,
    ArcElement,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Filler,
    
);

const AdminDashboardPage = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
    const { isXs, isSm } = useScreenSize();
    const { isRtl } = useTranslate();
    const componentRef = useRef<HTMLDivElement>(null);
    const handlePrint = useReactToPrint({
        content: () => componentRef.current,
        removeAfterPrint: true,
        documentTitle: "Dashboard",
        pageStyle: `
            @media print {
                @page {
                    size: landscape;
                    margin: .5cm;
                }
            }
        `,
    });

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "dashboard-" + EndPointsEnums.ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrdersApiRepo.getSum(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "dashboard-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const {
        data: purchaseInvoiceSum,
        isLoading: purchaseInvoiceSumLoading,
        isError: purchaseInvoiceSumError,
        refetch: refetchPurchaseInvoicePaymentsSum,
    } = useFetch(
        "dashboard-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const {
        data: returnedPurchaseInvoicePaymentsSum,
        isLoading: returnedPurchaseInvoicePaymentsSumLoading,
        isError: returnedPurchaseInvoicePaymentsSumError,
        refetch: refetchReturnedPurchaseInvoicePaymentsSum,
    } = useFetch(
        "dashboard-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedPurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const {
        data: paymentVouchersSum,
        isLoading: paymentVouchersSumLoading,
        isError: paymentVouchersSumError,
        refetch: refetchPaymentVouchersSum,
    } = useFetch(
        "dashboard-" + EndPointsEnums.PAYMENT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) =>
            PaymentVouchersApiRepo.getSum<IPaymentVoucherDestinationSumModel[]>(
                startTime,
                endTime,
                undefined,
                undefined,
                undefined,
                true
            ),
        { autoFetchIfEmpty: true }
    );

    const {
        data: receiptVoucherSum,
        isLoading: receiptVoucherSumLoading,
        isError: receiptVoucherSumError,
        refetch: refetchReceiptVoucherSum,
    } = useFetch(
        "report-customers-" + EndPointsEnums.RECEIPT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReceiptVouchersApiRepo.getSum(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const {
        data: ordersProducts,
        isLoading: ordersProductsLoading,
        isError: ordersProductsError,
        refetch: refetchOrderProducts,
    } = useFetch(
        "dashboard-" + EndPointsEnums.ORDER_PRODUCTS_SUM_MANY,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrderProductsApiRepo.getSumMany(startTime, endTime, hasTobaccoTax, 5, true),
        { autoFetchIfEmpty: true }
    );

    const {
        data: ordersRushHours,
        isLoading: ordersRushHoursLoading,
        isError: ordersRushHoursError,
        refetch: refetchOrdersRushHours,
    } = useFetch(
        "dashboard-" + EndPointsEnums.ORDERS_RUSH_HOURS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrderProductsApiRepo.getRushHours(startTime, endTime),
        { autoFetchIfEmpty: true }
    );

    const isLoading = useMemo(
        () =>
            orderSumLoading ||
            purchaseInvoiceSumLoading ||
            ordersProductsLoading ||
            returnedOrderSumLoading ||
            returnedPurchaseInvoicePaymentsSumLoading ||
            paymentVouchersSumLoading ||
            receiptVoucherSumLoading ||
            ordersRushHoursLoading,
        [
            orderSumLoading,
            purchaseInvoiceSumLoading,
            ordersProductsLoading,
            returnedOrderSumLoading,
            returnedPurchaseInvoicePaymentsSumLoading,
            paymentVouchersSumLoading,
            receiptVoucherSumLoading,
            ordersRushHoursLoading,
        ]
    );
    const isError = useMemo(
        () =>
            orderSumError ||
            purchaseInvoiceSumError ||
            ordersProductsError ||
            returnedOrderSumError ||
            returnedPurchaseInvoicePaymentsSumError ||
            paymentVouchersSumError ||
            receiptVoucherSumError ||
            ordersRushHoursError,
        [
            orderSumError,
            purchaseInvoiceSumError,
            ordersProductsError,
            returnedOrderSumError,
            returnedPurchaseInvoicePaymentsSumError,
            paymentVouchersSumError,
            receiptVoucherSumError,
            ordersRushHoursError,
        ]
    );
    const isEmpty = useMemo(
        () =>
            !orderSum?.total &&
            !purchaseInvoiceSum?.total &&
            !ordersProducts?.length &&
            !returnedOrderSum?.total &&
            !returnedPurchaseInvoicePaymentsSum?.total &&
            !paymentVouchersSum?.length &&
            !receiptVoucherSum?.total &&
            !ordersRushHours?.length,
        [
            orderSum,
            purchaseInvoiceSum,
            ordersProducts,
            returnedOrderSum,
            returnedPurchaseInvoicePaymentsSum,
            paymentVouchersSum,
            receiptVoucherSum,
            ordersRushHours,
        ]
    );
    const totals = useMemo(
        () =>
            AdminProfitAndLossReportReportService.handleTotalInfo(
                orderSum,
                returnedOrderSum,
                purchaseInvoiceSum,
                returnedPurchaseInvoicePaymentsSum,
                paymentVouchersSum
            ),
        [
            orderSum,
            returnedOrderSum,
            purchaseInvoiceSum,
            returnedPurchaseInvoicePaymentsSum,
            paymentVouchersSum,
        ]
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        refetchOrderSum(start, end);
        refetchReturnedOrderSum(start, end);
        refetchPurchaseInvoicePaymentsSum(start, end);
        refetchReturnedPurchaseInvoicePaymentsSum(start, end);
        refetchPaymentVouchersSum(start, end);
        refetchOrderProducts(start, end, hasTobaccoTax, 5);
        refetchReceiptVoucherSum(start, end);
        refetchOrdersRushHours(start, end);
    };


    return (
        <>
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={handlePrint}
                isDownloadDisabled={isEmpty}
                buttonText={TranslateConstants.PRINT}
                buttonIcon={<FiPrinter />}
            />
            <StatusComponent
                isTransparent={!isLoading && !isError}
                isLoading={isLoading}
                isError={isError}
                height={isXs ? 11 : isSm ? 7.4 : 9}
                showLogoLoading={true}
                className="overflow-y-auto"
            >
                <PrintWrapperComponent
                    ref={componentRef}
                    className="h-full flex flex-col gap-2"
                >
                    <AdminDashboardTotalInoFeature
                        orderSum={orderSum}
                        returnedOrderSum={returnedOrderSum}
                        data={totals}
                        receiptVoucherSum={receiptVoucherSum}
                    />
                    <div
                        className={
                            "grid grid-cols-1 md:grid-cols-2 print:grid-cols-2 gap-2 flex-1" +
                            " " +
                            (isRtl ? "print:[direction:rtl]" : "")
                        }
                    >
                        <div className="row-start-4 md:row-start-1 print:row-start-1">
                            <AdminDashboardOperationsSummaryFeature
                                orderTotal={totals.salesNet ?? 0}
                                purchaseInvoicesTotal={totals.purchasesNet ?? 0}
                            />
                        </div>
                        <AdminDashboardRushHoursFeature
                            ordersRushHours={[...(ordersRushHours || [])].reverse()}
                        />
                        <AdminDashboardMostSellingFeature
                            mostSellingProducts={ordersProducts}
                        />
                        <div className="row-start-1 md:row-start-2 print:row-start-2">
                            <AdminDashboardPaymentMethodsFeature
                                orderSum={orderSum}
                                returnedOrderSum={returnedOrderSum}
                            />
                        </div>
                    </div>
                </PrintWrapperComponent>
            </StatusComponent>
        </>
    );
};

export default AdminDashboardPage;
