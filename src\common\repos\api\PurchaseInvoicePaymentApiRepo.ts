import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IPurchaseInvoicePaymentModel } from "../../models/PurchaseInvoicePaymentModel";
import { IPurchaseInvoiceSumModel } from "../../models/PurchaseInvoiceSumModel";
import { debug } from "../../utils/CommonUtils";

export class PurchaseInvoicePaymentApiRepo {
    static async getPurchaseInvoicePayments(options?: {
        startTime?: number;
        endTime?: number;
        supplierId?: number;
        isCash?: boolean;
        isBank?: boolean;
    }) {
        try {
            const res = await AxiosHelper.get<IPurchaseInvoicePaymentModel[]>(
                EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
                {
                    params: {
                        startTime: options?.startTime,
                        endTime: options?.endTime,
                        supplierId: options?.supplierId,
                        isCash: options?.isCash,
                        isBank: options?.isBank,
                    },
                }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoicePaymentApiRepo [getPurchaseInvoicePayments] Error: `, error);
            throw error;
        }
    }

    static async getSum(
        startTime: number,
        endTime: number,
        supplierId?: number,
        isCash?: boolean,
        isBank?: boolean,
    ) {
        try {
            const res = await AxiosHelper.get<IPurchaseInvoiceSumModel>(
                EndPointsEnums.PURCHASE_INVOICE_PAYMENTS_SUM,
                { params: { startTime, endTime, supplierId, isCash, isBank } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoicePaymentApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}