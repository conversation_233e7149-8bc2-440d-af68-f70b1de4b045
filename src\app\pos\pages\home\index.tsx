import { useEffect } from "react";
import EmptyComponent from "../../../../common/components/EmptyComponent";
import ListComponent from "../../../../common/components/ListComponent";
import { useAppSelector } from "../../../../common/redux/store";
import {
    posCategoriesSelector,
    posHomeSelector,
    posOrderSelector,
} from "../../redux/selector";
import PosHomeBackButtonFeature from "./features/PosHomeBackButtonFeature";
import PosHomeHeaderFeature from "./features/PosHomeHeaderFeature";
import PosHomeProductsFeature from "./features/PosHomeProductsFeature";
import PosHomeCategoriesFeature from "./features/PosHomeCategoriesFeature";
import PosHomeTablesFeature from "./features/PosHomeTablesFeature";
import usePosActions from "../../redux/usePosActions";
import { PosHomeService } from "./PosHomeService";
import { PosOrderService } from "../../containers/order/PosOrderService";
import PosHomeDeliveryAppsFeature from "./features/PosHomeDeliveryAppsFeature";

const PosHomePage = () => {
    const categories = useAppSelector(posCategoriesSelector);
    const order = useAppSelector(posOrderSelector);
    const { view, selectedCategory } = useAppSelector(posHomeSelector);
    const posActions = usePosActions();

    useEffect(() => {
        return () => PosOrderService.resetOrder(posActions);
    }, []);

    useEffect(() => {
        PosHomeService.handleHomeViewForOrderType(order, posActions);
    }, [order.type]);

    return (
        <div className="flex flex-col">
            <PosHomeHeaderFeature />
            {!categories.length && (
                <div style={{ height: `calc(100svh - 9rem)` }}>
                    <EmptyComponent />
                </div>
            )}
            {!!categories.length && (
                <ListComponent
                    cols={5}
                    calcHeight={9}
                    allowScrollBar={false}
                    padding="0 p-2"
                >
                    <PosHomeBackButtonFeature view={view} order={order} />
                    {view === "tables" && <PosHomeTablesFeature />}
                    {view === "categories" && <PosHomeCategoriesFeature categories={categories} order={order} />}
                    {view === "products" && <PosHomeProductsFeature selectedCategory={selectedCategory} order={order} />}
                    {view === "delivery_apps" && <PosHomeDeliveryAppsFeature />}
                </ListComponent>
            )}
        </div>
    );
};

export default PosHomePage;
