import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IReturnedPurchaseInvoiceSumModel } from "../../../../../common/models/ReturnedPurchaseInvoiceSumModel";

interface IProps {
    returnedPurchaseInvoicesSum?: IReturnedPurchaseInvoiceSumModel;
}

const AdminReturnedPurchasesReportTotalInfoFeature: FC<IProps> = ({ returnedPurchaseInvoicesSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.RETURNED_ORDERS_COUNT, value: returnedPurchaseInvoicesSum?.count, isHiddenInSm: true },
                { text: TranslateConstants.SUB_TOTAL, value: returnedPurchaseInvoicesSum?.subTotal },
                { text: TranslateConstants.TAX, value: returnedPurchaseInvoicesSum?.vat },
                { text: TranslateConstants.TOTAL, value: returnedPurchaseInvoicesSum?.total },
            ]}
        />
    );
};

export default AdminReturnedPurchasesReportTotalInfoFeature;
