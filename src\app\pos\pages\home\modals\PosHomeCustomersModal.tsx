import { useEffect, useState } from "react";
import NumpadComponent from "../../../../../common/components/NumpadComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useActions from "../../../../../common/redux/data/useActions";
import InputComponent from "../../../../../common/components/InputComponent";
import { PosCustomersRepo } from "../../../repo/PosCustomersRepo";
import useFetch from "../../../../../common/asyncController/useFetch";
import { APP_LOCAL_DB_COLLECTIONS } from "../../../../../common/config/localDB";
import { IAdminCustomerInputs } from "../../../../admin/pages/customers/AdminCustomersInterface";
import { AdminCustomerInputs } from "../../../../admin/pages/customers/AdminCustomersConstants";
import usePosActions from "../../../redux/usePosActions";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { ToastHelper } from "../../../../../common/helpers/ToastHelper";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { CustomersApiRepo } from "../../../../../common/repos/api/CustomerApiRepo";
import useOnlineStatus from "../../../../../common/hooks/useOnlineStatus";
import { posOrderSelector } from "../../../redux/selector";
import { useAppSelector } from "../../../../../common/redux/store";
import PosReceiptVoucherModal from "./PosReceiptVoucherModal";
import { GiMoneyStack } from "react-icons/gi";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { RiErrorWarningLine } from "react-icons/ri";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { GoPlus } from "react-icons/go";

const PosHomeCustomersModal = () => {
    const actions = useActions();
    const posActions = usePosActions();
    const [view, setView] = useState<"numpad" | "add" | "credit">("numpad");
    const [inputs, setInputs] = useState<IAdminCustomerInputs>(AdminCustomerInputs);
    const isOnline = useOnlineStatus();
    const order = useAppSelector(posOrderSelector);

    const mutate = useFlatMutate(
        CustomersApiRepo.addCustomer,
        {
            closeModalOnSuccess: true,
            onSuccess: async ({ data: customer }) => {
                await PosCustomersRepo.addCustomer(customer);
                posActions.setOrder({ customer });
            },
        }
    )

    const { refetch } = useFetch(
        "pos-search-" + APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
        PosCustomersRepo.searchCustomer,
        {
            autoFetchIfEmpty: false,
            onSuccess: ({ data: customer }) => {
                if (!customer) setView("add");
                else {
                    if (!customer.active) {
                        ToastHelper.error(TranslateConstants.CUSTOMER_INACTIVE);
                        return;
                    }
                    posActions.setOrder({ customer });
                    if (customer.credit) setView("credit");
                    else actions.closeModal();
                }
            },
        }
    );

    useEffect(() => {
        actions.openModal({
            size: view === "add" ? "lg" : "sm",
            title: view === "add"
                ? TranslateHelper.t(TranslateConstants.ADD_CUSTOMER)
                : "",
            showButtons: view === 'credit',
        });
    }, [view, order.customer]);

    const handlePay = () => {
        actions.openModal({
            component: <PosReceiptVoucherModal customer={order.customer} />,
            showButtons: false,
            title: TranslateHelper.t(TranslateConstants.CUSTOMER) + " (" + (order.customer?.name || "") + ")",
        });
    };

    return (
        <>
            {
                view === "credit" && (
                    <>
                        <div className="flex flex-col items-center font-tajawal-bold mb-2">
                            <RiErrorWarningLine className="text-9xl text-red-400 mx-auto" />
                            <div>
                                {TranslateHelper.t(TranslateConstants.CUSTOMER) + " (" + (order.customer?.name || "") + ")"}
                            </div>
                        </div>
                        <DividerComponent className="mb-2" />
                        <div className="flex px-2 gap-2">
                            <IconButtonComponent
                                icon={<GiMoneyStack className="text-3xl" />}
                                text={TranslateHelper.t(TranslateConstants.PAY) + " (" + (order.customer?.credit || 0) + ")"}
                                bgColor="transparent"
                                borderColor="slate-600"
                                textColor="black"
                                onClick={handlePay}
                                className="py-4"
                            />
                            <IconButtonComponent
                                icon={<GoPlus className="text-3xl" />}
                                text={TranslateConstants.NEW_ORDER}
                                bgColor="transparent"
                                borderColor="slate-600"
                                textColor="black"
                                onClick={() => actions.closeModal()}
                                className="py-4"
                            />
                        </div>
                    </>
                )
            }
            {view === "numpad" && (
                <div className="flex flex-col gap-2 p-2 bg-base-200 rounded">
                    <NumpadComponent
                        type="text"
                        allowDot={false}
                        title={TranslateHelper.t(TranslateConstants.CUSTOMERS)}
                        button={{
                            text: TranslateConstants.ADD,
                            onClick: (val) => refetch(val.toString()),
                        }}
                        showCancelButton={true}
                        allowZeroAtFirst={true}
                        placeHolder={TranslateHelper.t(
                            TranslateConstants.SEARCH_WITH_MOBILE
                        )}
                        onCancelButtonClick={() => actions.closeModal()}
                        padding="p-0"
                        placeholderSize="text-xl"
                        disableResultInput={false}
                        onChange={(mobile) => setInputs({ ...inputs, mobile })}
                    />
                </div>
            )}
            {view === "add" && (
                <>
                    <div className="flex flex-col gap-2 px-2">
                        {
                            !isOnline && (
                                <p className="text-red-400 text-center">
                                    {TranslateHelper.t(TranslateConstants.NO_INTERNET_CONNECTION)}
                                </p>
                            )
                        }
                        <InputComponent
                            label={TranslateConstants.NAME}
                            className="!h-12"
                            value={inputs.name}
                            onChange={(name) => setInputs({ ...inputs, name })}
                        />
                        <InputComponent
                            type="number"
                            label={TranslateConstants.MOBILE}
                            className="!h-12"
                            value={inputs.mobile}
                            onChange={(mobile) => setInputs({ ...inputs, mobile })}
                        />
                        <InputComponent
                            type="number"
                            label={TranslateConstants.TAX_NUMBER}
                            className="!h-12"
                            value={inputs.taxNumber}
                            onChange={(taxNumber) => setInputs({ ...inputs, taxNumber })}
                        />
                        <InputComponent
                            label={TranslateConstants.ADDRESS}
                            className="!h-12"
                            value={inputs.address}
                            onChange={(address) => setInputs({ ...inputs, address })}
                        />
                    </div>
                    <ModalButtonsComponent
                        text={TranslateConstants.ADD}
                        onClick={() => mutate(inputs, false)}
                        isDisabled={!inputs.name || !inputs.mobile || !isOnline}
                    />
                </>
            )}
        </>
    );
};

export default PosHomeCustomersModal;
