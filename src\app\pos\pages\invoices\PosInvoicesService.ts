import { UpdateFetch } from "../../../../common/asyncController/updateFetch";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { CachedKeysEnums } from "../../../../common/enums/CachedKeysEnums";
import { OrderStatusEnum } from "../../../../common/enums/DataEnums";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { PrintersHelper } from "../../../../common/helpers/PrintersHelper";
import { ReturnedOrderHelper } from "../../../../common/helpers/ReturnedOrderHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { IPosReturnedOrderModel } from "../../../../common/models/PosReturnedOrderModel";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { PosPaymentVoucherPdfPrinterContent } from "../../../../common/pdf-make/slices/posPaymentVoucher/PosPaymentVoucherPdfPrinterContent";
import { IPosPaymentVoucherPdfPrinterBodyModel } from "../../../../common/pdf-make/slices/posPaymentVoucher/PosPaymentVoucherPdfPrinterModel";
import { PosReceiptVoucherPdfPrinterContent } from "../../../../common/pdf-make/slices/posReceiptVoucher/PosReceiptVoucherPdfPrinterContent";
import { IPosReceiptVoucherPdfPrinterBodyModel } from "../../../../common/pdf-make/slices/posReceiptVoucher/PosReceiptVoucherPdfPrinterModel";
import { IActions } from "../../../../common/redux/data/useActions";
import { debug } from "../../../../common/utils/CommonUtils";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { PosOrderService } from "../../containers/order/PosOrderService";
import { PosOrderUtils } from "../../containers/order/PosOrderUtils";
import { IPosActions } from "../../redux/usePosActions";
import { PosCustomersRepo } from "../../repo/PosCustomersRepo";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";
import { PosReturnedOrdersRepo } from "../../repo/PosReturnedOrdersRepo";

export class PosInvoicesService {
    static async handleOnPrint(actions: IActions, order: IPosOrderModel) {
        try {
            actions.setLoading();
            if (
                order.status !== OrderStatusEnum.COMPLETED &&
                order.status !== OrderStatusEnum.RETURNED
            ) {
                ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER_STATUS);
                return;
            }
            await PosPrintersRepo.printInvoiceOrder(order);
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrint] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnPrintReturned(actions: IActions, returnedOrder: IPosReturnedOrderModel) {
        try {
            actions.setLoading();
            await PosPrintersRepo.printReturnedInvoiceOrder(returnedOrder);
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrintReturned] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_RETURN_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnPrintPaymentVoucher(actions: IActions, voucher: IPaymentVoucherModel) {
        try {
            const { isVouchersPrintingActive } = PrintersHelper.getPrintingSettings();
            if (!isVouchersPrintingActive) return;

            actions.setLoading();
            const body: IPosPaymentVoucherPdfPrinterBodyModel = {
                organizationName: OrganizationHelper.getOrganization()?.name || "",
                organizationSubName: OrganizationHelper.getOrganization()?.subName || "",
                address: OrganizationHelper.getOrganization()?.address || "",
                vatNo: OrganizationHelper.getOrganization()?.taxNumber || "",
                crNo: OrganizationHelper.getOrganization()?.registrationNumber || "",
                phone: OrganizationHelper.getOrganization()?.mobile || "",
                invoiceTitle: "سند صرف",
                total: voucher.cash + voucher.network,
                date: DateUtils.format(voucher.date),
                note: voucher.note || "",
            };
            await PosPrintersRepo.print(PosPaymentVoucherPdfPrinterContent(body, {
                // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                logo: './images/logo.jpg',
                showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
            }));
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrintPaymentVoucher] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_PAYMENT_VOUCHER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnPrintReceiptVoucher(actions: IActions, voucher: IReceiptVoucherModel) {
        try {
            const { isVouchersPrintingActive } = PrintersHelper.getPrintingSettings();
            if (!isVouchersPrintingActive) return;

            actions.setLoading();
            const body: IPosReceiptVoucherPdfPrinterBodyModel = {
                organizationName: OrganizationHelper.getOrganization()?.name || "",
                organizationSubName: OrganizationHelper.getOrganization()?.subName || "",
                address: OrganizationHelper.getOrganization()?.address || "",
                vatNo: OrganizationHelper.getOrganization()?.taxNumber || "",
                crNo: OrganizationHelper.getOrganization()?.registrationNumber || "",
                phone: OrganizationHelper.getOrganization()?.mobile || "",
                invoiceTitle: "سند قبض",
                total: voucher.cash + voucher.network,
                date: DateUtils.format(voucher.date),
                note: voucher.note || "",
                customerName: voucher.customer?.name || "",
                customerMobile: voucher.customer?.mobile || "",
                customerAddress: voucher.customer?.address || "",
                customerTaxNumber: voucher.customer?.taxNumber || "",
            };
            await PosPrintersRepo.print(PosReceiptVoucherPdfPrinterContent(body, {
                // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                logo: './images/logo.jpg',
                showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
            }));
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrintReceiptVoucher] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_RECEIPT_VOUCHER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnSubmitReturnOrder(
        oldOrder: IPosOrderModel,
        returnedOrder: IPosReturnedOrderModel,
        cash: number,
        network: number,
        deferred: number,
        actions: IActions,
        posActions: IPosActions
    ) {
        try {
            if (!PosOrderService.checkShift()) return;
            const { isInvoicePrintingActive } = PrintersHelper.getPrintingSettings();
            let customer: ICustomerModel | undefined = undefined;
            if (deferred && oldOrder.customer) {
                customer = await PosCustomersRepo.getCustomer(oldOrder.customer.id);
                if ((customer?.credit || 0) < deferred) {
                    ToastHelper.error(TranslateConstants.CASH_BALANCE_IS_NOT_ENOUGH);
                    return;
                }
            }
            actions.setLoading();
            const formattedReturnedOrder = await PosOrderUtils.handleReturnOrderData(returnedOrder, cash, network, deferred);
            await PosReturnedOrdersRepo.addReturnedOrder(formattedReturnedOrder);
            ReturnedOrderHelper.incrementReturnedInvoiceNumber();
            await PosOrderRepo.updateOrderStatus(oldOrder);
            await UpdateFetch.update<IPosOrderModel>(
                CachedKeysEnums.POS_INVOICES,
                { ...oldOrder, status: OrderStatusEnum.RETURNED },
                (data: IPosOrderModel) => data.id === returnedOrder.id
            );
            if (deferred && oldOrder.customer && customer) {
                await PosCustomersRepo.updateCustomer(oldOrder.customer.id, {
                    ...customer,
                    credit: (customer.credit || 0) - deferred,
                });
            }
            if (isInvoicePrintingActive)
                await PosPrintersRepo.printReturnedInvoiceOrder(formattedReturnedOrder);
        } catch (error) {
            debug(`PosInvoicesService [handleOnSubmitReturnOrder] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_RETURN_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
            actions.closeModal();
            PosOrderService.resetOrder(posActions);
        }
    }
}
