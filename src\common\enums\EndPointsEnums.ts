export enum EndPointsEnums {
    ORGANIZATIONS = 'organizations',
    ORGANIZATIONS_OWN = 'organizations/own',
    ORGANIZATIONS_OWN_SENSITIVES = 'organizations/own-sensitives',
    ORGANIZATIONS_CHECK_ROLE = 'organizations/check-role',
    ORGANIZATIONS_UPDATES_COUNT = 'organizations/updates-counts',
    CUSTOMERS = 'customers',
    AUTH = 'auth',
    REGISTRATION_REQUESTS = 'registration-requests',
    CATEGORIES = 'categories',
    TABLES = 'tables',
    PRODUCTS = 'products',
    GET_PRINTERS = 'getPrinters',
    PRINT_KITCHEN = 'print-kitchen',
    PRINT_INVOICE = 'print-invoice',
    PRINT = 'print',
    PRINT_SHIFT = 'print-shift',
    SHIFTS = 'shifts',
    SHIFTS_SUM = 'shifts/sum',
    ORDERS = 'orders',
    ORDERS_GET_ONE = 'orders/get-one',
    ORDERS_SUM = 'orders/sum',
    ORDERS_COUNT = 'orders/count',
    ORDERS_RUSH_HOURS = 'orders/rush-hours',
    ORDER_PRODUCTS_SUM = 'order-products/sum',
    ORDER_PRODUCTS_SUM_MANY = 'order-products/sum_many',
    ORDER_PRODUCTS_GET = 'order-products/get',
    ORDER_PRODUCTS_ADDITIONS_SUM = 'order-product-additions/sum',
    DISCOUNTS = 'discounts',
    ADDITIONS = 'additions',
    DELIVERY_APPS = 'delivery-apps',
    PRODUCTS_DELIVERY_APPS = 'products-delivery-apps',
    SIZES_DELIVERY_APPS = 'sizes-delivery-apps',
    ADDITIONS_DELIVERY_APPS = 'additions-delivery-apps',
    RETURNED_ORDERS = 'returned-orders',
    RETURNED_ORDERS_SUM = 'returned-orders/sum',
    RETURNED_ORDERS_COUNT = 'returned-orders/count',
    RETURNED_ORDER_PRODUCTS_SUM_MANY = 'returned-order-products/sum_many',
    RETURNED_ORDER_PRODUCTS_ADDITIONS_SUM = 'returned-order-product-additions/sum',
    SUPPLIERS = 'suppliers',
    RAW_MATERIALS = 'raw-materials',
    PURCHASE_ORDERS = 'purchase-orders',
    PURCHASE_ORDERS_COUNT = 'purchase-orders/count',
    PURCHASE_INVOICES = 'purchase-invoices',
    PURCHASE_INVOICES_PAY = 'purchase-invoices/pay',
    PURCHASE_INVOICES_COUNT = 'purchase-invoices/count',
    PURCHASE_INVOICE_PAYMENTS = 'purchase-invoice-payments',
    PURCHASE_INVOICE_PAYMENTS_SUM = 'purchase-invoice-payments/sum',
    PURCHASE_INVOICES_SUM = 'purchase-invoices/sum',
    PAYMENT_VOUCHERS = 'payment-vouchers',
    PAYMENT_VOUCHERS_CREATE_MANY = 'payment-vouchers/create-many',
    PAYMENT_VOUCHERS_SUM = 'payment-vouchers/sum',
    RETURNED_PURCHASE_INVOICES = 'returned-purchase-invoices',
    RETURNED_PURCHASE_INVOICES_COUNT = 'returned-purchase-invoices/count',
    RETURNED_PURCHASE_INVOICES_SUM = 'returned-purchase-invoices/sum',
    RECEIPT_VOUCHERS = 'receipt-vouchers',
    RECEIPT_VOUCHERS_CREATE_MANY = 'receipt-vouchers/create-many',
    RECEIPT_VOUCHERS_SUM = 'receipt-vouchers/sum',
    DISPLAY_CONNECT = 'display/connect',
    DISPLAY_DISCONNECT = 'display/disconnect',
    DISPLAY_IS_CONNECTED = 'display/isConnected',
    DISPLAY_PORTS = 'display/ports',
    DISPLAY_PRICE = 'display/price',
};