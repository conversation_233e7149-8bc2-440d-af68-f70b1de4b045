import { APP_LOCAL_DB_COLLECTIONS, AppLocalDB } from "../../../common/config/localDB";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IPosPausedOrderModel } from "../../../common/models/PosOrderModel";
import { debug } from "../../../common/utils/CommonUtils";
import { IPosPausedOrder } from "../interface";

export class PosPausedOrdersRepo {
    static async addPausedOrder(order: IPosPausedOrder) {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");

            const body: IPosPausedOrderModel = {
                ...order,
                id: AppLocalDB.generateId(),
                shiftId: shift.shiftId,
                deleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            if (!body.orderProducts.length) throw new Error("No products found");

            const res = await AppLocalDB.add(
                APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS,
                body,
            );
            return res;
        } catch (error) {
            debug(`PosPausedOrdersRepo [addPausedOrder] Error: `, error);
            throw error;
        }
    }

    static async getPausedOrders() {
        try {
            const res = await AppLocalDB.get<IPosPausedOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS,
                { orderBy: ["id", "desc"] }
            );
            return res;
        } catch (error) {
            debug(`PosPausedOrdersRepo [getPausedOrders] Error: `, error);
            throw error;
        }
    }

    static async updatePausedOrder(id: number, order: IPosPausedOrder) {
        try {
            const res = await AppLocalDB.update(
                APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS,
                id,
                order
            );
            return res;
        } catch (error) {
            debug(`PosPausedOrdersRepo [updatePausedOrder] Error: `, error);
            throw error;
        }
    }

    static async deleteAllPausedOrder() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS);
        } catch (error) {
            debug(`PosPausedOrdersRepo [deleteAllPausedOrder] Error: `, error);
            throw error;
        }
    }

    static async deletePausedOrder(order: IPosPausedOrderModel) {
        try {
            await AppLocalDB.deleteById(APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS, order.id);
        } catch (error) {
            debug(`PosPausedOrdersRepo [deletePausedOrder] Error: `, error);
            throw error;
        }
    }
}