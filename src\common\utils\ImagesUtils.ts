export const ImagesUtils = {
    getBase64ImageFromUrl: async (url?: string): Promise<string> => {
        if (!url) return "";

        const res = await fetch(url);
        const blob = await res.blob();

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
                if (typeof reader.result === "string") {
                    resolve(reader.result);
                } else {
                    reject(new Error("Failed to convert image to base64 string."));
                }
            };
            reader.onerror = reject;
        });
    },
};
