import { IAdminReceiptVoucherInputs } from "../../../app/admin/pages/receiptVoucher/AdminReceiptVoucherInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IReceiptVoucherBody } from "../../interfaces/body/ReceiptVoucherBody";
import { IPaymentVoucherSumModel } from "../../models/PaymentVoucherSumModel";
import { IReceiptVoucherModel } from "../../models/ReceiptVoucherModel";
import { debug } from "../../utils/CommonUtils";

export class ReceiptVouchersApiRepo {
    static async getReceiptVouchers(options?: {
        startTime?: number;
        endTime?: number;
        destination?: string;
        isCash?: boolean;
        isBank?: boolean;
        customerId?: number;
        getCustomers?: boolean;
    }) {
        try {
            const res = await AxiosHelper.get<IReceiptVoucherModel[]>(
                EndPointsEnums.RECEIPT_VOUCHERS,
                {
                    params: {
                        startTime: options?.startTime,
                        endTime: options?.endTime,
                        destination: options?.destination,
                        isCash: options?.isCash,
                        isBank: options?.isBank,
                        customerId: options?.customerId,
                        getCustomers: options?.getCustomers,
                    },
                }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReceiptVouchersApiRepo [getReceiptVouchers] Error: `, error);
            throw error;
        }
    }

    static async addReceiptVoucher(body: IAdminReceiptVoucherInputs) {
        try {
            const formattedBody: IReceiptVoucherBody = { ...body, customerId: body.customer?.id };
            delete (formattedBody as any).customer;
            const res = await AxiosHelper.post<IReceiptVoucherModel>(
                EndPointsEnums.RECEIPT_VOUCHERS,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReceiptVouchersApiRepo [addReceiptVoucher] Error: `, error);
            throw error;
        }
    }

    static async updateReceiptVoucher(
        id: number,
        body: IAdminReceiptVoucherInputs
    ) {
        try {
            const formattedBody: IReceiptVoucherBody = { ...body, customerId: body.customer?.id };
            delete (formattedBody as any).customer;
            const res = await AxiosHelper.patch<IReceiptVoucherModel>(
                EndPointsEnums.RECEIPT_VOUCHERS,
                id,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReceiptVouchersApiRepo [updateReceiptVoucher] Error: `, error);
            throw error;
        }
    }

    static async getSum(
        startTime: number,
        endTime: number,
        destination?: string,
        isCash?: boolean,
        isBank?: boolean,
        customerId?: number,
    ) {
        try {
            const res = await AxiosHelper.get<IPaymentVoucherSumModel>(
                EndPointsEnums.RECEIPT_VOUCHERS_SUM,
                { params: { startTime, endTime, destination, isCash, isBank, customerId } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReceiptVouchersApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}