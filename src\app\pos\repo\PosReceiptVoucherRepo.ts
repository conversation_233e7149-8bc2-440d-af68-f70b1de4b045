import { APP_LOCAL_DB_COLLECTIONS, AppLocalDB } from "../../../common/config/localDB";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IReceiptVoucherBody } from "../../../common/interfaces/body/ReceiptVoucherBody";
import { ILocalDBGetOptionsProperties, IOperator } from "../../../common/LocalDB/LocalDBInterface";
import { IReceiptVoucherModel } from "../../../common/models/ReceiptVoucherModel";
import { debug } from "../../../common/utils/CommonUtils";
import { IAdminReceiptVoucherInputs } from "../../admin/pages/receiptVoucher/AdminReceiptVoucherInterface";

export class PosReceiptVoucherRepo {
    static getAndCountReceiptVouchers = async () => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [], count: 0 };
            }

            const receiptVoucherWere: [keyof IReceiptVoucherModel, IOperator, any][] = [
                [`shiftId`, "==", shift.shiftId],
            ];

            const res = await AppLocalDB.getAndCount<IReceiptVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS,
                {
                    where: [receiptVoucherWere],
                }
            );
            return res;
        } catch (error) {
            debug(`PosReceiptVoucherRepo [getAndCountReceiptVouchers] Error: ${error}`);
            throw error;
        }
    };

    static async getShiftReceiptVouchers() {
        try {
            const shift = ShiftHelper.get();
            if (!shift) return [];

            const res = await AppLocalDB.get<IReceiptVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS,
                {
                    orderBy: ["id", "desc"],
                    where: [[[`shiftId`, "===", shift.shiftId]]],
                }
            );
            return res;
        } catch (error) {
            debug(`PosReceiptVoucherRepo [getShiftReceiptVouchers] Error: `, error);
            throw error;
        }
    }

    static async getReceiptVouchers(
        options?: ILocalDBGetOptionsProperties<IReceiptVoucherModel>
    ) {
        try {
            return await AppLocalDB.get<IReceiptVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS,
                options
            );
        } catch (error) {
            debug(`PosReceiptVoucherRepo [getReceiptVouchers] Error: `, error);
            throw error;
        }
    }

    static async addReceiptVoucher(body: IAdminReceiptVoucherInputs) {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");

            const formattedBody = {
                ...body,
                id: AppLocalDB.generateId(),
                shiftId: shift.shiftId,
            };

            const res = await AppLocalDB.add(
                APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS,
                formattedBody,
                true
            );
            return res;
        } catch (error) {
            debug(`PosReceiptVoucherRepo [addReceiptVoucher] Error: `, error);
            throw error;
        }
    }

    static async receiptVoucherCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS);
        } catch (error) {
            debug(`PosReceiptVoucherRepo [receiptVoucherCount] Error: `, error);
            throw error;
        }
    }

    static async uploadReceiptVouchers(body: IReceiptVoucherBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsEnums.RECEIPT_VOUCHERS_CREATE_MANY, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosReceiptVoucherRepo [uploadReceiptVouchers] Error: `, error);
            throw error;
        }
    }

    static async deleteAllReceiptVouchers() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS);
        } catch (error) {
            debug(`PosReceiptVoucherRepo [deleteAllReceiptVouchers] Error: `, error);
            throw error;
        }
    }
}