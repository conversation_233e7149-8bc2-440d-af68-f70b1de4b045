interface IKitchenPdfPrinterAdditionModel {
    quantity: string;
    name: string;
    secondName?: string;
}

export interface IKitchenPdfPrinterItemModel {
    quantity: string;
    name: string;
    secondName?: string;
    isDeleted?: boolean;
    additions?: IKitchenPdfPrinterAdditionModel[];
}

export interface IKitchenPdfPrinterBodyModel {
    organizationName?: string;
    orderNumber?: string;
    orderTitle?: string;
    orderType?: string;
    table?: string;
    items: IKitchenPdfPrinterItemModel[];
    note?: string;
}