import { Content } from "pdfmake/interfaces";
import { ICustomerReportPdfPrinterModel } from "./CustomerReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const CustomerReportPdfPrinterContent = (model: ICustomerReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "تقرير العملاء", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            fontSize: 10,
            bold: true,
            alignment: "right",
            margin: 4,
            lineHeight: 1.5,
            layout: "noBorders",
            table: {
                widths: ["*", "*"],
                body: [
                    [
                        {
                            columns: [
                                { text: model.customer.mobile, width: "*", marginRight: 10 },
                                {
                                    text: PdfMakeHelper.reverseTextIfArabic("جوال  العميل:"),
                                    width: "auto"
                                },
                            ],
                        },
                        {
                            columns: [
                                { text: PdfMakeHelper.reverseTextIfArabic(model.customer.name), width: "*", marginRight: 10 },
                                { text: PdfMakeHelper.reverseTextIfArabic("اســــم  العميل:"), width: "auto" },
                            ],
                        },
                    ],
                    [
                        {
                            columns: [
                                { text: model.customer.credit, width: "*", marginRight: 10 },
                                { text: PdfMakeHelper.reverseTextIfArabic("المتبقي:"), width: "auto" },
                            ],
                        },
                        {
                            columns: [
                                { text: model.customer.taxNumber, width: "*", marginRight: 10 },
                                { text: PdfMakeHelper.reverseTextIfArabic("الرقم  الضريبي:"), width: "auto" },
                            ],
                        },
                    ],
                ],
            },
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto", "*", "*", "auto", "*"],
                body: [
                    [
                        { text: "اجل", style: "tableHeader" },
                        { text: "شبكة", style: "tableHeader" },
                        { text: "نقداً", style: "tableHeader" },
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        { text: "الخصم", style: "tableHeader" },
                        { text: "الإجمالي الفرعي", style: "tableHeader" },
                        { text: "نوع الطلب", style: "tableHeader" },
                        { text: "نوع العملية", style: "tableHeader" },
                        { text: "رقم الفاتورة", style: "tableHeader" },
                        { text: "التاريخ", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.credit.toFixed(2),
                        el.network.toFixed(2),
                        el.cash.toFixed(2),
                        el.total.toFixed(2),
                        el.vat.toFixed(2),
                        el.discount.toFixed(2),
                        el.subTotal.toFixed(2),
                        PdfMakeHelper.reverseTextIfArabic(el.orderType),
                        PdfMakeHelper.reverseTextIfArabic(el.operationType),
                        el.number,
                        el.date,
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            stack: [
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { text: model.totals.count.toString(), margin: [0, 3, 0, 0] },
                                        { text: "عدد المبيعات:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { text: model.totals.returnedCount.toString(), margin: [0, 3, 0, 0] },
                                        { text: "عدد المرتجعات: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.total.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي المبيعات: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.totalReturned.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي المرتجعات: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.cash.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي الكاش: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.network.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي الشبكة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.pay.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي المسدد: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.credit.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "إجمالي الاٌجل: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
            ]
        }
    ];
}
