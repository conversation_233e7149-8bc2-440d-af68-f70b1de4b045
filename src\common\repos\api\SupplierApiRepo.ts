import { IAdminSupplierInputs } from "../../../app/admin/pages/suppliers/AdminSuppliersInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { ISupplierModel } from "../../models/SuppliersModel";
import { debug } from "../../utils/CommonUtils";

export class SuppliersApiRepo {
    static async getSuppliers(search?: string, limit?: number) {
        try {
            const res = await AxiosHelper.get<ISupplierModel[]>(
                EndPointsEnums.SUPPLIERS,
                { params: { search, limit } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`SuppliersApiRepo [getSuppliers] Error: `, error);
            throw error;
        }
    }

    static async addSupplier(body: IAdminSupplierInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.post<ISupplierModel>(
                EndPointsEnums.SUPPLIERS,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`SuppliersApiRepo [addSupplier] Error: `, error);
            throw error;
        }
    }

    static async updateSupplier(id: number, body: IAdminSupplierInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.patch<ISupplierModel>(
                EndPointsEnums.SUPPLIERS,
                id,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`SuppliersApiRepo [updateSupplier] Error: `, error);
            throw error;
        }
    }
}
