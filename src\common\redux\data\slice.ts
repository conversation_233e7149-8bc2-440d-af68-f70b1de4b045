import { createSlice } from "@reduxjs/toolkit";
import InitialState from "./state";
import { closeModal, setLoading, setModal } from "./actions";

export const AppSlice = createSlice({
    name: "app",
    initialState: InitialState,
    reducers: {
        setModalAction: setModal,
        closeModalAction: closeModal,
        setLoadingAction: setLoading,
    },
});

export const {
    setModalAction,
    closeModalAction,
    setLoadingAction,
} = AppSlice.actions;

export default AppSlice.reducer;
