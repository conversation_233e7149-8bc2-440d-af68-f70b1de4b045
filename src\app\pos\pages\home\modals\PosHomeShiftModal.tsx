import NumpadComponent from "../../../../../common/components/NumpadComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ShiftHelper } from "../../../../../common/helpers/ShiftHelper";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useOnlineStatus from "../../../../../common/hooks/useOnlineStatus";
import useActions from "../../../../../common/redux/data/useActions";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";

const PosHomeShiftModal = () => {
    const actions = useActions();
    const posActions = usePosActions();
    const isOnline = useOnlineStatus();
    const isShiftOpen = ShiftHelper.isOpen();

    const handleOnSave = async (amount: number) =>
        PosHomeService.handleShift(amount, posActions, actions);

    return (
        <>
            {(!isOnline && isShiftOpen )&& (
                <div className="text-center p-2 font-tajawal-bold">
                    <div className="text-red-500">{TranslateHelper.t(TranslateConstants.NO_INTERNET_CAN_NOT_SYNC_DATA)}</div>
                    <div className="text-sm">{TranslateHelper.t(TranslateConstants.FOR_SHIFT_SYNC_NAVIGATE_TO_SETTINGS)}</div>
                </div>
            )}
            {(isOnline && isShiftOpen )&& (
                <div className="text-center p-2 font-tajawal-bold">
                    <div className="text-green-700">{TranslateHelper.t(TranslateConstants.SHIFT_WILL_BE_CLOSED_AUTOMATICALLY)}</div>
                    <div className="text-sm">{TranslateHelper.t(TranslateConstants.DO_NOT_TURN_OFF_YOUR_INTERNET_WHILE_SYNC)}</div>
                </div>
            )}
            <NumpadComponent
                title={
                    isShiftOpen
                        ? TranslateConstants.CLOSE_SHIFT
                        : TranslateConstants.OPEN_SHIFT
                }
                textCenter={true}
                button={{
                    text: TranslateConstants.SAVE,
                    onClick: (val) => handleOnSave(val as number),
                    isDisabled: !isOnline && isShiftOpen,
                }}
                showCancelButton={true}
                onCancelButtonClick={() => actions.closeModal()}
                allowZeroAtFirst={true}
            />
        </>
    );
};

export default PosHomeShiftModal;
