import * as Comlink from "comlink";
import { LocalDbAddAction } from "./actions/add";
import { LocalDbCountAction } from "./actions/count";
import {
    LocalDbClearAction,
    LocalDbDeleteAllAction,
    LocalDbDeleteByIdAction,
    LocalDbDeleteDbAction,
} from "./actions/delete";
import {
    LocalDbGetAction,
    LocalDbGetOneAction,
    LocalDbGetOneByIdAction,
} from "./actions/get";
import { LocalDbUpdateAction } from "./actions/update";
import {
    ILocalDBDataWithId,
    ILocalDBDataWithoutId,
    ILocalDBGetOptionsProperties,
    ILocalDBStore,
    IOperator,
} from "./LocalDBInterface";
import {
    LocalDbBackupAction,
    LocalDbBackupAndDownloadAction,
    LocalDbRestoreFromBackupAction,
} from "./actions/backup";

export class LocalDB {
    private db: IDBDatabase | undefined = undefined;

    constructor(
        dbName: string,
        collections: ILocalDBStore[],
        version: number = 1
    ) {
        this.init(dbName, collections, version);
    }

    private async init(
        dbName: string,
        collections: ILocalDBStore[],
        version: number = 1
    ): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(dbName, version);

            request.onupgradeneeded = () => {
                this.db = request?.result;

                collections.forEach((objectStore) => {
                    if (!this.db?.objectStoreNames.contains(objectStore.collection)) {
                        const store = this.db?.createObjectStore(objectStore.collection, {
                            keyPath: "id",
                        });

                        objectStore.indexes?.forEach((index) => {
                            store?.createIndex(index.name, index.key, index.options);
                        });
                    } else {
                        const store = request.transaction?.objectStore(
                            objectStore.collection
                        );
                        if (store) {
                            objectStore.indexes?.forEach((index) => {
                                if (!store.indexNames.contains(index.name)) {
                                    store.createIndex(index.name, index.key, index.options);
                                }
                            });

                            const indexesLength = store.indexNames.length;
                            let indexToDelete = 0;
                            for (let i = 0; i < indexesLength; i++) {
                                const indexName = store.indexNames[indexToDelete];
                                if (!objectStore.indexes?.find((x) => x.name === indexName)) {
                                    store.deleteIndex(indexName);
                                    continue;
                                }
                                indexToDelete++;
                            }
                        }
                    }
                });
            };

            request.onsuccess = () => {
                this.db = request?.result;
                version = this.db?.version || 1;
                resolve(true);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        });
    }

    public async close(): Promise<boolean> {
        return new Promise((resolve) => {
            this.db?.close();
            resolve(true);
        });
    }

    public generateId(): number {
        return new Date().getTime();
    }

    public async set(
        collection: string,
        data: ILocalDBDataWithId | ILocalDBDataWithId[]
    ): Promise<boolean> {
        await this.deleteAll(collection);
        return await LocalDbAddAction(this.db, collection, data, false, 1000);
    }

    public async add(
        collection: string,
        data: ILocalDBDataWithId | ILocalDBDataWithId[],
        updateIfKeyExist: boolean = false,
        batchSize: number = 1000
    ) {
        return await LocalDbAddAction(
            this.db,
            collection,
            data,
            updateIfKeyExist,
            batchSize
        );
    }

    public async update(
        collection: string,
        id: string | number,
        data: ILocalDBDataWithoutId,
        addIfNotExist: boolean = false
    ): Promise<boolean> {
        return await LocalDbUpdateAction(
            this.db,
            collection,
            id,
            data,
            addIfNotExist
        );
    }

    public async count(collection: string): Promise<number> {
        return await LocalDbCountAction(this.db, collection);
    }

    public async getOne<T>(
        collection: string,
        [key, operator, value]: [string, IOperator, any]
    ): Promise<T | undefined> {
        return await LocalDbGetOneAction<T>(this.db, collection, [
            key,
            operator,
            value,
        ]);
    }

    public async getOneById<T>(
        collection: string,
        id: string | number
    ): Promise<T | undefined> {
        return await LocalDbGetOneByIdAction<T>(this.db, collection, id);
    }

    public async get<T>(
        collection: string,
        options?: ILocalDBGetOptionsProperties<T>
    ): Promise<T[]> {
        return await LocalDbGetAction<T>(this.db, collection, options);
    }

    public async getAndCount<T>(
        collection: string,
        options?: ILocalDBGetOptionsProperties<T>
    ): Promise<{ data: T[]; count: number }> {
        const data = await this.get<T>(collection, options);
        const count = data.length;

        return { data, count };
    }

    public async deleteById(
        collection: string,
        id: string | number
    ): Promise<boolean> {
        return await LocalDbDeleteByIdAction(this.db, collection, id);
    }

    public async deleteAll(collection: string): Promise<boolean> {
        return await LocalDbDeleteAllAction(this.db, collection);
    }

    public async clear(): Promise<boolean> {
        return await LocalDbClearAction(this.db);
    }

    public async deleteDB(dbName: string): Promise<boolean> {
        return await LocalDbDeleteDbAction(dbName);
    }

    public async backup(tables?: string[]): Promise<Record<string, any[]>> {
        return await LocalDbBackupAction(this.db, tables);
    }

    public async backupAndDownload(
        tables?: string[],
        filename: string = "localDB-backup.json"
    ): Promise<void> {
        await LocalDbBackupAndDownloadAction(this.db, tables, filename);
    }

    public async restoreFromFile(file: File): Promise<void> {
        await LocalDbRestoreFromBackupAction(this.db, file);
    }
}

Comlink.expose(LocalDB);
