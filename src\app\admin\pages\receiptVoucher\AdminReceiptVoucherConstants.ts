import { ReceiptVoucherDestinationsConstant } from "../../../../common/constants/CommonConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { IAdminReceiptVoucherInputs } from "./AdminReceiptVoucherInterface";

export const AdminReceiptVoucherInputs = (): IAdminReceiptVoucherInputs => ({
    destination: undefined,
    customer: undefined,
    date: new Date().getTime(),
    cash: 0,
    network: 0,
    note: "",
});

export const AdminReceiptVoucherDataTableHeaders = [
    TranslateConstants.NUMBER,
    TranslateConstants.RECEIPT_DESTINATION,
    TranslateConstants.CUSTOMER,
    TranslateConstants.CASH,
    TranslateConstants.NETWORK,
    TranslateConstants.DATE,
    TranslateConstants.STATEMENT,
];

export const AdminReceiptVoucherDestinationsConstant = [
    {
        name: null,
    },
    ...ReceiptVoucherDestinationsConstant
        .filter((el) => el.name !== TranslateConstants.CUSTOMER),
]
