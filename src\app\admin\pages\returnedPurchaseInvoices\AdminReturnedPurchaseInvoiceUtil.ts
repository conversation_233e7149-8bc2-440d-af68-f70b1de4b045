import { PaymentsEnum } from "../../../../common/enums/DataEnums";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { PurchaseUtils } from "../../../../common/utils/PurchaseUtils";
import { IAdminReturnedPurchaseInvoiceInputs, IAdminReturnedPurchaseInvoiceState } from "./AdminReturnedPurchaseInvoiceInterfaces";

export class AdminReturnedPurchaseInvoiceUtil {
    static handlePurchaseInvoiceProductsData = (
        purchaseInvoiceProducts: IAdminReturnedPurchaseInvoiceInputs[],
        isPriceIncludingTax: boolean = true
    ): IAdminReturnedPurchaseInvoiceInputs[] => {
        return purchaseInvoiceProducts.map((el) => {
            const { subTotal, vat } = PurchaseUtils.getPurchaseProductData(el, isPriceIncludingTax);
            const purchaseInvoiceProductId =
                el.purchaseInvoiceProductId ??
                (el as any)?.item?.purchaseInvoiceProductId ??
                (el as any)?.item?.item?.purchaseInvoiceProductId ??
                undefined; // TODO: fix and remove this

            return {
                ...el,
                purchaseInvoiceProductId,
                subTotal: fixedNumber(subTotal),
                vat: fixedNumber(vat)
            };
        });
    }

    static convertPurchaseInvoiceToReturnedPurchaseInvoice = (
        purchaseInvoice?: IPurchaseInvoiceModel
    ): IAdminReturnedPurchaseInvoiceState => ({
        paymentMethod: PaymentsEnum.CASH,
        purchaseInvoice,
        note: "",
        nativeTotal: purchaseInvoice?.nativeTotal ?? 0,
        productsDiscount: purchaseInvoice?.productsDiscount ?? 0,
        subTotal: purchaseInvoice?.subTotal ?? 0,
        vat: purchaseInvoice?.vat ?? 0,
        total: purchaseInvoice?.total ?? 0,
        returnedPurchaseInvoiceProducts: (purchaseInvoice?.purchaseInvoiceProducts ?? [])?.map((item) => ({
            name: item.name,
            isTaxable: item.isTaxable,
            price: item.price,
            purchaseInvoiceProductId: item.id,
            quantity: item.quantity,
            discount: item.discount,
            subTotal: item.subTotal,
            vat: item.vat,
            total: item.total,
        })),
    })
}