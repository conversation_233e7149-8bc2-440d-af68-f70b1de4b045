import { useMemo } from "react";
import { useLocation } from "react-router-dom";
import { RoutsConstants } from "../../../common/constants/RoutesConstants";

const useShowOrder = () => {
    const { pathname } = useLocation();
    const showOrder = useMemo(() => {
        return (
            pathname === RoutsConstants.pos.home.fullPath
        );
    }, [pathname]);

    const showMenu = useMemo(() => {
        return pathname === RoutsConstants.pos.home.fullPath;
    }, [pathname]);

    return { showOrder, showMenu };
};

export default useShowOrder;
