import { <PERSON><PERSON><PERSON>, FC, SetStateAction, useEffect, useMemo } from "react";
import useFetch from "../../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import InputComponent from "../../../../../common/components/InputComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import {
    PaymentMethodsConstant,
    VatTypesConstant,
} from "../../../../../common/constants/CommonConstants";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import { RawMaterialApiRepo } from "../../../../../common/repos/api/RawMaterialApiRepo";
import { SuppliersApiRepo } from "../../../../../common/repos/api/SupplierApiRepo";
import {
    AdminPurchaseOrderHeaders,
    AdminPurchaseOrderInitialState,
    AdminPurchaseOrderInputs,
} from "../AdminPurchaseOrderConstants";
import {
    IAdminPurchaseOrderInputs,
    IAdminPurchaseOrderState,
} from "../AdminPurchaseOrderInterfaces";
import { ISupplierModel } from "../../../../../common/models/SuppliersModel";
import { PurchaseOrderApiRepo } from "../../../../../common/repos/api/PurchaseOrderApiRepo";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminPurchaseOrderValidation } from "../AdminPurchaseOrderValidation";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import useCustomState from "../../../../../common/hooks/useCustomState";
import StatusComponent from "../../../../../common/components/StatusComponent";
import { PurchaseUtils } from "../../../../../common/utils/PurchaseUtils";
import useDerivedState from "../../../../../common/hooks/useDerivedState";
import AdminMultiplePurchasesProductInputsComponent from "../../../components/AdminMultiplePurchasesProductInputsComponent";
import { AdminPurchaseOrderService } from "../AdminPurchaseOrderService";
import { AdminPurchaseOrderUtil } from "../AdminPurchaseOrderUtil";
import { IPurchaseOrderModel } from "../../../../../common/models/PurchaseOrderModel";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
    setSelectedItem: Dispatch<SetStateAction<IPurchaseOrderModel | undefined>>;
    selectedItem: IPurchaseOrderModel | undefined;
}

const AdminPurchaseOrderInputsFeature: FC<IProps> = ({ setIsView, setSelectedItem, selectedItem }) => {
    const { isSm } = useScreenSize();
    const [state, setState, resetState] =
        useCustomState<IAdminPurchaseOrderState, IPurchaseOrderModel>(AdminPurchaseOrderInitialState, {
            updateState: selectedItem,
            explodeFromReset: ["supplier"],
            updateSelector: AdminPurchaseOrderUtil.handelUpdateSelector,
        });
    const oldState = useDerivedState(state);

    const {
        data: purchaseOrderCount,
        isLoading: purchaseOrderCountIsLoading,
        isError: purchaseOrderCountIsError,
        refetch: refetchPurchaseOrderCount,
    } = useFetch(
        EndPointsEnums.PURCHASE_ORDERS_COUNT,
        PurchaseOrderApiRepo.getPurchaseOrderCount,
        {
            autoFetchOnMount: !selectedItem,
            autoFetchIfEmpty: false,
        }
    );

    const {
        data: suppliers,
        isLoading: suppliersIsLoading,
        isError: suppliersIsError,
    } = useFetch(EndPointsEnums.SUPPLIERS, SuppliersApiRepo.getSuppliers);

    const {
        data: rawMaterials,
        isLoading: rawMaterialsIsLoading,
        isError: rawMaterialsIsError,
    } = useFetch(
        EndPointsEnums.RAW_MATERIALS,
        RawMaterialApiRepo.getRawMaterials
    );

    const addPurchaseOrder = useFlatMutate(
        (body) => PurchaseOrderApiRepo.addPurchaseOrder(body), {
        showDefaultSuccessToast: true,
        updateCached: { key: EndPointsEnums.PURCHASE_ORDERS, operation: "add" },
        beforeStartMiddleware: () => AdminPurchaseOrderValidation.inputsValidation(state),
        onSuccess: async ({ args, data }) => {
            if (args[1]) await AdminPurchaseOrderService.handlePreview(data);
            refetchPurchaseOrderCount();
            resetState();
        },
    });

    const updatePurchaseOrder = useFlatMutate(
        (id, body) => PurchaseOrderApiRepo.updatePurchaseOrder(id, body), {
        showDefaultSuccessToast: true,
        updateCached: {
            key: EndPointsEnums.PURCHASE_ORDERS,
            operation: "update",
            selector: (data: IPurchaseOrderModel) => data.id
        },
        beforeStartMiddleware: () => AdminPurchaseOrderValidation.inputsValidation(state),
        onSuccess: async ({ args, data }) => {
            if (args[2]) await AdminPurchaseOrderService.handlePreview(data);
            setSelectedItem(data);
        },
    });

    useEffect(() => {
        setState((prev) => ({
            ...prev,
            ...PurchaseUtils.getPurchaseData(
                prev.purchaseOrderProducts,
                prev.isPriceIncludingTax
            ),
        }));
    }, [state.purchaseOrderProducts, state.isPriceIncludingTax]);

    const multiInputs = useMemo(() => {
        return AdminPurchaseOrderInputs(state.isPriceIncludingTax, rawMaterials);
    }, [rawMaterials, state.isPriceIncludingTax]);

    const isDisabled = useMemo(() => {
        return !!selectedItem && AdminPurchaseOrderValidation.checkDeeplyEqual(state, selectedItem);
    }, [state, selectedItem]);

    const handleOnPurchaseOrderProductsResult = (
        result: IAdminPurchaseOrderInputs[]
    ) => {
        setState((prev) => ({
            ...prev,
            purchaseOrderProducts: AdminPurchaseOrderUtil.handlePurchaseOrderProductsData(
                result,
                prev.isPriceIncludingTax
            ),
        }))
    };

    const handleOnClick = (isPrint: boolean) => {
        if (selectedItem) updatePurchaseOrder(selectedItem.id, state, isPrint);
        else addPurchaseOrder(state, isPrint);
    };

    return (
        <>
            <AddAndFilterComponent
                isSaveDisabled={isDisabled}
                isSaveAndPrintDisabled={isDisabled}
                isEdit={!!selectedItem}
                onSave={() => handleOnClick(false)}
                onSaveAndPrint={() => handleOnClick(true)}
                onBack={() => setIsView(true)}
            />
            <StatusComponent
                isLoading={
                    purchaseOrderCountIsLoading ||
                    suppliersIsLoading ||
                    rawMaterialsIsLoading
                }
                isError={
                    purchaseOrderCountIsError ||
                    suppliersIsError ||
                    rawMaterialsIsError
                }
                height={isSm ? 7.5 : 8.1}
                className="!p-0"
            >
                <ListComponent
                    padding="0 px-2"
                    isBorder={true}
                    className="bg-base-100 !border-none"
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                        <InputComponent
                            label={TranslateConstants.INVOICE_NUMBER}
                            type="fixed"
                            value={`${selectedItem?.number ?? (purchaseOrderCount ?? 0) + 1}`}
                        />
                        <InputComponent
                            label={TranslateConstants.DATE}
                            type="datetime-local"
                            value={DateUtils.toIsoString(state.date)}
                            onChange={(date) =>
                                setState({ ...state, date: new Date(date).getTime() })
                            }
                        />
                        <InputComponent
                            label={TranslateConstants.DUE_DATE}
                            type="datetime-local"
                            value={DateUtils.toIsoString(state.dueDate)}
                            onChange={(dueDate) =>
                                setState({ ...state, dueDate: new Date(dueDate).getTime() })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.PAYMENT_METHOD}
                            items={PaymentMethodsConstant}
                            titleSelector={(item) => TranslateHelper.t(item.name)}
                            defaultValue={TranslateHelper.t(state.paymentMethod)}
                            isSearchable={false}
                            onSelect={(item) =>
                                setState({ ...state, paymentMethod: item.value })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.SUPPLIERS}
                            items={suppliers}
                            titleSelector={(item) => item.name}
                            subtitleSelector={(item) => item.mobile}
                            isLoading={suppliersIsLoading}
                            isError={suppliersIsError}
                            defaultValue={state.supplier?.name}
                            onSelect={(supplier: ISupplierModel) =>
                                setState({ ...state, supplier })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.VAT_TYPE}
                            items={VatTypesConstant}
                            titleSelector={(item) => TranslateHelper.t(item.name)}
                            defaultValue={TranslateHelper.t(
                                VatTypesConstant[state.isPriceIncludingTax ? 0 : 1].name
                            )}
                            isSearchable={false}
                            onSelect={(item) =>
                                setState({ ...state, isPriceIncludingTax: item.value })
                            }
                        />
                    </div>
                    <AdminMultiplePurchasesProductInputsComponent<IAdminPurchaseOrderInputs>
                        headers={AdminPurchaseOrderHeaders}
                        inputs={multiInputs}
                        onResult={handleOnPurchaseOrderProductsResult}
                        defaultValues={state.purchaseOrderProducts}
                        state={state}
                        setState={setState}
                        resetInputs={
                            state.purchaseOrderProducts.length === 0 ||
                            state.isPriceIncludingTax !== oldState.isPriceIncludingTax
                        }
                    />
                </ListComponent>
            </StatusComponent>
        </>
    );
};

export default AdminPurchaseOrderInputsFeature;
