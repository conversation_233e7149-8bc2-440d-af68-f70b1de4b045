import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { IPaymentVoucherDestinationSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { IPurchaseInvoiceSumModel } from "../../../../common/models/PurchaseInvoiceSumModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { IReturnedPurchaseInvoiceSumModel } from "../../../../common/models/ReturnedPurchaseInvoiceSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { IProfitAndLossReportPdfPrinterModel } from "../../../../common/pdf-make/slices/profitAndLossReport/ProfitAndLossReportPdfPrinterModel";
import { ProfitAndLossReportPdfPrinterContent } from "../../../../common/pdf-make/slices/profitAndLossReport/ProfitAndLossReportReportPdfPrinterContent";
import { IProfitAndLossReportTotalInfoInterface } from "./ProfitAndLossReportInterface";

export class AdminProfitAndLossReportReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: IProfitAndLossReportTotalInfoInterface,
    ) {
        const model: IProfitAndLossReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            data
        };

        PdfMakeUtils.download(ProfitAndLossReportPdfPrinterContent(model), "Profit And Loss Report", {
            isLandScape: false,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleTotalInfo(
        orderSum: IOrderSumModel | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined,
        purchaseInvoicePaymentsSum: IPurchaseInvoiceSumModel | undefined,
        returnedPurchaseInvoicePaymentsSum: IReturnedPurchaseInvoiceSumModel | undefined,
        paymentVouchersSum: IPaymentVoucherDestinationSumModel[] | undefined,
    ): IProfitAndLossReportTotalInfoInterface {
        const data = {
            ordersTotal: orderSum?.total ?? 0,
            returnedOrdersTotal: returnedOrderSum?.total ?? 0,
            purchaseInvoicesTotal: purchaseInvoicePaymentsSum?.total ?? 0,
            returnedPurchaseInvoicesTotal: returnedPurchaseInvoicePaymentsSum?.total ?? 0,
            paymentVouchersTotal: paymentVouchersSum?.reduce((acc, el) => acc + el.total, 0) ?? 0,
            paymentVouchersSum
        };

        const salesNet = data.ordersTotal - data.returnedOrdersTotal;
        const purchasesNet = data.purchaseInvoicesTotal - data.returnedPurchaseInvoicesTotal;
        const totalNet = salesNet - purchasesNet - data.paymentVouchersTotal;

        return {
            ...data,
            totalNet,
            salesNet,
            purchasesNet,
        }
    }
}
