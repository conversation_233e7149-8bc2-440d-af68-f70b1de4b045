import { FC, useMemo } from "react";

interface IProps {
    children: React.ReactNode;
}

const RandomBackGroundImageComponent: FC<IProps> = ({ children }) => {
    const bgs = [
        "/images/background1.jpg",
        "/images/background2.jpg",
        "/images/background3.jpg",
    ];
    const randomBackground = useMemo(() => {
        return bgs[Math.floor(Math.random() * bgs.length)];
    }, []);

    return (
        <div className="flex h-s relative">
            <div
                className="h-s w-screen bg-cover bg-center"
                style={{
                    backgroundImage: `url('${randomBackground}')`,
                }}
            ></div>
            <div className="h-s w-screen bg-black absolute opacity-75"></div>
            <div className="h-s w-screen absolute">{children}</div>
        </div>
    );
};

export default RandomBackGroundImageComponent;
