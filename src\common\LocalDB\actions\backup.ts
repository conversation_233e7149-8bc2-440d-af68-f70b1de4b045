
export const LocalDbBackupAction = async (
    db: IDBDatabase | undefined,
    tables?: string[]
) => {
    if (!db) throw new Error("IndexedDB is not initialized");

    const backupData: Record<string, any[]> = {};
    let storeNames = Array.from(db.objectStoreNames);
    if (tables?.length) {
        storeNames = storeNames.filter((storeName) => tables.includes(storeName));
    }

    await Promise.all(
        storeNames.map((storeName) => {
            return new Promise<void>((resolve, reject) => {
                const tx = db!.transaction(storeName, "readonly");
                const store = tx.objectStore(storeName);
                const request = store.openCursor();
                const data: any[] = [];

                request.onsuccess = (event) => {
                    const cursor = (event.target as IDBRequest<IDBCursorWithValue>)
                        .result;
                    if (cursor) {
                        data.push(cursor.value);
                        cursor.continue();
                    } else {
                        backupData[storeName] = data;
                        resolve();
                    }
                };

                request.onerror = (event) => {
                    reject((event.target as IDBRequest).error);
                };
            });
        })
    );

    return backupData;
};

export const LocalDbBackupAndDownloadAction = async (
    db: IDBDatabase | undefined,
    tables?: string[],
    filename = "localDB-backup.json"
) => {
    const data = await LocalDbBackupAction(db, tables);
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    a.style.display = "none";
    document.body.appendChild(a);
    a.click();

    // Cleanup
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

export const LocalDbRestoreFromBackupAction = async (
    db: IDBDatabase | undefined,
    file: File
) => {
    const fileContent = await file.text();
    const parsedData: Record<string, any[]> = JSON.parse(fileContent);

    if (!db) throw new Error("IndexedDB is not initialized");

    const storeNames = Object.keys(parsedData);

    await Promise.all(
        storeNames.map((storeName) => {
            return new Promise<void>((resolve, reject) => {
                const tx = db!.transaction(storeName, "readwrite");
                const store = tx.objectStore(storeName);
                const records = parsedData[storeName];

                records.forEach((record) => {
                    store.put(record);
                });

                tx.oncomplete = () => resolve();
                tx.onerror = (event) => reject((event.target as IDBRequest).error);
            });
        })
    );
}