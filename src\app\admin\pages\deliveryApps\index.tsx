import { useState } from "react";
import AdminDeliveryAppsViewFeature from "./features/AdminDeliveryAppsViewFeature";
import AdminDeliveryAppsProductsFeature from "./features/AdminDeliveryAppsProductsFeature";
import { TAdminDeliveryAppsState } from "./AdminDeliveryAppsInterface";
import { IDeliveryApp } from "../../../../common/interfaces";

const AdminDeliveryAppsPage = () => {
    const [view, setView] = useState<TAdminDeliveryAppsState>("view");
    const [deliveryApp, setDeliveryApp] = useState<IDeliveryApp | undefined>();

    const onProducts = (item: IDeliveryApp) => {
        setView("products");
        setDeliveryApp(item);
    };
    const onBack = () => setView("view");

    return (
        <>
            {view === "view" && (
                <AdminDeliveryAppsViewFeature onProducts={onProducts} />
            )}
            {view === "products" && (
                <AdminDeliveryAppsProductsFeature
                    onBack={onBack}
                    item={deliveryApp as IDeliveryApp}
                />
            )}
        </>
    );
};

export default AdminDeliveryAppsPage;
