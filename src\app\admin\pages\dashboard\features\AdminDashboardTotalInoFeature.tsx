import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IProfitAndLossReportTotalInfoInterface } from "../../profitAndLossReport/ProfitAndLossReportInterface";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";
import { IReturnedOrderSumModel } from "../../../../../common/models/ReturnedOrderSumModel";
import { IPaymentVoucherSumModel } from "../../../../../common/models/PaymentVoucherSumModel";

interface IProps {
    data: IProfitAndLossReportTotalInfoInterface;
    orderSum?: IOrderSumModel;
    returnedOrderSum?: IReturnedOrderSumModel;
    receiptVoucherSum?: IPaymentVoucherSumModel;
}

const AdminDashboardTotalInoFeature: FC<IProps> = ({
    data,
    orderSum,
    returnedOrderSum,
    receiptVoucherSum,
}) => {
    return (
        <ReportTotalInfoComponent
            items={[
                {
                    text: TranslateConstants.SALES,
                    value: data?.salesNet,
                },
                {
                    text: TranslateConstants.ORDERS_COUNT,
                    value: orderSum?.count,
                    fixedVal: 0,
                },
                {
                    text: TranslateConstants.CREDIT,
                    value: (orderSum?.deferred ?? 0) - ((returnedOrderSum?.deferred ?? 0) + (receiptVoucherSum?.total ?? 0))
                },
                {
                    text: TranslateConstants.APPS,
                    value: (orderSum?.deliveryApps ?? 0) - (returnedOrderSum?.deliveryApps ?? 0),
                },
                {
                    text: TranslateConstants.EXPENSES,
                    value: data?.paymentVouchersTotal,
                },
                {
                    text:
                        data.totalNet >= 0
                            ? TranslateConstants.PROFIT
                            : TranslateConstants.LOSS,
                    value: data.totalNet,
                },
            ]}
        />
    );
}

export default AdminDashboardTotalInoFeature;
