import { LocalDB } from "../LocalDB/LocalDB";
import { ILocalDBStore } from "../LocalDB/LocalDBInterface";

export const APP_LOCAL_DB_COLLECTIONS = {
    CATEGORIES: "categories",
    PRODUCTS: "products",
    TABLES: "tables",
    ORDERS: "orders",
    SHIFT: "shift",
    DISCOUNTS: "discounts",
    CUSTOMERS: "customers",
    ADDITIONS: "additions",
    RETURNED_ORDERS: "returned-orders",
    RECEIPT_VOUCHERS: "receipt-vouchers",
    PAYMENT_VOUCHERS: "payment-vouchers",
    PAUSED_ORDERS: "paused-orders",
} as const;

export type APP_LOCAL_DB_COLLECTIONS_TYPE = (typeof APP_LOCAL_DB_COLLECTIONS)[keyof typeof APP_LOCAL_DB_COLLECTIONS];

const stores: ILocalDBStore[] = [
    {
        collection: APP_LOCAL_DB_COLLECTIONS.CATEGORIES,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.PRODUCTS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.TABLES,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.ORDERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.SHIFT,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.DISCOUNTS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.ADDITIONS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.RECEIPT_VOUCHERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.PAUSED_ORDERS,
        indexes: [],
    },
] as const;

export type APP_LOCAL_DB_INDEXES_TYPE = (typeof stores)[number]["indexes"][number]["name"];

export const AppLocalDB = new LocalDB("AppLocalDB", stores, 8);