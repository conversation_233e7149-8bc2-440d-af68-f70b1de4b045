import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { IPurchaseInvoiceSumModel } from "../../../../common/models/PurchaseInvoiceSumModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { IReturnedPurchaseInvoiceSumModel } from "../../../../common/models/ReturnedPurchaseInvoiceSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { ITaxReportPdfPrinterModel } from "../../../../common/pdf-make/slices/taxReport/TaxReportPdfPrinterModel";
import { TaxReportPdfPrinterContent } from "../../../../common/pdf-make/slices/taxReport/TaxReportReportPdfPrinterContent";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { ITaxReportTotalInfoInterface } from "./TaxReportInterface";

export class AdminTaxReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: ITaxReportTotalInfoInterface,
    ) {
        const model: ITaxReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            data
        };

        PdfMakeUtils.download(TaxReportPdfPrinterContent(model), "Tax Report", {
            isLandScape: false,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleTotalInfo(
        orderSum: IOrderSumModel | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined,
        purchaseInvoicePaymentsSum: IPurchaseInvoiceSumModel | undefined,
        returnedPurchaseInvoicePaymentsSum: IReturnedPurchaseInvoiceSumModel | undefined,
    ): ITaxReportTotalInfoInterface {
        const ordersSubTotal = fixedNumber(orderSum?.subTotal ?? 0, 3);
        const returnedOrdersSubTotal = fixedNumber(returnedOrderSum?.subTotal ?? 0, 3);
        const purchaseInvoicesSubTotal = fixedNumber((purchaseInvoicePaymentsSum?.vat ?? 0) / 0.15, 3);
        const returnedPurchaseInvoicesSubTotal = fixedNumber((returnedPurchaseInvoicePaymentsSum?.vat ?? 0) / 0.15, 3);

        return {
            ordersSubTotal,
            returnedOrdersSubTotal,
            ordersNet: fixedNumber(ordersSubTotal - returnedOrdersSubTotal, 3),
            orderTax: fixedNumber((ordersSubTotal - returnedOrdersSubTotal) * 0.15, 3),
            purchaseInvoicesSubTotal,
            returnedPurchaseInvoicesSubTotal,
            purchaseInvoicesNet: fixedNumber(purchaseInvoicesSubTotal - returnedPurchaseInvoicesSubTotal, 3),
            purchaseInvoicesTax: fixedNumber((purchaseInvoicesSubTotal - returnedPurchaseInvoicesSubTotal) * 0.15, 3),
        };
    }
}
