import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminPaymentVoucherInputs } from "./AdminPaymentVoucherInterface";

export class AdminPaymentVoucherValidation {
    static inputsValidation = (
        values: IAdminPaymentVoucherInputs,
        cash?: number
    ) => {
        let isValid = true;

        if (!values.date) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DATE_FIELD_IS_REQUIRED);
        } else if (values.cash < 0 || values.network < 0 || (values.cash + values.network) === 0) {
            isValid = false;
            ToastHelper.error(TranslateConstants.AMOUNT_MUST_BE_POSITIVE);
        } else if (cash !== undefined && values.cash > cash) {
            isValid = false;
            ToastHelper.error(TranslateConstants.CASH_BALANCE_IS_NOT_ENOUGH);
        } else if (values.note?.length > 200) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NOTE_SIZE_ERROR);
        }

        return isValid;
    };
}
