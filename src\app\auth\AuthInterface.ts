export interface IAuthLoginInputs {
    email: string;
    password: string;
}

export interface IAuthRegisterInputs {
    name: string;
    email: string;
    mobile: string;
    password: string;
}

export interface IAuthLoginState {
    inputs: IAuthLoginInputs;
    loading: boolean;
    error: string;
}

export interface IAuthRegisterState {
    inputs: IAuthRegisterInputs;
    loading: boolean;
    error: string;
}

export type IAutView = 'login' | 'register';

export interface IAuthState {
    view: IAutView;
}