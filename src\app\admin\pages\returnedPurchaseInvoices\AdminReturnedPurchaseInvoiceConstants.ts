import { IMultiInputsItem } from "../../../../common/components/MultiInputsComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { PaymentsEnum } from "../../../../common/enums/DataEnums";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { PurchaseUtils } from "../../../../common/utils/PurchaseUtils";
import {
    IAdminReturnedPurchaseInvoiceInputs,
    IAdminReturnedPurchaseInvoiceState,
} from "./AdminReturnedPurchaseInvoiceInterfaces";

export const AdminReturnedPurchaseInvoiceHeaders = [
    TranslateConstants.PRODUCT,
    TranslateConstants.QUANTITY,
    TranslateConstants.PRICE,
    TranslateConstants.DISCOUNT,
    TranslateConstants.TOTAL,
    "",
];

export const AdminReturnedPurchaseInvoiceInputs = (
    isPriceIncludingTax?: boolean
): IMultiInputsItem<IAdminReturnedPurchaseInvoiceInputs>[] => {
    return [
        {
            name: "name",
            width: "!w-5/12",
            className: "text-gray-400",
            isDisabled: true,
        },
        {
            name: "quantity",
            placeholder: "0",
            width: "!w-2/12",
            type: "number",
            validation: (value, _, originalItem) => Number(value) <= originalItem.quantity,
        },
        {
            name: "price",
            placeholder: "0",
            width: "!w-2/12",
            type: "number",
            className: "text-gray-400",
            isDisabled: true,
        },
        {
            name: "discount",
            placeholder: "0",
            width: "!w-2/12",
            type: "number",
            className: "text-gray-400",
            isDisabled: true,
        },
        {
            name: "total",
            placeholder: "0",
            width: "!w-2/12",
            isHideInMd: true,
            className: "text-gray-400",
            type: "fixed",
            computedValue: (item) =>
                fixedNumber(
                    PurchaseUtils.getPurchaseProductData(item, isPriceIncludingTax).total,
                    3
                ).toString(),
        },
        {
            type: "checkbox",
            name: "isTaxable",
            placeholder: "0",
            width: "!w-2/12",
            label: TranslateConstants.VAT_15,
            isDisabled: true,
        },
    ];
};

export const AdminReturnedPurchaseInvoiceInitialState: IAdminReturnedPurchaseInvoiceState =
{
    paymentMethod: PaymentsEnum.CASH,
    purchaseInvoice: undefined,
    note: "",
    nativeTotal: 0,
    productsDiscount: 0,
    subTotal: 0,
    vat: 0,
    total: 0,
    returnedPurchaseInvoiceProducts: [],
};

export const AdminReturnedPurchaseInvoiceDataTableHeaders = [
    TranslateConstants.RETURNED_NUMBER,
    TranslateConstants.INVOICE_NUMBER,
    TranslateConstants.SUPPLIER,
    TranslateConstants.TOTAL,
    TranslateConstants.DATE,
];
