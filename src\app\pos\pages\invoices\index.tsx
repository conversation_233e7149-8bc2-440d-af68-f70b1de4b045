import { useMemo, useState } from "react";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import PosInvoicesFeature from "./features/PosInvoicesFeature";
import PosReturnedInvoicesFeature from "./features/PosReturnedInvoiceFeature";
import SelectedButtonsComponent from "../../../../common/components/SelectedButtonsComponent";
import { PosInvoicesViewEnum } from "./PosInvoicesConstants";
import PosVouchersFeature from "./features/PosVouchersFeature";
import PosPausedOrdersFeature from "./features/PosPausedOrdersFeature";

const PosInvoicesPage = () => {
    const [view, setView] = useState(PosInvoicesViewEnum.INVOICES);

    const getPageTitle = useMemo(() => {
        switch (view) {
            case PosInvoicesViewEnum.RETURNED_INVOICES:
                return TranslateConstants.RETURNED_INVOICES;
            case PosInvoicesViewEnum.VOUCHERS:
                return TranslateConstants.VOUCHERS;
            case PosInvoicesViewEnum.PAUSED_ORDERS:
                return TranslateConstants.PAUSED_ORDERS;
            default:
                return TranslateConstants.INVOICES;
        }
    }, [view]);

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="flex justify-between items-center">
                <div className="font-tajawal-bold text-2xl">
                    {TranslateHelper.t(getPageTitle)}
                </div>
                <SelectedButtonsComponent
                    items={[
                        TranslateConstants.INVOICES,
                        TranslateConstants.RETURNED_INVOICES,
                        TranslateConstants.VOUCHERS,
                        TranslateConstants.PAUSED_ORDERS,
                    ]}
                    textSelector={(item: string) => item}
                    defaultSelected={TranslateConstants.INVOICES}
                    onSelect={(item: string) => setView(item as PosInvoicesViewEnum)}
                    className="w-[60%]"
                />
            </div>
            {view === PosInvoicesViewEnum.INVOICES && <PosInvoicesFeature />}
            {view === PosInvoicesViewEnum.RETURNED_INVOICES && (
                <PosReturnedInvoicesFeature />
            )}
            {view === PosInvoicesViewEnum.VOUCHERS && <PosVouchersFeature />}
            {view === PosInvoicesViewEnum.PAUSED_ORDERS && <PosPausedOrdersFeature />}
            <PosBackButtonComponent />
        </div>
    );
};

export default PosInvoicesPage;
