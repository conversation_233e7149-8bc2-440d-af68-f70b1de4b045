import { FC, useState } from "react";
import { ModalButtonsComponent } from "../components/ModalComponent";
import PriceComponent from "../components/PriceComponent";
import { TranslateConstants } from "../constants/TranslateConstants";
import InputComponent from "../components/InputComponent";
import { DateUtils } from "../utils/DateUtils";
import { fixedNumber } from "../utils/numberUtils";
import { PaymentsEnum } from "../enums/DataEnums";

export interface ISplitPaymentState {
    date: number;
    cash: number;
    network: number;
    deferred: number;
    note?: string;
}

interface IProps {
    total: number;
    onClick: (val: ISplitPaymentState) => void;
    date?: Date | number | string;
    paymentType?: PaymentsEnum;
}

export const SplitPaymentInitialState = (
    total: number = 0,
    date?: Date | number | string,
    paymentType = PaymentsEnum.CASH,
): ISplitPaymentState => ({
    date: date ? DateUtils.getTime(date) : new Date().getTime(),
    cash: paymentType === PaymentsEnum.CASH ? total : 0,
    network: paymentType === PaymentsEnum.BANK ? total : 0,
    deferred: paymentType === PaymentsEnum.CREDIT ? total : 0,
    note: "",
});

const SplitPaymentModal: FC<IProps> = ({ total, onClick, date, paymentType }) => {
    const [state, setState] = useState<ISplitPaymentState>(
        SplitPaymentInitialState(total, date, paymentType)
    );

    const handleOnPayEdit = (
        paymentType: "cash" | "network",
        value: number
    ) => {
        if (paymentType === "cash") {
            setState({ ...state, cash: value, deferred: fixedNumber(total - (value + state.network)) });
            return;
        }

        setState({ ...state, network: value, deferred: fixedNumber(total - (value + state.cash)) });
    }

    return (
        <>
            <form className="px-2 flex flex-col gap-2 sm:gap-0">
                <InputComponent
                    label={TranslateConstants.DATE}
                    type="datetime-local"
                    value={DateUtils.toIsoString(state.date)}
                    onChange={(date) => setState({ ...state, date: new Date(date).getTime() })}
                />
                <div className="flex gap-1 items-center">
                    <InputComponent
                        label={TranslateConstants.CASH}
                        type="number"
                        containerClassName="flex-1 text-center"
                        className="!text-center"
                        value={state.cash.toString()}
                        onChange={(cash) => handleOnPayEdit("cash", Number(cash))}
                    />
                    <span className="text-2xl mt-6">:</span>
                    <InputComponent
                        label={TranslateConstants.NETWORK}
                        type="number"
                        containerClassName="flex-1 text-center"
                        className="!text-center"
                        value={state.network.toString()}
                        onChange={(network) => handleOnPayEdit("network", Number(network))}
                    />
                    <span className="text-2xl mt-6">:</span>
                    <InputComponent
                        label={TranslateConstants.REMAINING}
                        type="fixed"
                        containerClassName="w-1/5 text-center"
                        className="!justify-center"
                        value={state.deferred.toString()}
                    />
                </div>
                <InputComponent
                    label={TranslateConstants.NOTICE}
                    type="textarea"
                    value={state.note}
                    onChange={(note) => setState({ ...state, note })}
                    textAreaRows={2}
                />
            </form>
            <ModalButtonsComponent
                text={TranslateConstants.PAY}
                component={<PriceComponent price={state.cash + state.network} color="white" />}
                className="!justify-between"
                onClick={() => onClick(state)}
                isDisabled={(state.cash + state.network + Math.abs(state.deferred)) !== total}
            />
        </>
    );
};

export default SplitPaymentModal;
