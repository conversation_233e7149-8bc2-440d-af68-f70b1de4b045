import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { useEffect, useState } from "react";
import { IPrinterModel } from "../../../../../common/models/PrinterModel";
import { useAppSelector } from "../../../../../common/redux/store";
import { posCategoriesSelector } from "../../../redux/selector";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { PosSettingsValidation } from "../PosSettingsValidation";
import { IPrinterData } from "../../../../../common/interfaces";
import { PosSettingsService } from "../PosSettingsService";
import useFetch from "../../../../../common/asyncController/useFetch";
import { PosPrintersRepo } from "../../../repo/PosPrintersRepo";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { LoadingLogoComponent } from "../../../../../common/components/LoadingComponent";
import ErrorComponent from "../../../../../common/components/ErrorComponent";
import { IoMdRefresh } from "react-icons/io";

const PosPrinterSettingsFeature = () => {
    const { translate, isRtl, isArabic } = useTranslate();
    const categories = useAppSelector(posCategoriesSelector);
    const [data, setData] = useState<IPrinterData[]>([]);
    const [disabled, setDisabled] = useState<boolean>(true);

    const {
        data: printers,
        isLoading,
        isError,
        refetch,
    } = useFetch(EndPointsEnums.GET_PRINTERS, PosPrintersRepo.getAllPrinters);

    useEffect(() => {
        PosSettingsService.initPrintersData(setData, categories);
    }, [categories]);

    useEffect(() => {
        const isDeeplyEqual = PosSettingsValidation.checkPrintersDeeplyEqual(data);
        setDisabled(!printers || isDeeplyEqual);
    }, [data, disabled]);

    const handleOnSave = () => PosSettingsService.onSave(data, setDisabled);
    const handleOnSelect = (value: IPrinterModel, index: number) =>
        PosSettingsService.handleOnPrinterSelect(setData, index, value);

    return (
        <div
            dir={isRtl ? "rtl" : "ltr"}
            className="bg-white rounded-xl shadow p-2 border"
        >
            <div className="flex justify-between items-center">
                <div className="text-lg font-tajawal-bold">
                    {translate(TranslateConstants.PRINTER_SETTINGS)}
                </div>
                <ButtonComponent
                    text={TranslateConstants.REFRESH}
                    className="!w-32"
                    iconComponent={<IoMdRefresh />}
                    bgColor={isLoading ? "base-200" : "transparent"}
                    textColor="primary"
                    borderColor={isLoading ? "gray-400" : "primary"}
                    onClick={refetch}
                    isDisabled={isLoading}
                />
            </div>
            <DividerComponent className="my-2" />
            {(isLoading || isError) && (
                <div className="flex justify-center items-center min-h-80">
                    {isError ? (
                        <ErrorComponent text={TranslateConstants.ERROR_GETTING_PRINTERS} />
                    ) : (
                        <LoadingLogoComponent className="h-full" />
                    )}
                </div>
            )}
            {!!printers && !isLoading && !isError && (
                <div className="grid grid-cols-1 lg:grid-cols-2 my-2 gap-2 gap-x-5">
                    {data?.map((item, index) => (
                        <div key={index}>
                            <label>
                                {!isArabic && item.secondName ? item.secondName : item.name}
                            </label>
                            <DropDownSearchComponent
                                placeholder={TranslateConstants.PRINTER_NAME}
                                isSearchable={false}
                                items={printers || []}
                                titleSelector={(item: IPrinterModel) => item.deviceId}
                                defaultValue={
                                    item.printer ? translate(item.printer) : undefined
                                }
                                onSelect={(value) => handleOnSelect(value, index)}
                                isNullable={false}
                                isLoading={isLoading}
                                isError={isError}
                            />
                        </div>
                    ))}
                </div>
            )}
            <DividerComponent className="my-4" />
            <ButtonComponent
                text={translate(TranslateConstants.SAVE)}
                className="lg:!w-1/4"
                onClick={handleOnSave}
                isDisabled={disabled || isLoading || isError}
            />
        </div>
    );
};

export default PosPrinterSettingsFeature;
