import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { IPurchaseInvoicePaymentModel } from "../../../../common/models/PurchaseInvoicePaymentModel";
import { IPurchaseInvoiceSumModel } from "../../../../common/models/PurchaseInvoiceSumModel";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { CashReportPdfPrinterContent } from "../../../../common/pdf-make/slices/cashReport/CashReportPdfPrinterContent";
import { ICashReportPdfPrinterModel } from "../../../../common/pdf-make/slices/cashReport/CashReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { ICashReportInterface, ICashReportTotalInfoInterface } from "./CashReportInterface";

export class AdminCashReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: ICashReportInterface[],
        totals: ICashReportTotalInfoInterface,
    ) {
        const model: ICashReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: data?.map((el) => ({
                date: DateUtils.format(el.date),
                number: el.number,
                type: el.type,
                cash: el.cash,
            })),
            totals: {
                ordersCount: totals.ordersCount,
                returnedCount: totals.returnedCount,
                paymentVoucherCount: totals.paymentVoucherCount,
                receiptVoucherCount: totals.receiptVoucherCount,
                total: totals.total,
            },
        };

        PdfMakeUtils.download(CashReportPdfPrinterContent(model), "Cash Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleTotalInfo(
        orderSum: IOrderSumModel | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined,
        purchaseInvoicePaymentsSum: IPurchaseInvoiceSumModel | undefined,
        paymentVouchersSum: IPaymentVoucherSumModel | undefined,
        receiptVouchersSum: IPaymentVoucherSumModel | undefined,
    ): ICashReportTotalInfoInterface {
        return {
            ordersCount: orderSum?.count ?? 0,
            returnedCount: returnedOrderSum?.count ?? 0,
            paymentVoucherCount: paymentVouchersSum?.count ?? 0,
            receiptVoucherCount: receiptVouchersSum?.count ?? 0,
            total: ((orderSum?.cash ?? 0) + (receiptVouchersSum?.cash ?? 0) - ((returnedOrderSum?.cash ?? 0) + (purchaseInvoicePaymentsSum?.cash ?? 0) + (paymentVouchersSum?.cash ?? 0))),
        };
    }

    static handleData(
        orders: IOrderModel[] = [],
        returnedOrders: IReturnedOrderModel[] = [],
        purchaseInvoicesPayments: IPurchaseInvoicePaymentModel[] = [],
        paymentVouchers: IPaymentVoucherModel[] = [],
        receiptVouchers: IReceiptVoucherModel[] = [],
    ): ICashReportInterface[] {
        return [
            ...orders.map((el) => ({
                number: el.invoiceNumber,
                type: TranslateHelper.t(TranslateConstants.SELL),
                cash: el.cash,
                date: el.startTime,
            })),
            ...returnedOrders.map((el) => ({
                number: el.returnedInvoiceNumber,
                type: TranslateHelper.t(TranslateConstants.RETURNED),
                cash: el.cash,
                date: el.startTime,
            })),
            ...purchaseInvoicesPayments.map((el) => ({
                number: el.id,
                type: TranslateHelper.t(TranslateConstants.PURCHASE_INVOICE_PAYMENT) + " - " + el.purchaseInvoiceId,
                cash: el.cash,
                date: el.date,
            })),
            ...paymentVouchers.map((el) => ({
                number: el.number,
                type: TranslateHelper.t(TranslateConstants.PAYMENT_VOUCHER) + (el.destination ? (" - " + TranslateHelper.t(el.destination)) : ""),
                cash: el.cash,
                date: el.date,
            })),
            ...receiptVouchers.map((el) => ({
                number: el.number,
                type: TranslateHelper.t(TranslateConstants.RECEIPT_VOUCHER) + (el.destination ? (" - " + TranslateHelper.t(el.destination)) : ""),
                cash: el.cash,
                date: el.date,
            })),
        ].sort((a, b) => b.date - a.date);
    }
}
