import { Dispatch, FC, SetStateAction, useEffect } from "react";
import { IProductModel } from "../../../../../common/models/ProductModel";
import TableComponent from "../../../../../common/components/TableComponent";
import { AdminDeliveryAppsProductsTableHeaders } from "../AdminDeliveryAppsConstants";
import { IAdminDeliveryAppsProductItemProps } from "../AdminDeliveryAppsInterface";
import { ProductSizeTypeEnum } from "../../../../../common/enums/DataEnums";
import { fixedNumber } from "../../../../../common/utils/numberUtils";
import { adminDeliveryAppsGetFormattedProducts } from "../AdminDeliveryAppsUtils";

interface IProps {
    products?: IProductModel[];
    formatProducts: IAdminDeliveryAppsProductItemProps[];
    setFormatProducts: Dispatch<SetStateAction<IAdminDeliveryAppsProductItemProps[]>>;
    deliveryApp: string;
}

const AdminDeliveryAppsProductsSlice: FC<IProps> = ({
    products,
    formatProducts,
    setFormatProducts,
    deliveryApp,
}) => {
    useEffect(() => {
        if (!formatProducts.length) {
            const newProducts = adminDeliveryAppsGetFormattedProducts(products, deliveryApp);
            setFormatProducts(newProducts);
        }
    }, [products]);

    const updateProductField = <
        K extends keyof IAdminDeliveryAppsProductItemProps
    >(
        item: IAdminDeliveryAppsProductItemProps,
        key: K,
        value: IAdminDeliveryAppsProductItemProps[K]
    ) => {
        setFormatProducts((prev) =>
            prev.map((i) => {
                if (i.productId !== item.productId) return i;
                if (
                    i.productSizeType !== ProductSizeTypeEnum.FIXED &&
                    i.sizeId !== item.sizeId
                )
                    return i;
                return { ...i, [key]: value };
            })
        );
    };

    const handleChangePrice = (
        priceInDeliveryApp: number,
        item: IAdminDeliveryAppsProductItemProps
    ) => {
        updateProductField(item, "priceInDeliveryApp", priceInDeliveryApp);
        const isActive = !!priceInDeliveryApp && priceInDeliveryApp !== item.priceInDeliveryApp;
        updateProductField(item, "active", isActive);
    };

    const handleOnActive = (
        active: boolean,
        item: IAdminDeliveryAppsProductItemProps
    ) => {
        updateProductField(item, "active", active);
    };

    return (
        <TableComponent
            headers={AdminDeliveryAppsProductsTableHeaders}
            items={formatProducts || []}
            showNumbering={true}
            selectors={(item: IAdminDeliveryAppsProductItemProps) => {
                return [
                    item.name,
                    item.price,
                    <input
                        type="number"
                        className="w-28 border border-gray-400 rounded p-2 text-center bg-base-100"
                        placeholder={item.price.toString()}
                        onChange={(e) => handleChangePrice(fixedNumber(e.target.value), item)}
                        value={item.priceInDeliveryApp?.toString() || ""}
                    />,
                ];
            }}
            imageSelector={(item: IAdminDeliveryAppsProductItemProps) => item.image}
            showActiveButton={true}
            activeSelector={(item: IAdminDeliveryAppsProductItemProps) => item.active}
            showActiveButtonBorder={false}
            onActive={(active: boolean, item: IAdminDeliveryAppsProductItemProps) => {
                handleOnActive(active, item);
            }}
        />
    );
};

export default AdminDeliveryAppsProductsSlice;
