import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import logo from "/logo.svg";
import { useTranslate } from "../hooks/useTranslate";

interface IProps {
    height?: string;
    width?: string;
}

const SplashScreenComponent: FC<IProps> = ({
    height = "h-s",
    width = "w-screen",
}) => {
    const { translate } = useTranslate();

    return (
        <div
            className={
                "justify-center items-center flex flex-col gap-5" +
                " " +
                height +
                " " +
                width
            }
        >
            <img src={logo} alt="logo" className="animate-h_spin w-20" />
            <h1 className="font-bold text-xl">{translate(TranslateConstants.APP_NAME)}</h1>
        </div>
    );
};

export default SplashScreenComponent;
