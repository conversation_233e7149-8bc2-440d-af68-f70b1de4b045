import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";

export const AdminMostSellingProductsReportTableHeaders = () => {
    const hasTobaccoTax =  OrganizationHelper.hasTobaccoTax();

    return [
        TranslateConstants.NAME,
        TranslateConstants.QUANTITY,
        TranslateConstants.THE_DISCOUNT,
        TranslateConstants.SUB_TOTAL,
        ...(hasTobaccoTax ? [TranslateConstants.TOBACCO_TAX] : []),
        TranslateConstants.TAX,
        TranslateConstants.TOTAL,
    ]
}