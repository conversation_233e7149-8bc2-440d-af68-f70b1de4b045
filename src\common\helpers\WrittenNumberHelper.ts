import writtenNumber from "written-number";

export class WrittenNumberHelper {
    static convert = (
        number?: number | string,
        options?: {
            lang?: "ar" | "en";
            mainCurrency?: string[];
            subCurrency?: string[];
        }
    ) => {
        if (!number) return "";
        if (isNaN(Number(number))) return "";

        const lang = options?.lang ?? "ar";
        const mainCurrency = options?.mainCurrency ?? ["ريال", "Ryal"];
        const subCurrency = options?.subCurrency ?? ["هللة", "Halala"];

        const [main, sub] = number.toString().split(".");

        const value =
            writtenNumber(Number(main), { lang }) +
            " " +
            mainCurrency[lang === "ar" ? 0 : 1] +
            (sub
                ? (lang === "ar" ? " و " : " And ") +
                writtenNumber(Number(sub), { lang }) +
                " " +
                subCurrency[lang === "ar" ? 0 : 1]
                : "");

        return value;
    };
}
