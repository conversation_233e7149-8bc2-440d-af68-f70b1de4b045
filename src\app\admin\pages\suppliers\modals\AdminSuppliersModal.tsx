import { FC } from "react";
import { IAdminSupplierInputs } from "../AdminSuppliersInterface";
import { AdminSupplierInputs } from "../AdminSuppliersConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminSuppliersValidation } from "../AdminSuppliersValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";
import { ISupplierModel } from "../../../../../common/models/SuppliersModel";
import { SuppliersApiRepo } from "../../../../../common/repos/api/SupplierApiRepo";

interface IProps {
    item?: ISupplierModel;
}

const AdminSuppliersModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminSupplierInputs>(
        AdminSupplierInputs,
        { updateState: item }
    );
    const addSupplier = useFlatMutate(SuppliersApiRepo.addSupplier, {
        updateCached: { key: EndPointsEnums.SUPPLIERS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateSupplier = useFlatMutate(SuppliersApiRepo.updateSupplier, {
        updateCached: {
            key: EndPointsEnums.SUPPLIERS,
            operation: "update",
            selector: (data: ISupplierModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminSuppliersValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateSupplier(item.id, state);
        addSupplier(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(value) => setState({ ...state, name: value })}
                    value={state.name}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.MOBILE}
                    onChange={(value) => setState({ ...state, mobile: value })}
                    value={state.mobile}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.TAX_NUMBER}
                    onChange={(value) => setState({ ...state, taxNumber: value })}
                    value={state.taxNumber}
                />
                <InputComponent
                    label={TranslateConstants.ADDRESS}
                    onChange={(value) => setState({ ...state, address: value })}
                    value={state.address}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminSuppliersModal;
