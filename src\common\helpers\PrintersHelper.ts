import { posPrintersKeyConstant, posPrintingSettingsKeyConstant } from "../constants/ConfigConstants";
import { PrinterEnum } from "../enums/DataEnums";
import { IPrinterData, IPrintingSettings } from "../interfaces";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class PrintersHelper {
    static getPrinters(): IPrinterData[] {
        const printers: IPrinterData[] = LocalStorageHelper.get(
            posPrintersKeyConstant
        );
        return printers || [];
    }

    static getDefaultPrinter(): IPrinterData | undefined {
        const printers = this.getPrinters();
        return printers.find((item) => item.categoryId === PrinterEnum.RECEIPT)
    };

    static setPrinters(printers: IPrinterData[]): void {
        LocalStorageHelper.set(posPrintersKeyConstant, printers);
    }

    static getPrintingSettings(): IPrintingSettings {
        return {
            isInvoicePrintingActive: true,
            isKitchenPrintingActive: true,
            isLogoPrintingActive: true,
            isShiftPrintingActive: true,
            isVouchersPrintingActive: true,
            ...LocalStorageHelper.get(posPrintingSettingsKeyConstant)
        };
    }

    static setPrintingSettings(settings: IPrintingSettings) {
        LocalStorageHelper.set(posPrintingSettingsKeyConstant, settings);
    }
}
