import { RootState } from "../../../common/redux/store";

export const posProductsSelector = (state: RootState) => state.pos.products;
export const posAdditionsSelector = (state: RootState) => state.pos.additions;
export const posCategoriesSelector = (state: RootState) => state.pos.categories;
export const posTablesSelector = (state: RootState) => state.pos.tables;
export const posDiscountsSelector = (state: RootState) => state.pos.discounts;
export const posOrderSelector = (state: RootState) => state.pos.order;
export const posHomeSelector = (state: RootState) => state.pos.home;