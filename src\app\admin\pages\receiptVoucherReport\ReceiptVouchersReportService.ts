import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { WrittenNumberHelper } from "../../../../common/helpers/WrittenNumberHelper";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { ReceiptVoucherPdfPrinterContent } from "../../../../common/pdf-make/slices/receiptVoucher/ReceiptVoucherPdfPrinterContent";
import { IReceiptVoucherPdfPrinterModel } from "../../../../common/pdf-make/slices/receiptVoucher/ReceiptVoucherPdfPrinterModel";
import { ReceiptVoucherReportPdfPrinterContent } from "../../../../common/pdf-make/slices/receiptVoucherReport/ReceiptVoucherReportPdfPrinterContent";
import { IReceiptVoucherReportPdfPrinterModel } from "../../../../common/pdf-make/slices/receiptVoucherReport/ReceiptVoucherReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminReceiptVouchersReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        receiptVouchers: IReceiptVoucherModel[] | undefined,
        receiptVouchersSum: IPaymentVoucherSumModel | undefined
    ) {
        const model: IReceiptVoucherReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: receiptVouchers?.map((el) => ({
                number: el.number.toString(),
                destination: el.destination ? TranslateHelper.t(el.destination) : "-",
                date: DateUtils.format(el.date),
                cash: el.cash,
                network: el.network,
                note: el.note || "",
            })),
            totals: {
                cash: receiptVouchersSum?.cash ?? 0,
                network: receiptVouchersSum?.network ?? 0,
                count: receiptVouchersSum?.count ?? 0,
            },
        };

        PdfMakeUtils.download(ReceiptVoucherReportPdfPrinterContent(model), "Receipt Vouchers Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static async handleOnPrint(receiptVouchers: IReceiptVoucherModel) {
        const model: IReceiptVoucherPdfPrinterModel = {
            date: DateUtils.format(receiptVouchers.date),
            number: receiptVouchers.number.toString(),
            destination: receiptVouchers.destination ? TranslateHelper.t(receiptVouchers.destination) : "-",
            amount: (receiptVouchers.cash + receiptVouchers.network).toString(),
            writtenAmount: WrittenNumberHelper.convert(receiptVouchers.cash + receiptVouchers.network),
            note: receiptVouchers.note || "",
        };

        PdfMakeUtils.preview(ReceiptVoucherPdfPrinterContent(model), {
            headerType: "normal",
            header: await PdfMakeHeaders.normal(false, "normal"),
        });
    }
}
