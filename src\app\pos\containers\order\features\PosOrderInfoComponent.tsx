import { FC, useMemo } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { IPosOrder } from "../../../interface";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { orderUtils } from "../../../../../common/utils/OrderUtils";
import { OrganizationHelper } from "../../../../../common/helpers/OrganizationHelper";

interface IProps {
    order: IPosOrder;
}

const PosOrderInfoComponent: FC<IProps> = ({ order }) => {
    const { translate } = useTranslate();
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    const { subTotal, vat, discount, tobaccoTax } = useMemo(() => {
        return orderUtils.getPosOrderData(order);
    }, [order]);

    return (
        <div className="bg-base-200 border-y flex flex-col gap-2 p-2 text-sm font-tajawal-bold">
            {order.type === OrderTypeEnum.DINE_IN && (
                <div className="flex justify-between items-center">
                    <span>{translate(TranslateConstants.TABLE)}</span>
                    <span>
                        {order.table?.name ?? translate(TranslateConstants.NOT_FOUND)}
                    </span>
                </div>
            )}
            {order.type === OrderTypeEnum.DELIVERY_APP && (
                <div className="flex justify-between items-center">
                    <span>{translate(TranslateConstants.DELIVERY_APP)}</span>
                    <span>
                        {translate(order.deliveryApp ?? TranslateConstants.NOT_FOUND)}
                    </span>
                </div>
            )}
            {!!order.customer && (
                <div className="flex justify-between items-center">
                    <span>{translate(TranslateConstants.CUSTOMER)}</span>
                    <span>{order.customer.name}</span>
                </div>
            )}
            <div className="flex justify-between items-center">
                <span>{translate(TranslateConstants.SUB_TOTAL)}</span>
                <span>{subTotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
                <span>{translate(TranslateConstants.THE_DISCOUNT)}</span>
                <span>{discount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
                <span>{translate(TranslateConstants.TAX)}</span>
                <span>{vat.toFixed(2)}</span>
            </div>
            {
                hasTobaccoTax && (
                    <div className="flex justify-between items-center">
                        <span>{translate(TranslateConstants.TOBACCO_TAX)}</span>
                        <span>{tobaccoTax.toFixed(2)}</span>
                    </div>
                )
            }
        </div>
    );
};

export default PosOrderInfoComponent;
