import MultiInputsComponent, {
    IMultiInputsItem,
} from "../../../common/components/MultiInputsComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import InputComponent from "../../../common/components/InputComponent";
import InfoTableComponent from "../../../common/components/InfoTableComponent";
import { Dispatch, SetStateAction } from "react";

interface AdminMultiplePurchasesProductInputsState {
    isPriceIncludingTax?: boolean;
    note: string;
    nativeTotal: number;
    productsDiscount: number;
    subTotal: number;
    vat: number;
    total: number;
}

interface IProps<T> {
    headers: string[];
    inputs: IMultiInputsItem<T>[];
    onResult: (value: T[]) => void;
    defaultValues?: T[];
    values?: T[];
    resetInputs: boolean;
    state: AdminMultiplePurchasesProductInputsState;
    setState: Dispatch<SetStateAction<any>>;
    showDeleteButton?: boolean;
    showAddButton?: boolean;
}

function AdminMultiplePurchasesProductInputsComponent<T>({
    headers,
    inputs,
    onResult,
    defaultValues,
    values,
    resetInputs,
    state,
    setState,
    showDeleteButton = true,
    showAddButton = true,
}: IProps<T>) {
    return (
        <MultiInputsComponent<T>
            key={`inputs-${state.isPriceIncludingTax ?? ""}`}
            buttonText={TranslateConstants.ADD_PRODUCT}
            headers={headers}
            inputs={inputs}
            showMobileViewOnSm={true}
            showAddButton={showAddButton}
            onResult={(purchaseOrderProducts) => onResult(purchaseOrderProducts)}
            maxItems={50}
            productButtonClassName="sm:!w-1/12"
            defaultValues={defaultValues}
            values={values}
            resetInputs={resetInputs}
            showDeleteButton={showDeleteButton}
            suffixComponent={
                <div className="sm:w-2/3 md:w-1/2 lg:2/5 flex flex-col gap-2 mb-2 sm:mb-0">
                    <InputComponent
                        type="textarea"
                        textAreaRows={2}
                        placeholder={TranslateConstants.NOTICE}
                        value={state.note || ""}
                        onChange={(note) => setState({ ...state, note })}
                    />
                    <InfoTableComponent
                        items={[
                            {
                                text: TranslateConstants.TOTAL_BEFORE_DISCOUNT,
                                value: state.nativeTotal,
                            },
                            {
                                text: TranslateConstants.TOTAL_PRODUCTS_DISCOUNT,
                                value: state.productsDiscount,
                            },
                            {
                                text: TranslateConstants.TOTAL_BEFORE_VAT,
                                value: state.subTotal,
                            },
                            { text: TranslateConstants.VAT_15, value: state.vat },
                            { text: TranslateConstants.TOTAL, value: state.total },
                        ]}
                    />
                </div>
            }
        />
    );
}

export default AdminMultiplePurchasesProductInputsComponent;
