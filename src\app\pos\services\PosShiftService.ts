import { OrderTypeEnum } from "../../../common/enums/DataEnums";
import { IOrderBody } from "../../../common/interfaces/body/orderBody";
import { IPaymentVoucherBody } from "../../../common/interfaces/body/PaymentVoucherBody";
import { IReceiptVoucherBody } from "../../../common/interfaces/body/ReceiptVoucherBody";
import { IReturnedOrderBody } from "../../../common/interfaces/body/ReturnedOrderBody";
import { IShiftBody } from "../../../common/interfaces/body/shiftBody";
import { debug } from "../../../common/utils/CommonUtils";
import { PosOrderUtils } from "../containers/order/PosOrderUtils";
import { IPosOrderProduct, IPosOrderProductAddition } from "../interface";
import { PosOrderRepo } from "../repo/PosOrderRepo";
import { PosPausedOrdersRepo } from "../repo/PosPausedOrdersRepo";
import { PosPaymentVoucherRepo } from "../repo/PosPaymentVoucherRepo";
import { PosReceiptVoucherRepo } from "../repo/PosReceiptVoucherRepo";
import { PosReturnedOrdersRepo } from "../repo/PosReturnedOrdersRepo";
import { PosShiftRepo } from "../repo/PosShiftRepo";

export class PosShiftService {
    private static uploadShifts = async () => {
        try {
            let limit = 20;
            let skip = 0;

            let shifts = await PosShiftRepo.getShifts({ limit, skip });
            while (shifts.length) {
                const body: IShiftBody[] = shifts.map((el) => ({
                    shiftId: el.shiftId,
                    startAmount: el.startAmount || 0,
                    endAmount: el.endAmount || 0,
                    ordersCount: el.ordersCount || 0,
                    returnedOrdersCount: el.returnedOrdersCount || 0,
                    additionAmount: el.additionAmount || 0,
                    shortageAmount: el.shortageAmount || 0,
                    totalAmount: el.totalAmount || 0,
                    totalNetAmount: el.totalNetAmount || 0,
                    totalReturnedAmount: el.totalReturnedAmount || 0,
                    cashAmount: el.cashAmount || 0,
                    networkAmount: el.networkAmount || 0,
                    deferredAmount: el.deferredAmount || 0,
                    addedToShiftAmount: el.addedToShiftAmount || 0,
                    customerReceiptsAmount: el.customerReceiptsAmount || 0,
                    pullsAmount: el.pullsAmount || 0,
                    cashNetAmount: el.cashNetAmount || 0,
                    networkNetAmount: el.networkNetAmount || 0,
                    deliveryAppsAmount: el.deliveryAppsAmount || 0,
                    vatAmount: el.vatAmount || 0,
                    discountAmount: el.discountAmount || 0,
                    tobaccoTaxAmount: el.tobaccoTaxAmount || 0,
                    startTime: new Date(el.startTime).getTime(),
                    endTime: new Date(el.endTime).getTime(),
                }));
                await PosShiftRepo.uploadShifts(body);
                skip = shifts[shifts.length - 1].id;
                shifts = await PosShiftRepo.getShifts({ limit, skip });
            }
            await PosShiftRepo.deleteAllShifts();
        } catch (error) {
            debug(`PosShiftService [uploadShifts] Error: `, error);
            throw error;
        }
    };

    private static uploadOrders = async () => {
        try {
            let limit = 50;
            let skip = 0;

            let orders = await PosOrderRepo.getOrders({ limit, skip });
            while (orders.length) {
                const body: IOrderBody[] = orders.map((el) => {
                    if (!("orderProducts" in el) && !("products" in el)) throw new Error("No order products found");
                    let orderProducts: IPosOrderProduct[]
                    if (el.type === OrderTypeEnum.DINE_IN && "products" in el) {
                        orderProducts = (el as any).products;
                    } else {
                        orderProducts = "orderProducts" in el ? el.orderProducts : (el as any).products
                    }
                    await PosOrderUtils.checkOrderDataIssue({ ...el, orderProducts });

                    return ({
                        orderId: el.orderId,
                        cash: el.cash || 0,
                        network: el.network || 0,
                        deferred: el.deferred || 0,
                        total: el.total || 0,
                        subTotal: el.subTotal || 0,
                        discount: el.discount || 0,
                        tobaccoTax: el.tobaccoTax || 0,
                        shiftId: el.shiftId,
                        vat: el.vat || 0,
                        tableId: el.table?.id,
                        customerId: el.customer?.id,
                        status: el.status,
                        type: el.type,
                        deliveryApp: el.deliveryApp,
                        deliveryAppFee: el.deliveryAppFee || 0,
                        totalDue: el.totalDue || 0,
                        orderProducts: orderProducts.map((pr) => {
                            let orderProductAdditions: IPosOrderProductAddition[] | undefined = undefined;
                            if (
                                "orderProductAdditions" in pr &&
                                !pr.orderProductAdditions?.some((ad) => !!ad.price && (!ad.total || !ad.subTotal || (!!el.discount && !ad.discount)))
                            )
                                orderProductAdditions = pr.orderProductAdditions;
                            else if ("additions" in pr) orderProductAdditions = (pr as any).additions;

                            return ({
                                productId: pr.product.id,
                                quantity: pr.quantity,
                                price: pr.price,
                                name: pr.name,
                                secondName: pr.secondName,
                                isSubjectToTobaccoTax: pr.isSubjectToTobaccoTax ?? false,
                                subTotal: pr.subTotal ?? 0,
                                discount: pr.discount ?? 0,
                                tobaccoTax: pr.tobaccoTax ?? 0,
                                vat: pr.vat ?? 0,
                                total: pr.total ?? 0,
                                startTime: pr.startTime ?? new Date().getTime(),
                                orderProductAdditions: orderProductAdditions?.map((ad) => {
                                    return {
                                        additionId: ad.addition.id,
                                        quantity: ad.quantity,
                                        price: ad.price,
                                        name: ad.name,
                                        secondName: ad.secondName,
                                        subTotal: ad.subTotal ?? 0,
                                        discount: ad.discount ?? 0,
                                        vat: ad.vat ?? 0,
                                        total: ad.total ?? 0,
                                        startTime: ad.startTime ?? new Date().getTime(),
                                    }
                                })
                            })
                        }),
                        selectedDiscountId: el.selectedDiscount?.id,
                        invoiceNumber: el.invoiceNumber || "",
                        orderNumber: el.orderNumber || "",
                        startTime: new Date(el.startTime || "").getTime(),
                        endTime: new Date(el.endTime || "").getTime(),
                    })
                });
                await PosOrderRepo.uploadOrders(body);
                skip = orders[orders.length - 1].id;
                orders = await PosOrderRepo.getOrders({ limit, skip });
            }
            await PosOrderRepo.deleteAllOrders();
        } catch (error) {
            debug(`PosShiftService [uploadOrders] Error: `, error);
            throw error;
        }
    };

    private static uploadReturnedOrders = async () => {
        try {
            let limit = 50;
            let skip = 0;

            let returnedOrders = await PosReturnedOrdersRepo.getReturnedOrders({ limit, skip });
            while (returnedOrders.length) {
                const body: IReturnedOrderBody[] = returnedOrders.map((el) => {
                    if (!("orderProducts" in el) && !("products" in el)) throw new Error("No order products found");
                    const orderProducts: IPosOrderProduct[] = "orderProducts" in el ? el.orderProducts : (el as any).products;
                    PosOrderUtils.checkOrderDataIssue(el);

                    return ({
                        orderId: el.orderId,
                        cash: el.cash || 0,
                        network: el.network || 0,
                        deferred: el.deferred || 0,
                        total: el.total || 0,
                        subTotal: el.subTotal || 0,
                        discount: el.discount || 0,
                        tobaccoTax: el.tobaccoTax || 0,
                        shiftId: el.shiftId,
                        vat: el.vat || 0,
                        tableId: el.table?.id,
                        customerId: el.customer?.id,
                        type: el.type,
                        deliveryApp: el.deliveryApp,
                        deliveryAppFee: el.deliveryAppFee || 0,
                        totalDue: el.totalDue || 0,
                        returnedOrderProducts: orderProducts.map((pr) => {
                            let orderProductAdditions: IPosOrderProductAddition[] | undefined = undefined;
                            if (
                                "orderProductAdditions" in pr &&
                                !pr.orderProductAdditions?.some((ad) => !!ad.price && (!ad.total || !ad.subTotal || (!!el.discount && !ad.discount)))
                            )
                                orderProductAdditions = pr.orderProductAdditions;
                            else if ("additions" in pr) orderProductAdditions = (pr as any).additions;

                            return ({
                                productId: pr.product.id,
                                quantity: pr.quantity,
                                price: pr.price,
                                name: pr.name,
                                secondName: pr.secondName,
                                isSubjectToTobaccoTax: pr.isSubjectToTobaccoTax ?? false,
                                subTotal: pr.subTotal ?? 0,
                                discount: pr.discount ?? 0,
                                tobaccoTax: pr.tobaccoTax ?? 0,
                                vat: pr.vat ?? 0,
                                total: pr.total ?? 0,
                                startTime: pr.startTime ?? new Date().getTime(),
                                returnedOrderProductAdditions: orderProductAdditions?.map((ad) => {
                                    return {
                                        additionId: ad.addition.id,
                                        quantity: ad.quantity,
                                        price: ad.price,
                                        name: ad.name,
                                        secondName: ad.secondName,
                                        subTotal: ad.subTotal ?? 0,
                                        discount: ad.discount ?? 0,
                                        vat: ad.vat ?? 0,
                                        total: ad.total ?? 0,
                                        startTime: ad.startTime ?? new Date().getTime(),
                                    }
                                })
                            })
                        }),
                        selectedDiscountId: el.selectedDiscount?.id,
                        invoiceNumber: el.invoiceNumber || "",
                        returnedInvoiceNumber: el.returnedInvoiceNumber || "",
                        orderNumber: el.orderNumber || "",
                        startTime: new Date(el.startTime || "").getTime(),
                    })
                });
                await PosReturnedOrdersRepo.uploadReturnedOrders(body);
                skip = returnedOrders[returnedOrders.length - 1].id;
                returnedOrders = await PosReturnedOrdersRepo.getReturnedOrders({ limit, skip });
            }
            await PosReturnedOrdersRepo.deleteAllReturnedOrders();
        } catch (error) {
            debug(`PosShiftService [uploadReturnedOrders] Error: `, error);
            throw error;
        }
    };

    private static uploadPaymentVouchers = async () => {
        try {
            let limit = 50;
            let skip = 0;

            let paymentVouchers = await PosPaymentVoucherRepo.getPaymentVouchers({ limit, skip });
            while (paymentVouchers.length) {
                const body: IPaymentVoucherBody[] = paymentVouchers.map((el) => ({
                    destination: el.destination,
                    date: new Date(el.date).getTime(),
                    cash: el.cash || 0,
                    network: el.network || 0,
                    note: el.note || "",
                    shiftId: el.shiftId,
                }));
                await PosPaymentVoucherRepo.uploadPaymentVouchers(body);
                skip = paymentVouchers[paymentVouchers.length - 1].id;
                paymentVouchers = await PosPaymentVoucherRepo.getPaymentVouchers({ limit, skip });
            }
            await PosPaymentVoucherRepo.deleteAllPaymentVouchers();
        } catch (error) {
            debug(`PosShiftService [uploadPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    private static uploadReceiptVouchers = async () => {
        try {
            let limit = 50;
            let skip = 0;

            let receiptVouchers = await PosReceiptVoucherRepo.getReceiptVouchers({ limit, skip });
            while (receiptVouchers.length) {
                const body: IReceiptVoucherBody[] = receiptVouchers.map((el) => ({
                    destination: el.destination,
                    customerId: el.customer?.id,
                    date: new Date(el.date).getTime(),
                    cash: el.cash || 0,
                    network: el.network || 0,
                    note: el.note || "",
                    shiftId: el.shiftId,
                }));
                await PosReceiptVoucherRepo.uploadReceiptVouchers(body);
                skip = receiptVouchers[receiptVouchers.length - 1].id;
                receiptVouchers = await PosReceiptVoucherRepo.getReceiptVouchers({ limit, skip });
            }
            await PosReceiptVoucherRepo.deleteAllReceiptVouchers();
        } catch (error) {
            debug(`PosShiftService [uploadReceiptVouchers] Error: `, error);
            throw error;
        }
    }

    static endDay = async () => {
        try {
            await PosPausedOrdersRepo.deleteAllPausedOrder();
            await this.uploadShifts();
            await this.uploadOrders();
            await this.uploadReturnedOrders();
            await this.uploadPaymentVouchers();
            await this.uploadReceiptVouchers();
        } catch (error) {
            debug(`PosShiftService [endDay] Error: `, error);
            throw error;
        }
    };
}
