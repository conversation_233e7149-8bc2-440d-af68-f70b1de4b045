import { IReturnedPurchaseInvoiceModel } from "../../../../common/models/ReturnedPurchaseInvoiceModel";
import { IReturnedPurchaseInvoiceSumModel } from "../../../../common/models/ReturnedPurchaseInvoiceSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { ReturnedPurchasesReportPdfPrinterContent } from "../../../../common/pdf-make/slices/returnedPurchasesReport/ReturnedPurchasesReportPdfPrinterContent";
import { IReturnedPurchasesReportPdfPrinterModel } from "../../../../common/pdf-make/slices/returnedPurchasesReport/ReturnedPurchasesReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminReturnedPurchasesReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        returnedPurchaseInvoices: IReturnedPurchaseInvoiceModel[] | undefined,
        returnedPurchaseInvoicesSum: IReturnedPurchaseInvoiceSumModel | undefined
    ) {
        const model: IReturnedPurchasesReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: returnedPurchaseInvoices?.map((el) => ({
                invoiceNumber: el.number.toString(),
                supplierName: el.supplier?.name,
                subTotal: el.subTotal,
                discount: el.productsDiscount,
                vat: el.vat,
                total: el.total,
                date: DateUtils.format(el.date),
            })),
            totals: {
                count: returnedPurchaseInvoicesSum?.count ?? 0,
                subTotal: returnedPurchaseInvoicesSum?.subTotal ?? 0,
                vat: returnedPurchaseInvoicesSum?.vat ?? 0,
                total: returnedPurchaseInvoicesSum?.total ?? 0,
            },
        };
        PdfMakeUtils.download(ReturnedPurchasesReportPdfPrinterContent(model), "Returned Purchases Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }
}
