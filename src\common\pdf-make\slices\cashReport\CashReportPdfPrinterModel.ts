export interface ICashReportPdfPrinterItemModel {
    date: string;
    number: string | number;
    type: string;
    cash: number;
}

export interface ICashReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: ICashReportPdfPrinterItemModel[];
    totals: {
        ordersCount: number;
        returnedCount: number;
        paymentVoucherCount: number;
        receiptVoucherCount: number;
        total: number;
    };
}