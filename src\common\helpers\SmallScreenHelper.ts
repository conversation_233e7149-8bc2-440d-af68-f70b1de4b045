import { posSmallScreenSettingsKeyConstant } from "../constants/ConfigConstants";
import { IPosSmallScreenSettings } from "../interfaces";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class SmallScreenHelper {
    static get(): IPosSmallScreenSettings {
        const smallScreenSettings: IPosSmallScreenSettings = LocalStorageHelper.get(
            posSmallScreenSettingsKeyConstant
        );
        return smallScreenSettings || {
            port: "2400",
            comPort: "COM2",
            isActive: false,
        };
    }

    static set(smallScreenSettings: IPosSmallScreenSettings): void {
        LocalStorageHelper.set(posSmallScreenSettingsKeyConstant, smallScreenSettings);
    }
}