interface IDeliveryAppsReportPdfPrinterItemModel {
    invoiceNumber: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
}

export interface IDeliveryAppsReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    deliveryAppName: string;
    items?: IDeliveryAppsReportPdfPrinterItemModel[];
    totals: {
        count: number;
        vat: number;
        total: number;
        deliveryAppFee: number;
        totalDue: number;
    };
}
