import { ChangeEvent, FC, useEffect, useState } from "react";
import { IoImageOutline } from "react-icons/io5";
import { useTranslate } from "../hooks/useTranslate";
import logo from "/logo.svg";

export interface UploadFileComponentProps {
    className?: string;
    onFileChange?: (file: File | null) => void;
    disabled?: boolean;
    image?: string;
    file?: File | null;
    containerClassName?: string;
    label?: string;
}

const UploadFileComponent: FC<UploadFileComponentProps> = ({
    className = "",
    onFileChange,
    disabled = false,
    image,
    file = null,
    containerClassName = "",
    label,
}) => {
    const { translate } = useTranslate();
    const [fileInput, setFile] = useState<File | null>(file);

    const handelOnChange = (e: ChangeEvent<HTMLInputElement>) => {
        setFile(e.target.files ? e.target.files[0] : null);
        if (onFileChange) {
            onFileChange(e.target.files ? e.target.files[0] : null);
        }
    };

    useEffect(() => { setFile(file) }, [file]);

    return (
        <div className={"flex flex-col h-full w-full sm:w-1/2 md:w-1/3" + " " + containerClassName}>
            {label && (<label className="text-center">{translate(label)}</label>)}
            <div
                className={
                    "h-full w-full border border-gray-400 rounded flex relative cursor-pointer" +
                    " " +
                    className
                }
            >
                <input
                    type="file"
                    accept="image/*"
                    onChange={handelOnChange}
                    className="h-full w-full cursor-pointer absolute z-10 bottom-0.5 left-1 opacity-0"
                    style={{ fontSize: 0 }}
                    disabled={disabled}
                />
                {fileInput || image ? (
                    <img
                        src={fileInput ? URL.createObjectURL(fileInput) : image}
                        alt="upload"
                        className="h-16 my-auto w-full rounded-md object-contain"
                        onError={(e) => {
                            e.currentTarget.src = logo;
                        }}
                    />
                ) : (
                    <IoImageOutline className="m-auto" size={28} />
                )}
            </div>
        </div>
    );
};

export default UploadFileComponent;
