import { FC } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";

interface IProps {
    label?: string;
    onChange?: (val: boolean) => void;
    value?: boolean;
    isError?: boolean;
    containerClassName?: string;
    labelClassName?: string;
    isDisabled?: boolean;
}

const CheckBoxComponent: FC<IProps> = ({
    label,
    onChange,
    value = false,
    isError,
    containerClassName = "",
    labelClassName = "",
    isDisabled = false,
}) => {
    const handleOnChange = (val: boolean) => {
        if (isDisabled) return;
        onChange && onChange(val);
    };

    return (
        <div className={"h-min flex items-center gap-2" + " " + containerClassName}>
            <input
                type="checkbox"
                className="checkbox"
                onChange={(e) => handleOnChange(e.target.checked)}
                checked={value}
                disabled={isDisabled}
            />
            {!!label && (
                <label
                    className={
                        (!isDisabled ? "cursor-pointer hover:underline" : "") +
                        " " +
                        `${isError ? "text-red-400" : ""} ${labelClassName}`
                    }
                    onClick={() => handleOnChange(!value)}
                >
                    {TranslateHelper.t(label)}
                </label>
            )}
        </div>
    );
};

export default CheckBoxComponent;
