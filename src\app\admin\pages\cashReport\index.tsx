import { useMemo, useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminCashReportTableHeaders } from "./AdminCashReportsConstants";
import AdminCashReportTotalInfoFeature from "./features/AdminCashReportTotalInfoFeature";
import { AdminCashReportService } from "./CahsReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { ICashReportInterface } from "./CashReportInterface";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import { ReceiptVouchersApiRepo } from "../../../../common/repos/api/ReceiptVouchersApiRepo";
import { PurchaseInvoicePaymentApiRepo } from "../../../../common/repos/api/PurchaseInvoicePaymentApiRepo";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";

const AdminCashReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-cash-" + EndPointsEnums.ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrdersApiRepo.getSum(startTime, endTime, undefined, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: orders,
        isLoading: ordersLoading,
        isError: ordersError,
        refetch: refetchOrders,
    } = useFetch(
        "report-cash-" + EndPointsEnums.ORDERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrdersApiRepo.getOrders(startTime, endTime, undefined, undefined, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-cash-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime, undefined, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrders,
        isLoading: returnedOrdersLoading,
        isError: returnedOrdersError,
        refetch: refetchReturnedOrders,
    } = useFetch(
        "report-cash-" + EndPointsEnums.RETURNED_ORDERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrdersApiRepo.getReturnedOrders(startTime, endTime, undefined, undefined, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: purchaseInvoicePaymentsSum,
        isLoading: purchaseInvoicePaymentsSumLoading,
        isError: purchaseInvoicePaymentsSumError,
        refetch: refetchPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-cash-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoicePaymentApiRepo.getSum(startTime, endTime, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: purchaseInvoicePayments,
        isLoading: purchaseInvoicePaymentsLoading,
        isError: purchaseInvoicePaymentsError,
        refetch: refetchPurchaseInvoicePayments,
    } = useFetch(
        "report-cash-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoicePaymentApiRepo.getPurchaseInvoicePayments({
            startTime,
            endTime,
            isCash: true,
        }),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: paymentVouchersSum,
        isLoading: paymentVouchersSumLoading,
        isError: paymentVouchersSumError,
        refetch: refetchPaymentVouchersSum,
    } = useFetch(
        "report-cash-" + EndPointsEnums.PAYMENT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PaymentVouchersApiRepo.getSum<IPaymentVoucherSumModel>(startTime, endTime, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: paymentVouchers,
        isLoading: paymentVouchersLoading,
        isError: paymentVouchersError,
        refetch: refetchPaymentVouchers,
    } = useFetch(
        "report-cash-" + EndPointsEnums.PAYMENT_VOUCHERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PaymentVouchersApiRepo.getPaymentVouchers({ startTime, endTime, isCash: true }),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: receiptVouchersSum,
        isLoading: receiptVouchersSumLoading,
        isError: receiptVouchersSumError,
        refetch: refetchReceiptVouchersSum,
    } = useFetch(
        "report-cash-" + EndPointsEnums.RECEIPT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReceiptVouchersApiRepo.getSum(startTime, endTime, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: receiptVouchers,
        isLoading: receiptVouchersLoading,
        isError: receiptVouchersError,
        refetch: refetchReceiptVouchers,
    } = useFetch(
        "report-cash-" + EndPointsEnums.RECEIPT_VOUCHERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReceiptVouchersApiRepo.getReceiptVouchers({ startTime, endTime, isCash: true }),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchOrderSum(start, end);
        refetchOrders(start, end);
        refetchReturnedOrderSum(start, end);
        refetchReturnedOrders(start, end);
        refetchPurchaseInvoicePaymentsSum(start, end);
        refetchPurchaseInvoicePayments(start, end);
        refetchPaymentVouchersSum(start, end);
        refetchPaymentVouchers(start, end);
        refetchReceiptVouchersSum(start, end);
        refetchReceiptVouchers(start, end);
    };

    const totals = useMemo(() =>
        AdminCashReportService.handleTotalInfo(
            orderSum,
            returnedOrderSum,
            purchaseInvoicePaymentsSum,
            paymentVouchersSum,
            receiptVouchersSum,
        ), [
        orderSum,
        returnedOrderSum,
        purchaseInvoicePaymentsSum,
        paymentVouchersSum,
        receiptVouchersSum,
    ]);

    const data = useMemo(() => {
        return AdminCashReportService.handleData(
            orders,
            returnedOrders,
            purchaseInvoicePayments,
            paymentVouchers,
            receiptVouchers,
        );
    }, [
        orders,
        returnedOrders,
        purchaseInvoicePayments,
        paymentVouchers,
        receiptVouchers,
    ]);

    const onDownload = () =>
        AdminCashReportService.handleDownload(dateRange, data, totals);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!data?.length}
                disableSearchButtonByDefault={false}
            />
            <AdminCashReportTotalInfoFeature
                totals={totals}
            />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={
                    orderSumLoading ||
                    ordersLoading ||
                    returnedOrderSumLoading ||
                    returnedOrdersLoading ||
                    purchaseInvoicePaymentsSumLoading ||
                    purchaseInvoicePaymentsLoading ||
                    paymentVouchersSumLoading ||
                    paymentVouchersLoading ||
                    receiptVouchersSumLoading ||
                    receiptVouchersLoading
                }
                isError={
                    orderSumError ||
                    ordersError ||
                    returnedOrderSumError ||
                    returnedOrdersError ||
                    purchaseInvoicePaymentsSumError ||
                    purchaseInvoicePaymentsError ||
                    paymentVouchersSumError ||
                    paymentVouchersError ||
                    receiptVouchersSumError ||
                    receiptVouchersError
                }
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminCashReportTableHeaders}
                    items={data || []}
                    selectors={(item: ICashReportInterface) => [
                        DateUtils.format(item.date),
                        item.number,
                        item.type,
                        item.cash,
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminCashReportPage;
