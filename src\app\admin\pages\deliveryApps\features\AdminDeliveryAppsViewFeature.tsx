import useFetch from "../../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { IDeliveryAppModel } from "../../../../../common/models/DeliveryAppModel";
import { DeliveryAppsApiRepo } from "../../../../../common/repos/api/DeliveryAppsApiRepo";
import { AdminDeliveryAppDataTableHeaders } from "../AdminDeliveryAppsConstants";
import AdminDeliveryAppsModal from "../modals/AdminDeliveryAppsModal";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import useActions from "../../../../../common/redux/data/useActions";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { deliveryAppsConstant } from "../../../../../common/constants/CommonConstants";
import { IDeliveryApp } from "../../../../../common/interfaces";
import { IAdminDeliveryAppInputs } from "../AdminDeliveryAppsInterface";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { FC } from "react";

interface IProps {
    onProducts: (item: IDeliveryApp) => void;
}

const AdminDeliveryAppsViewFeature: FC<IProps> = ({ onProducts }) => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch<
        IDeliveryAppModel | undefined
    >(EndPointsEnums.DELIVERY_APPS, DeliveryAppsApiRepo.getDeliveryApps);

    const handleOnClick = (name: string) => {
        const item: IAdminDeliveryAppInputs = {
            name,
            percentage: (data?.[name as keyof IDeliveryAppModel] || 0) as number,
        };

        actions.openModal({
            title:
                TranslateHelper.t(TranslateConstants.EDIT) +
                " " +
                `(${TranslateHelper.t(name)})`,
            component: <AdminDeliveryAppsModal item={item} />,
            showButtons: false,
        });
    };

    const handleOnProducts = (item: IDeliveryApp) => onProducts(item);

    return (
        <>
            <AddAndFilterComponent onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminDeliveryAppDataTableHeaders}
                    items={deliveryAppsConstant}
                    selectors={(item: IDeliveryApp) => {
                        const percentage =
                            data?.[item.name as keyof IDeliveryAppModel] || 0;
                        const isActive = !!percentage;

                        return [
                            TranslateHelper.t(item.name),
                            percentage ? `%${percentage}` : "-",
                            <span className={isActive ? "text-green-600" : ""}>
                                {TranslateHelper.t(
                                    isActive
                                        ? TranslateConstants.ACTIVE
                                        : TranslateConstants.INACTIVE
                                )}
                            </span>,
                        ];
                    }}
                    imageSelector={(item: IDeliveryApp) => item.image}
                    showEditButton={true}
                    onEdit={(item: IDeliveryApp) => handleOnClick(item.name)}
                    customButtons={[
                        {
                            text: TranslateConstants.PRODUCTS,
                            bgColor: "slate",
                            onClick: (item: IDeliveryApp) => handleOnProducts(item),
                        },
                    ]}
                />
            </StatusComponent>
        </>
    );
};

export default AdminDeliveryAppsViewFeature;
