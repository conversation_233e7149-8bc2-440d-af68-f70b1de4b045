import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import { useTranslate } from "../hooks/useTranslate";

interface IProps {
    className?: string;
    text?: string;
}

const ErrorComponent: FC<IProps> = ({
    className = "",
    text,
}) => {
    const { translate } = useTranslate();

    return (
        <div className={"w-full h-full flex justify-center items-center text-red-500 font-bold" + " " + className}>
            {translate(text || TranslateConstants.ERROR_PLEASE_TRY_AGAIN)}
        </div>
    );
}

export default ErrorComponent;
