import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";
import { DeliveryAppsHelper } from "../../../../common/helpers/DeliveryAppsHelper";
import { IDeliveryAppProductBody } from "../../../../common/interfaces/body/DeliveryAppProductBody";
import { IAdditionModel } from "../../../../common/models/AdditionsModel";
import { IProductModel } from "../../../../common/models/ProductModel";
import {
    IAdminDeliveryAppsAdditionItemProps,
    IAdminDeliveryAppsProductItemProps
} from "./AdminDeliveryAppsInterface";

export const adminDeliveryAppsGetFormattedProducts = (
    products: IProductModel[] | undefined,
    deliveryApp: string,
): IAdminDeliveryAppsProductItemProps[] => {
    const newProducts = products?.flatMap((item): IAdminDeliveryAppsProductItemProps[] => {
        const { key, isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(deliveryApp);
        const { name, price, productSizeType, id, sizes, image, deliveryApps } = item;

        if (productSizeType === ProductSizeTypeEnum.MULTIPLE && sizes?.length) {
            return sizes.map((size) => {
                const sizeDeliveryApps = size.deliveryApps;
                const isActive = (sizeDeliveryApps as any)?.[isActiveKey];
                const priceInDeliveryApp = (sizeDeliveryApps as any)?.[key] || undefined;

                return {
                    image,
                    name: `${name} - ${size.name}`,
                    price: size.price,
                    productSizeType,
                    sizeId: size.id,
                    active: isActive !== undefined && isActive,
                    priceInDeliveryApp,
                }
            });
        }

        const isActive = (deliveryApps as any)?.[isActiveKey];
        const priceInDeliveryApp = (deliveryApps as any)?.[key] || undefined;

        return [{
            image,
            name,
            price,
            productSizeType,
            productId: id,
            active: isActive !== undefined && isActive,
            priceInDeliveryApp,
        }];
    }) ?? [];

    return newProducts;
}

export const adminDeliveryAppsGetFormattedAdditions = (
    additions: IAdditionModel[] | undefined,
    deliveryApp: string,
): IAdminDeliveryAppsAdditionItemProps[] => {
    const newAdditions = additions?.map((item): IAdminDeliveryAppsAdditionItemProps => {
        const { key, isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(deliveryApp);
        const { name, price, id, deliveryApps } = item;

        const isActive = (deliveryApps as any)?.[isActiveKey];
        const priceInDeliveryApp = (deliveryApps as any)?.[key] || undefined;

        return {
            name,
            price,
            additionId: id,
            active: isActive !== undefined && isActive,
            priceInDeliveryApp,
        };
    }) ?? [];

    return newAdditions;
}


const getDeliveryAppData = (
    items: IAdminDeliveryAppsProductItemProps | IAdminDeliveryAppsAdditionItemProps,
    deliveryApp: string
): IDeliveryAppProductBody => {
    const { key, isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(deliveryApp);

    return {
        [key]: items.priceInDeliveryApp,
        [isActiveKey]: items.active,
        productId: "productId" in items ? items.productId : undefined,
        additionId: "additionId" in items ? items.additionId : undefined,
        sizeId: "sizeId" in items ? items.sizeId : undefined,
    };
}

export const adminHandleDeliveryAppProducts = (
    formattedProducts: IAdminDeliveryAppsProductItemProps[],
    productsItems: IProductModel[] | undefined,
    formattedAdditions: IAdminDeliveryAppsAdditionItemProps[],
    additionsItems: IAdditionModel[] | undefined,
    deliveryApp: string,
) => {
    const { key, isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(deliveryApp);

    const products = formattedProducts.filter((item) => {
        if (item.productSizeType !== ProductSizeTypeEnum.FIXED) return false;

        // check if the old product is the same as the new one then skip it
        const oldProduct = productsItems?.find((p) => p.id === item.productId);
        if (!oldProduct) return false;
        const oldDeliveryApps = oldProduct.deliveryApps as any;
        const oldPrice = oldDeliveryApps?.[key];
        const oldActive = oldDeliveryApps?.[isActiveKey];
        const isEqual = oldPrice === item.priceInDeliveryApp && oldActive === item.active;

        return !!item.priceInDeliveryApp && !isEqual;
    });

    const sizes = formattedProducts.filter((item) => {
        if (item.productSizeType !== ProductSizeTypeEnum.MULTIPLE) return false;

        // check if the old size is the same as the new one then skip it
        const oldSize = productsItems?.find((p) => p.sizes.some((s) => s.id === item.sizeId))?.sizes.find((s) => s.id === item.sizeId);
        if (!oldSize) return false;
        const oldDeliveryApps = oldSize.deliveryApps as any;
        const oldPrice = oldDeliveryApps?.[key];
        const oldActive = oldDeliveryApps?.[isActiveKey];
        const isEqual = oldPrice === item.priceInDeliveryApp && oldActive === item.active;

        return !!item.priceInDeliveryApp && !isEqual;
    });

    const additions = formattedAdditions.filter((item) => {
        // check if the old addition is the same as the new one then skip it
        const oldAddition = additionsItems?.find((a) => a.id === item.additionId);
        if (!oldAddition) return false;
        const oldDeliveryApps = oldAddition.deliveryApps as any;
        const oldPrice = oldDeliveryApps?.[key];
        const oldActive = oldDeliveryApps?.[isActiveKey];
        const isEqual = oldPrice === item.priceInDeliveryApp && oldActive === item.active;

        return !!item.priceInDeliveryApp && !isEqual;
    });

    return {
        deliveryProducts: products.map((item) => getDeliveryAppData(item, deliveryApp)),
        deliverySizes: sizes.map((item) => getDeliveryAppData(item, deliveryApp)),
        deliveryAdditions: additions.map((item) => getDeliveryAppData(item, deliveryApp)),
    };
}