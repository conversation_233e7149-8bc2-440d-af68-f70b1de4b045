interface IShiftReportPdfPrinterItemModel {
    ordersCount: number;
    startAmount: number;
    endAmount: number;
    discountAmount: number;
    tobaccoTaxAmount?: number;
    vatAmount: number;
    totalAmount: number;
    cashAmount: number;
    networkAmount: number;
    deliveryAppsAmount: number;
    additionAmount: number;
    shortageAmount: number;
}

export interface IShiftReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IShiftReportPdfPrinterItemModel[];
    totals: {
        ordersCount: number;
        totalAmount: number;
        vatAmount: number;
        additionAmount: number;
        shortageAmount: number;
    };
}
