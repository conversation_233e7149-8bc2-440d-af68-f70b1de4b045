const { execSync } = require("child_process");

const bucket = process.argv[2];

if (!bucket) {
    console.error("❌ Please provide the bucket name");
    console.error("Usage: npm run fire-cors your-bucket-name");
    process.exit(1);
}

const command = `gsutil cors set cors.json gs://${bucket}`;
console.log(`➡️  Running: ${command}`);

try {
    execSync(command, { stdio: "inherit" });
    console.log("✅ CORS configuration applied successfully.");
} catch (err) {
    console.error("❌ Failed to apply CORS:", err.message);
}
