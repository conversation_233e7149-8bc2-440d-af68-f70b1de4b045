interface ISalesReportPdfPrinterItemModel {
    invoiceNumber: string;
    orderType: string;
    subTotal: number;
    discount: number;
    tobaccoTax?: number;
    vat: number;
    total: number;
    cash: number;
    network: number;
    credit: number;
}

export interface ISalesReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: ISalesReportPdfPrinterItemModel[];
    totals: {
        subTotal: number;
        vat: number;
        total: number;
        cash: number;
        network: number;
        credit: number;
    };
}