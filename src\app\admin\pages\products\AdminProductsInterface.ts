import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";
import { IProductSizeModel } from "../../../../common/models/ProductModel";

export interface IAdminProductSizeInputs {
    item?: IProductSizeModel;
    name: string;
    secondName: string;
    price: number;
}

export interface IAdminProductInputs {
    name?: string;
    secondName?: string;
    price?: number;
    image?: File | null;
    categoryId?: number;
    productSizeType: ProductSizeTypeEnum;
    active: boolean;
    isIncludingAdditions: boolean;
    sizes?: IAdminProductSizeInputs[];
    isSubjectToTobaccoTax?: boolean;
}