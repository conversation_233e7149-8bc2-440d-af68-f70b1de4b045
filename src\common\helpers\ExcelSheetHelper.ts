import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { DateUtils } from "../utils/DateUtils";

export class ExcelSheetHelper {
    static async create<T>(options: {
        fileName: string;
        data: T[];
        cols: {
            name: string;
            key: keyof T;
            format?: (val: any) => any;
            width?: number;
        }[];
    }) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet(options.fileName);

        worksheet.columns = options.cols.map((col) => ({
            header: col.name,
            key: String(col.key),
            width: col.width,
        }));

        options.data.forEach((item) => {
            const row = worksheet.addRow(item);
            options.cols.forEach((col) => {
                if (col.format) {
                    row.getCell(String(col.key)).value = col.format(item[col.key]);
                }
            });
        });

        // Style header
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true, color: { argb: "FFFFFF" } };
            cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: "0070C0" },
            };
        });

        worksheet.columns.forEach((column) => {
            let maxLength = 10; // minimum width
            if (typeof column.eachCell === "function") {
                column.eachCell({ includeEmpty: true }, (cell) => {
                    const cellValue = cell.value ? cell.value.toString() : "";
                    maxLength = Math.max(maxLength, cellValue.length);
                });
            }
            column.width = maxLength + 2; // add padding
        });

        // Save file
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer]),
            `${options.fileName +
            " - " +
            DateUtils.format(Date.now(), "yyyy-MM-dd hh-mm A", true)
            }.xlsx`
        );
    }
}
