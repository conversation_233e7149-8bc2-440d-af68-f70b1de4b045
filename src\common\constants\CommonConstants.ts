import hungerStation from '/images/hungerStation.jpg';
import jahez from '/images/jahez.jpg';
import toYou from '/images/toYou.jpg';
import theChefz from '/images/theChefz.jpg';
import keeta from '/images/keeta.jpg';
import ninja from '/images/ninja.jpg';
import meshwarFood from '/images/meshwarFood.jpg';
import locate from '/images/locate.jpg';
import noonFood from '/images/noonFood.jpg';
import marsool from '/images/marsool.jpg';
import { IDeliveryApp } from '../interfaces';
import { DeliveryAppEnum, PaymentsEnum, PaymentVoucherDestinationEnum } from '../enums/DataEnums';
import { TranslateConstants } from './TranslateConstants';

export const AppVersion = "1.1.10";
export const PrinterServerHost = 'http://127.0.0.1:3007';

export const Size = {
    MB: 1024 * 1024,
};

export const TaxPercentage = 0.15;
export const TobaccoTaxPercentage = 2;

export const WhatsAppLink = (message: string) => {
    return `https://api.whatsapp.com/send/?phone=9660552994007&text=${message}&type=phone_number&app_absent=0`;
}

export const deliveryAppsConstant: IDeliveryApp[] = [
    {
        name: DeliveryAppEnum.HUNGER_STATION,
        image: hungerStation
    },
    {
        name: DeliveryAppEnum.JAHEZ,
        image: jahez
    },
    {
        name: DeliveryAppEnum.TO_YOU,
        image: toYou
    },
    {
        name: DeliveryAppEnum.THE_CHEFZ,
        image: theChefz
    },
    {
        name: DeliveryAppEnum.KEETA,
        image: keeta
    },
    {
        name: DeliveryAppEnum.NINJA,
        image: ninja
    },
    {
        name: DeliveryAppEnum.MESHWAR_FOOD,
        image: meshwarFood
    },
    {
        name: DeliveryAppEnum.LOCATE,
        image: locate
    },
    {
        name: DeliveryAppEnum.NOON_FOOD,
        image: noonFood
    },
    {
        name: DeliveryAppEnum.MARSOOL,
        image: marsool
    },
]

export const PaymentMethodsConstant = [
    {
        name: TranslateConstants.CASH,
        value: PaymentsEnum.CASH,
    },
    {
        name: TranslateConstants.BANK,
        value: PaymentsEnum.BANK,
    },
    {
        name: TranslateConstants.CREDIT,
        value: PaymentsEnum.CREDIT,
    },
];

export const VatTypesConstant = [
    {
        name: TranslateConstants.PRICE_INCLUDING_VAT,
        value: true,
    },
    {
        name: TranslateConstants.PRICE_EXCLUDING_VAT,
        value: false,
    },
];

export const VoucherDestinationsConstant = [
    {
        name: TranslateConstants.SALARIES,
        value: PaymentVoucherDestinationEnum.Salaries,
    },
    {
        name: TranslateConstants.PETTY_CASH,
        value: PaymentVoucherDestinationEnum.PettyCash,
    },
    {
        name: TranslateConstants.RENTS,
        value: PaymentVoucherDestinationEnum.Rents,
    },
    {
        name: TranslateConstants.ELECTRICITY_BILLS,
        value: PaymentVoucherDestinationEnum.ElectricityBills,
    },
    {
        name: TranslateConstants.ADMINISTRATIVE_EXPENSES,
        value: PaymentVoucherDestinationEnum.AdministrativeExpenses,
    },
    {
        name: TranslateConstants.EMPLOYEES_ADVANCES,
        value: PaymentVoucherDestinationEnum.EmployeesAdvances,
    },
    {
        name: TranslateConstants.DINING_HALL_EXPENSES,
        value: PaymentVoucherDestinationEnum.DiningHallExpenses,
    },
    {
        name: TranslateConstants.KITCHEN_EXPENSES,
        value: PaymentVoucherDestinationEnum.KitchenExpenses,
    },
];

export const ReceiptVoucherDestinationsConstant = [
    {
        name: TranslateConstants.CUSTOMER,
    },
    {
        name: TranslateConstants.PURCHASES_RETURN,
    },
    ...deliveryAppsConstant,
]
