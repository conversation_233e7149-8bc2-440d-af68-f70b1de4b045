import { FC, useMemo } from "react";
import PosItemCardComponent from "../../../components/PosItemCardComponent";
import { ICategoryModel } from "../../../../../common/models/CategoryModel";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";
import { IPosOrder } from "../../../interface";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { useAppSelector } from "../../../../../common/redux/store";
import { posProductsSelector } from "../../../redux/selector";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";
import { useTranslate } from "../../../../../common/hooks/useTranslate";

interface IProps {
    categories: ICategoryModel[];
    order: IPosOrder;
}

const PosHomeCategoriesFeature: FC<IProps> = ({ categories, order }) => {
    const { isArabic } = useTranslate();
    const posActions = usePosActions();
    const products = useAppSelector(posProductsSelector);

    const handleOnClick = (selectedCategory: ICategoryModel) =>
        PosHomeService.handelSelectCategory(selectedCategory, posActions);

    const handleCategories = useMemo(() => {
        if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
            return categories.filter((category) => {
                return products.some((product) => {
                    const isActiveKey = DeliveryAppsHelper.getDeliverAppKeys(order.deliveryApp as string).isActiveKey;

                    return (
                        product.categoryId === category.id &&
                        (
                            (product.deliveryApps as any)?.[isActiveKey] ||
                            product.sizes?.some((size) => (size.deliveryApps as any)?.[isActiveKey])
                        )
                    )
                });
            });
        }

        return categories;
    }, [categories, order.type]);

    return (
        <>
            {handleCategories.map((category) => (
                <PosItemCardComponent
                    key={category.id}
                    name={(!isArabic && category.secondName) ? category.secondName : category.name}
                    type="category"
                    image={category.image}
                    onClick={() => handleOnClick(category)}
                />
            ))}
        </>
    );
};

export default PosHomeCategoriesFeature;
