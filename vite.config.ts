import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { comlink } from "vite-plugin-comlink";
import { VitePWA } from "vite-plugin-pwa";
import manifestForPlugIn from "./pwa-manifest";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), VitePWA(manifestForPlugIn)],
  worker: {
    plugins: () => [comlink()],
  },
  server: {
    allowedHosts: process.env.NODE_ENV === "development" ? true : undefined,
  },
});
