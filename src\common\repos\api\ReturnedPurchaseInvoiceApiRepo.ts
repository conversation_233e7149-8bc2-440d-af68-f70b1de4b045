import { IAdminReturnedPurchaseInvoiceState } from "../../../app/admin/pages/returnedPurchaseInvoices/AdminReturnedPurchaseInvoiceInterfaces";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IReturnedPurchaseInvoiceBody } from "../../interfaces/body/ReturnedPurchaseInvoiceBody";
import { IReturnedPurchaseInvoiceModel } from "../../models/ReturnedPurchaseInvoiceModel";
import { IReturnedPurchaseInvoiceSumModel } from "../../models/ReturnedPurchaseInvoiceSumModel";
import { debug } from "../../utils/CommonUtils";

export class ReturnedPurchaseInvoiceApiRepo {
    static async getReturnedPurchaseInvoices(options: {
        getPurchaseInvoice?: boolean;
        startTime?: number;
        endTime?: number;
        supplierId?: number;
        search?: string;
        limit?: number;
        getSuppliers?: boolean;
    }) {
        try {
            const res = await AxiosHelper.get<IReturnedPurchaseInvoiceModel[]>(
                EndPointsEnums.RETURNED_PURCHASE_INVOICES,
                {
                    params: {
                        getPurchaseInvoice: options?.getPurchaseInvoice ?? true,
                        startTime: options?.startTime,
                        endTime: options?.endTime,
                        supplierId: options?.supplierId,
                        search: options?.search,
                        limit: options?.limit,
                        getSuppliers: options?.getSuppliers ?? true,
                    },
                }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(
                `ReturnedPurchaseInvoiceApiRepo [getReturnedPurchaseInvoices] Error: `,
                error
            );
            throw error;
        }
    }

    static async addReturnedPurchaseInvoice(
        body: IAdminReturnedPurchaseInvoiceState,
    ) {
        try {
            const formattedBody: IReturnedPurchaseInvoiceBody = {
                nativeTotal: body.nativeTotal,
                productsDiscount: body.productsDiscount,
                subTotal: body.subTotal,
                vat: body.vat,
                total: body.total,
                note: body.note,
                purchaseInvoiceId: body.purchaseInvoice?.id ?? 0,
                returnedPurchaseInvoiceProducts: body.returnedPurchaseInvoiceProducts
                    .filter((el) => el.quantity && el.quantity > 0)
                    .map((el) => ({
                        purchaseInvoiceProductId: el.purchaseInvoiceProductId,
                        name: el.name,
                        isTaxable: el.isTaxable,
                        quantity: el.quantity,
                        price: el.price,
                        discount: el.discount,
                        vat: el.vat,
                        total: el.total,
                        subTotal: el.subTotal,
                    })),
                date: new Date().getTime(),
                supplierId: body.purchaseInvoice?.supplier?.id ?? 0,
            };

            const res = await AxiosHelper.post<IReturnedPurchaseInvoiceModel>(
                EndPointsEnums.RETURNED_PURCHASE_INVOICES,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(
                `ReturnedPurchaseInvoiceApiRepo [addReturnedPurchaseInvoice] Error: `,
                error
            );
            throw error;
        }
    }

    static async getSum(startTime?: number, endTime?: number, supplierId?: number) {
        try {
            const res = await AxiosHelper.get<IReturnedPurchaseInvoiceSumModel>(
                EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
                { params: { startTime, endTime, supplierId } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReturnedPurchaseInvoiceApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}
