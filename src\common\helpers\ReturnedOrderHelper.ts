import { returnedInvoiceNumberKeyConstant } from "../constants/ConfigConstants";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class ReturnedOrderHelper {
    static getNextReturnedInvoiceNumber(): string {
        const invoiceNumber: number = LocalStorageHelper.get(returnedInvoiceNumberKeyConstant) || 0;
        return (invoiceNumber + 1).toString();
    }

    static setReturnedInvoiceNumber(returnedInvoiceNumber: number): void {
        LocalStorageHelper.set(returnedInvoiceNumberKeyConstant, returnedInvoiceNumber);
    }

    static incrementReturnedInvoiceNumber(): void {
        const invoiceNumber: number = parseInt(this.getNextReturnedInvoiceNumber()) - 1;
        this.setReturnedInvoiceNumber(invoiceNumber + 1);
    }
}