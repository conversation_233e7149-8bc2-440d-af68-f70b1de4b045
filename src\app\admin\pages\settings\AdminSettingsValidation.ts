import { Size } from "../../../../common/constants/CommonConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IOrganizationSensitiveModel } from "../../../../common/models/OrganizationModel";
import { IAdminSettingsInputs } from "./AdminSettingsInterface";

export class AdminSettingsValidation {
    static validate(
        state: IAdminSettingsInputs,
    ) {
        let isValid = true;

        if (!state.name || state.name.length > 30) {
            ToastHelper.error(TranslateConstants.ORGANIZATION_NAME_AR_ERROR)
            isValid = false;
        } else if (!state.nameEn || state.nameEn.length > 30) {
            ToastHelper.error(TranslateConstants.ORGANIZATION_NAME_EN_ERROR)
            isValid = false;
        } else if (!!state.password && state.password.length < 8) {
            ToastHelper.error(TranslateConstants.PASSWORD_SIZE_ERROR)
            isValid = false;
        } else if (!!state.taxNumber && state.taxNumber.length > 15) {
            ToastHelper.error(TranslateConstants.TAX_NUMBER_SIZE_ERROR)
            isValid = false;
        } else if (!!state.registrationNumber && state.registrationNumber.length > 15) {
            ToastHelper.error(TranslateConstants.REGISTRATION_NUMBER_SIZE_ERROR)
            isValid = false;
        } else if (!!state.licenseNumber && state.licenseNumber.length > 15) {
            ToastHelper.error(TranslateConstants.LICENSE_NUMBER_SIZE_ERROR)
            isValid = false;
        } else if (!!state.mobile && state.mobile.length > 20) {
            ToastHelper.error(TranslateConstants.MOBILE_SIZE_ERROR)
            isValid = false;
        } else if (!!state.address && state.address.length > 100) {
            ToastHelper.error(TranslateConstants.ADDRESS_SIZE_ERROR)
            isValid = false;
        } else if (!!state.subName && state.subName.length > 30) {
            ToastHelper.error(TranslateConstants.SUB_NAME_AR_SIZE_ERROR)
            isValid = false;
        } else if (!!state.subNameEn && state.subNameEn.length > 30) {
            ToastHelper.error(TranslateConstants.SUB_NAME_EN_SIZE_ERROR)
            isValid = false;
        } else if (!state.adminPassword || state.adminPassword.length < 4 || state.adminPassword.length > 8) {
            ToastHelper.error(TranslateConstants.ADMIN_PASSWORD_SIZE_ERROR)
            isValid = false;
        } else if (!state.posPassword || state.posPassword.length < 4 || state.posPassword.length > 8) {
            ToastHelper.error(TranslateConstants.POS_PASSWORD_SIZE_ERROR)
            isValid = false;
        } else if (isNaN(Number(state.adminPassword)) || isNaN(Number(state.posPassword))) {
            ToastHelper.error(TranslateConstants.ADMIN_PASSWORD_AND_POS_PASSWORD_MUST_BE_NUMBERS)
            isValid = false;
        } else if (state.adminPassword === state.posPassword) {
            ToastHelper.error(TranslateConstants.ADMIN_PASSWORD_AND_POS_PASSWORD_ARE_THE_SAME)
            isValid = false;
        } else if (!!state.logo && state.logo.size > Size.MB) {
            ToastHelper.error(TranslateConstants.IMAGE_SIZE_MUST_BE_LESS_THAN_1_MB)
            isValid = false;
        } else if (!!state.invoiceFooter && state.invoiceFooter.length > 50) {
            ToastHelper.error(TranslateConstants.INVOICE_FOOTER_SIZE_ERROR)
            isValid = false;
        }

        return isValid;
    }

    static checkDeeplyEqual(
        values: IAdminSettingsInputs,
        organizationSensitives?: IOrganizationSensitiveModel
    ) {
        const organization = OrganizationHelper.getOrganization();
        if (!organization) return false;

        const isNameEqual = !values.name || values.name === organization.name;
        const isNameEnEqual = !values.nameEn || values.nameEn === organization.nameEn;
        const isSubNameEqual = !values.subName || values.subName === organization.subName;
        const isSubNameEnEqual = !values.subNameEn || values.subName === organization.subNameEn;
        const isPasswordEqual = !values.password;
        const isTaxNumberEqual = !values.taxNumber || values.taxNumber === organization.taxNumber;
        const isRegistrationNumberEqual = !values.registrationNumber || values.registrationNumber === organization.registrationNumber;
        const isLicenseNumberEqual = !values.licenseNumber || values.licenseNumber === organization.licenseNumber;
        const isMobileEqual = !values.mobile || values.mobile === organization.mobile;
        const isAddressEqual = !values.address || values.address === organization.address;
        const isLogoEqual = !values.logo;
        const isAdminPasswordEqual = !values.adminPassword || values.adminPassword === organizationSensitives?.adminPassword;
        const isPosPasswordEqual = !values.posPassword || values.posPassword === organizationSensitives?.posPassword;
        const isTobaccoTaxEqual = values.activateTobaccoTax === organization.activateTobaccoTax;
        const isInvoiceFooterEqual = !values.invoiceFooter || values.invoiceFooter === organization.invoiceFooter;

        return (
            isNameEqual &&
            isNameEnEqual &&
            isSubNameEqual &&
            isSubNameEnEqual &&
            isPasswordEqual &&
            isTaxNumberEqual &&
            isRegistrationNumberEqual &&
            isLicenseNumberEqual &&
            isMobileEqual &&
            isAddressEqual &&
            isLogoEqual &&
            isAdminPasswordEqual &&
            isPosPasswordEqual &&
            isTobaccoTaxEqual &&
            isInvoiceFooterEqual
        );
    }
}