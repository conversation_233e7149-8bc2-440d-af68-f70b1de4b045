import { LuForward } from "react-icons/lu";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { FC, useMemo } from "react";
import {
    OrderStatusEnum,
    OrderTypeEnum,
} from "../../../../../common/enums/DataEnums";
import { IPosOrder, PosHomeViewType } from "../../../interface";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";

interface IProps {
    view: PosHomeViewType;
    order: IPosOrder;
}

const PosHomeBackButtonFeature: FC<IProps> = ({ view, order }) => {
    const posActions = usePosActions();
    const handleOnClick = () =>
        PosHomeService.handleBackButton(view, order, posActions);

    const hideButton = useMemo(() => {
        const tablePage = view === "tables";
        const categoryPage =
            view === "categories" &&
            (order.type === OrderTypeEnum.TAKE_AWAY || order.status !== OrderStatusEnum.PENDING);
        const deliveryAppsPage = view === "delivery_apps";
        return tablePage || categoryPage || deliveryAppsPage;
    }, [view, order.type]);

    if (hideButton) return null;

    return (
        <IconButtonComponent
            icon={<LuForward />}
            iconSize="text-4xl"
            text={TranslateConstants.BACK}
            textSize="text-lg"
            bgColor="slate"
            className="min-h-40"
            onClick={handleOnClick}
        />
    );
};

export default PosHomeBackButtonFeature;
