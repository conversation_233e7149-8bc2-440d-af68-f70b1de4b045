import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminReturnedPurchaseInvoiceState } from "./AdminReturnedPurchaseInvoiceInterfaces";

export class AdminReturnedPurchaseInvoiceValidation {
    static inputsValidation = (values: IAdminReturnedPurchaseInvoiceState) => {
        let isValid = true;

        if (
            !values.returnedPurchaseInvoiceProducts.length ||
            values.returnedPurchaseInvoiceProducts.every((el) =>
                !el.quantity || Number(el.quantity) <= 0 ||
                !el.total || Number(el.total) < 0
            ) ||
            values.returnedPurchaseInvoiceProducts.some((el) =>
                [0, undefined].includes(el.price) || Number(el.price) < 0 ||
                Number(el.discount) < 0
            )
        ) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRODUCTS_FIELD_IS_NOT_VALID);
        }

        return isValid;
    };
}
