import { useState } from "react";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { FullScreenHelper } from "../../../../../common/helpers/FullScreenHelper";
import PosBackButtonComponent from "../../../components/PosBackButtonComponent";
import { BiExitFullscreen } from "react-icons/bi";
import { RiFullscreenFill } from "react-icons/ri";
import { isDesktop } from "react-device-detect";

const PosSettingsMenuFeature = () => {
    const [isFullScreen, setIsFullScreen] = useState(FullScreenHelper.isFullScreen());

    const handleFullScreen = () => {
        FullScreenHelper.toggle();
        setIsFullScreen(!isFullScreen);
    }

    return (
        <div className="flex justify-between items-center w-full">
            <PosBackButtonComponent />
            {
                isDesktop && (
                    <IconButtonComponent
                        icon={isFullScreen ? <BiExitFullscreen /> : <RiFullscreenFill />}
                        iconSize="text-4xl"
                        text={TranslateConstants.FULL_SCREEN}
                        textSize="text-lg"
                        bgColor="primary"
                        className="min-h-28 !w-1/6 rounded-xl"
                        onClick={handleFullScreen}
                    />
                )
            }
        </div>
    );
};

export default PosSettingsMenuFeature;
