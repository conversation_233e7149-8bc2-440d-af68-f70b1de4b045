import useFetch from "../../../../../common/asyncController/useFetch";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import { CachedKeysEnums } from "../../../../../common/enums/CachedKeysEnums";
import { PrintersHelper } from "../../../../../common/helpers/PrintersHelper";
import { IPosReturnedOrderModel } from "../../../../../common/models/PosReturnedOrderModel";
import useActions from "../../../../../common/redux/data/useActions";
import { getOrderType } from "../../../../../common/utils/CommonUtils";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { PosReturnedOrdersRepo } from "../../../repo/PosReturnedOrdersRepo";
import { PosReturnedInvoicesTableHeaderConstants } from "../PosInvoicesConstants";
import { PosInvoicesService } from "../PosInvoicesService";

const PosReturnedInvoicesFeature = () => {
    const actions = useActions();
    const { data, isLoading, isError } = useFetch(
        CachedKeysEnums.POS_RETURNED_INVOICES,
        PosReturnedOrdersRepo.getShiftReturnedOrders,
        { autoFetchOnMount: true }
    );
    const { isInvoicePrintingActive } = PrintersHelper.getPrintingSettings();

    const handleOnPrint = async (order: IPosReturnedOrderModel) =>
        PosInvoicesService.handleOnPrintReturned(actions, order);

    return (
        <StatusComponent
            isLoading={isLoading}
            isError={isError}
            isEmpty={!data || data.length === 0}
            height={13}
        >
            <TableComponent
                headers={PosReturnedInvoicesTableHeaderConstants}
                items={data || []}
                selectors={(item: IPosReturnedOrderModel) => {
                    return [
                        item.returnedInvoiceNumber,
                        item.invoiceNumber,
                        getOrderType(item),
                        item.table?.name,
                        item.customer?.name,
                        item.customer?.mobile,
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        item.deferred.toFixed(2),
                        item.total.toFixed(2),
                        DateUtils.format(item.createdAt),
                    ]
                }}
                showPrintButton={isInvoicePrintingActive}
                onPrint={handleOnPrint}
            />
        </StatusComponent>
    );
};

export default PosReturnedInvoicesFeature;
