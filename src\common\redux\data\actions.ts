import { IModalProps } from "../../components/ModalComponent";
import IState from "./interface";
import { initialModalState } from "./state";

export const setModal = (
    state: IState,
    action: {
        payload: Partial<IModalProps>;
        type: string;
    }
) => {
    const modal = action.payload;

    Object.keys(modal).forEach((key) => {
        if (key in state.modal) {
            (state.modal as any)[key] = modal[key as keyof IModalProps];
        }
    });
};

export const closeModal = (state: IState) => {
    state.modal = initialModalState;
};

export const setLoading = (
    state: IState,
    action: {
        payload: boolean;
        type: string;
    }
) => {
    state.loading = action.payload;
};
