import { TranslateConstants } from "../constants/TranslateConstants";
import useScreenSize from "../hooks/useScreenSize";
import { langList, useTranslate } from "../hooks/useTranslate";
import { setModalAction } from "../redux/data/slice";
import useActions from "../redux/data/useActions";
import { dispatchAction } from "../redux/store";
import ButtonComponent from "./ButtonComponent";
import { MdOutlineGTranslate } from "react-icons/md";

export const ShowLanguageModal = () => {
    dispatchAction(
        setModalAction({
            title: TranslateConstants.LANGUAGE,
            show: true,
            component: <LanguageModalComponent />,
            size: "sm",
        })
    );
};

const LanguageModalComponent = () => {
    const actions = useActions();
    const { code, switchLang } = useTranslate();

    return (
        <div className="grid grid-cols-2 gap-2 px-2" dir="rtl">
            {langList.map((lang, index) => (
                <div
                    key={index}
                    className={"flex flex-col items-center border p-2 rounded cursor-pointer active:scale-95" + " " +
                        (lang.code === code ? "border-gray-400 bg-gray-200 dark:bg-gray-100" : "border-gray-300")
                    }
                    onClick={() => {
                        switchLang(lang.code);
                        actions.closeModal();
                    }}
                >
                    <img src={lang.image} alt={lang.name} className="h-4/5 w-full" />
                    <span className="font-tajawal-bold mt-1">{lang.name}</span>
                </div>
            ))}
        </div>
    );
};

export const LanguageButtonComponent = () => {
    const { isXs } = useScreenSize();
    return (
        <ButtonComponent
            onClick={ShowLanguageModal}
            text={isXs ? undefined : TranslateConstants.LANGUAGE}
            iconComponent={<MdOutlineGTranslate className="text-2xl sm:text-lg" />}
            bgColor="white"
            textColor="black"
            className="!w-min"
        />
    );
}
