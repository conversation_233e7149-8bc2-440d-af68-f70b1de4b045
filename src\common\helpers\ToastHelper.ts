import { toast } from "react-toastify";
import { TranslateConstants } from "../constants/TranslateConstants";
import { TranslateHelper } from "./TranslateHelper";

export class ToastHelper {
    static success(message?: string) {
        const translatedMessage = message || TranslateConstants.SUCCESS_TOAST;
        toast.success(TranslateHelper.t(translatedMessage), { toastId: "1", updateId: "1" });
    }

    static error(message?: string) {
        const translatedMessage = message || TranslateConstants.ERROR_TOAST;
        toast.error(TranslateHelper.t(translatedMessage), { toastId: "1", updateId: "1" });
    }

    static warning(message: string) {
        toast.warning(TranslateHelper.t(message), { toastId: "1", updateId: "1" });
    }

    static info(message: string) {
        toast.info(TranslateHelper.t(message), { toastId: "1", updateId: "1" });
    }

    static mutated(isEdit: boolean) {
        const successMessage = isEdit
            ? TranslateConstants.EDITED_SUCCESSFULLY
            : TranslateConstants.ADDED_SUCCESSFULLY;
        ToastHelper.success(successMessage);
    }
}
