import ListComponent from "../../../../common/components/ListComponent";
import PosQuickSettingsFeature from "./features/PosQuickSettingsFeature";
import PosPrinterSettingsFeature from "./features/PosPrinterSettingsFeature";
import PosSettingsMenuFeature from "./features/PosSettingsMenuFeature";

const PosSettingsPage = () => {
    return (
        <div className="flex h-s p-4 gap-4 text-slate-500">
            <div className="col-span-7 flex-1">
                <ListComponent
                    calcHeight={9}
                    padding="0"
                    space="1"
                    allowScrollBar={false}
                >
                    <PosPrinterSettingsFeature />
                </ListComponent>
                <div className="flex gap-4">
                    <PosSettingsMenuFeature />
                </div>
            </div>
            <div className="flex flex-col w-2/6 bg-white rounded-2xl shadow p-2 text-center gap-2">
                <PosQuickSettingsFeature />
            </div>
        </div>
    );
};

export default PosSettingsPage;
