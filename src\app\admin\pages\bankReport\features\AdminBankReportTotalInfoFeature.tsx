import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IBankReportTotalInfoInterface } from "../AdminBankReportInterface";

interface IProps {
    totals: IBankReportTotalInfoInterface;
}

const AdminBankReportTotalInfoFeature: FC<IProps> = ({
    totals,
}) => {
    return (
        <ReportTotalInfoComponent
            items={[
                {
                    text: TranslateConstants.SALES_COUNT,
                    value: totals?.ordersCount,
                    fixedVal: 0,
                },
                {
                    text: TranslateConstants.RETURNED_ORDERS_COUNT,
                    value: totals?.returnedCount,
                    fixedVal: 0,
                },
                {
                    text: TranslateConstants.PAYMENT_VOUCHERS,
                    value: totals?.paymentVoucherCount,
                    fixedVal: 0,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.RECEIPT_VOUCHERS,
                    value: totals?.receiptVoucherCount,
                    fixedVal: 0,
                    isHiddenInSm: true,
                },
                { text: TranslateConstants.TOTAL, value: totals?.total },
            ]}
        />
    );
};

export default AdminBankReportTotalInfoFeature;
