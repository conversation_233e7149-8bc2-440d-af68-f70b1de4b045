import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ITableModel } from "../../../common/models/TableModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosTablesRepo {
    static async getAndCacheTables(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<ITableModel[]>(
                EndPointsEnums.TABLES,
                { params: { updatedAt } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt) await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.TABLES, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.TABLES, data, true);
        } catch (error) {
            debug(`PosTablesRepo [getAndCacheTables] Error: `, error);
            throw error;
        }
    }

    static async getTables(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<ITableModel>(
                APP_LOCAL_DB_COLLECTIONS.TABLES,
                { where: activeOnly ? [[["active", "==", true]]] : undefined }
            );

            if (!res) {
                throw new Error("No tables found");
            }

            return { data: res };
        } catch (error) {
            debug(`PosTablesRepo [getTables] Error: `, error);
            throw error;
        }
    }

    static getOneTable(id: number) {
        try {
            const res = AppLocalDB.getOneById<ITableModel>(
                APP_LOCAL_DB_COLLECTIONS.TABLES,
                id
            );

            if (!res) {
                throw new Error("Table not found");
            }

            return res;
        } catch (error) {
            debug(`PosTablesRepo [getOneTable] Error: `, error);
            throw error;
        }
    }

    static async updateTable(
        id: number,
        body: Partial<ITableModel>
    ): Promise<ITableModel> {
        try {
            const table = await this.getOneTable(id);

            if (!table) {
                throw new Error("Table not found");
            }

            const data: ITableModel = { ...table, ...body };

            const res = await AppLocalDB.update(
                APP_LOCAL_DB_COLLECTIONS.TABLES,
                id,
                data
            );

            if (!res) {
                throw new Error("Table not updated");
            }

            return data;
        } catch (error) {
            debug(`PosTablesRepo [updateTable] Error: `, error);
            throw error;
        }
    }
}
