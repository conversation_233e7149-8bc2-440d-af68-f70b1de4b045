import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { IPurchaseInvoicePaymentModel } from "../../../../common/models/PurchaseInvoicePaymentModel";
import { IReturnedPurchaseInvoiceModel } from "../../../../common/models/ReturnedPurchaseInvoiceModel";
import { IAdminSuppliersReportDateInterface } from "./AdminSuppliersReportsInterface";

export class AdminSuppliersReportService {
    static handleData(
        purchaseInvoices: IPurchaseInvoiceModel[] = [],
        returnedPurchaseInvoices: IReturnedPurchaseInvoiceModel[] = [],
        purchaseInvoicePayments: IPurchaseInvoicePaymentModel[] = [],
    ): IAdminSuppliersReportDateInterface[] {
        const data = [
            ...purchaseInvoices.map((el) => ({
                ...el,
                type: TranslateHelper.t(TranslateConstants.PURCHASE_INVOICE) + " - " + el.number,
                debit: 0,
                credit: el.total,
            })),
            ...returnedPurchaseInvoices.map((el) => ({
                ...el,
                type: TranslateHelper.t(TranslateConstants.RETURNED_PURCHASE_INVOICE) + " - " + el.number,
                debit: el.total,
                credit: 0,
            })),
            ...purchaseInvoicePayments.map((el) => ({
                ...el,
                total: el.cash + el.network,
                type: TranslateHelper.t(TranslateConstants.PURCHASE_INVOICE_PAYMENT) + " - " + el.purchaseInvoiceId,
                debit: el.cash + el.network,
                credit: 0,
            })),
        ].sort((a, b) => a.date - b.date);

        const result: IAdminSuppliersReportDateInterface[] = []

        data.forEach((el) => {
            if (!result.length) {
                result.push({ ...el, deferred: el.total });
                return;
            }

            const last = result[result.length - 1];

            if(el.credit > 0) {
                result.push({ ...el, deferred: last.deferred + el.credit });
                return;
            }

            result.push({ ...el, deferred: last.deferred - el.debit });
        });

        return result;
    }
}
