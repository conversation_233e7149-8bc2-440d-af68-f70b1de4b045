import { PaymentsEnum } from "../../enums/DataEnums";

export interface IPurchaseOrderProductBody {
    id?: number;
    rawMaterialId: number;
    isTaxable: boolean;
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IPurchaseOrderBody {
    date: number;
    dueDate: number;
    paymentMethod: PaymentsEnum;
    supplierId: number;
    isPriceIncludingTax: boolean;
    purchaseOrderProducts: IPurchaseOrderProductBody[];
    note: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
}
