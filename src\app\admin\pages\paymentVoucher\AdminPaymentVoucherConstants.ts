import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { PaymentVoucherDestinationEnum } from "../../../../common/enums/DataEnums";
import { IAdminPaymentVoucherInputs } from "./AdminPaymentVoucherInterface";

export const AdminPaymentVoucherInputs = (): IAdminPaymentVoucherInputs => ({
    destination: PaymentVoucherDestinationEnum.Salaries,
    date: new Date().getTime(),
    cash: 0,
    network: 0,
    note: "",
});

export const AdminPaymentVoucherDataTableHeaders = [
    TranslateConstants.NUMBER,
    TranslateConstants.EXPENSE_DESTINATION,
    TranslateConstants.CASH,
    TranslateConstants.NETWORK,
    TranslateConstants.DATE,
    TranslateConstants.STATEMENT,
];
