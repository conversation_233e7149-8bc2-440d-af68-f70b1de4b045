import { FC } from "react";
import { IAdminRawMaterialInputs } from "../AdminRawMaterialsInterface";
import { AdminRawMaterialInputs } from "../AdminRawMaterialsConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminRawMaterialsValidation } from "../AdminRawMaterialsValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import { IRawMaterialModel } from "../../../../../common/models/RawMaterialModel";
import { RawMaterialApiRepo } from "../../../../../common/repos/api/RawMaterialApiRepo";
import { fixedNumber } from "../../../../../common/utils/numberUtils";
import { SuppliersApiRepo } from "../../../../../common/repos/api/SupplierApiRepo";
import useFetch from "../../../../../common/asyncController/useFetch";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import { ISupplierModel } from "../../../../../common/models/SuppliersModel";

interface IProps {
    item?: IRawMaterialModel;
}

const AdminRawMaterialsModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminRawMaterialInputs>(
        AdminRawMaterialInputs,
        { updateState: item }
    );

    const {
        data: suppliers,
        isLoading,
        isError,
        refetch,
    } = useFetch(
        EndPointsEnums.SUPPLIERS,
        (search, limit = 5) => SuppliersApiRepo.getSuppliers(search, limit)
    );

    const addRawMaterial = useFlatMutate(RawMaterialApiRepo.addRawMaterial, {
        updateCached: { key: EndPointsEnums.RAW_MATERIALS, operation: "add" },
        afterEnd: () => resetState(),
    });

    const updateRawMaterial = useFlatMutate(
        RawMaterialApiRepo.updateRawMaterial,
        {
            updateCached: {
                key: EndPointsEnums.RAW_MATERIALS,
                operation: "update",
                selector: (data: IRawMaterialModel) => data.id,
            },
            closeModalOnSuccess: true,
        }
    );

    const onClick = () => {
        const isValid = AdminRawMaterialsValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateRawMaterial(item.id, state);
        addRawMaterial(state);
    };

    return (
        <>
            <form className="px-2">
                <DropDownSearchComponent
                    label={TranslateConstants.SUPPLIER}
                    onSelect={(item) => setState({ ...state, supplierId: item.id })}
                    onChange={(search) => refetch(search)}
                    isSearchable={true}
                    isOutFilter={true}
                    showSearchIcon={true}
                    showViewButton={false}
                    isLoading={isLoading}
                    isError={isError}
                    items={suppliers || []}
                    titleSelector={(item: ISupplierModel) => item.name}
                    subtitleSelector={(item: ISupplierModel) => item.mobile}
                    defaultValue={item?.supplier?.name || ""}
                />
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(value) => setState({ ...state, name: value })}
                    value={state.name}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.PRICE}
                    onChange={(value) =>
                        setState({ ...state, price: fixedNumber(value) })
                    }
                    value={state.price.toString()}
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminRawMaterialsModal;
