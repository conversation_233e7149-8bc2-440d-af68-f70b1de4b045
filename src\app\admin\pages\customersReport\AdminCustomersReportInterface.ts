import { IOrderModel } from "../../../../common/models/OrderModel";

export interface ICustomersReportDataInterface extends IOrderModel {
    isReturned: boolean;
    customNumber: string;
    operationType: string;
}

export interface ICustomersReportTotalInfoInterface {
    count: number;
    returnedCount: number;
    vat: number;
    totalReturned: number;
    cash: number;
    network: number;
    pay: number;
    credit: number;
    total: number;
}