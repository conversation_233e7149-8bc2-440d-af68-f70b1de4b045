import { BaseModel } from ".";
import { ReceiptVoucherDestinationEnum } from "../enums/DataEnums";
import { ICustomerModel } from "./CustomerModel";

export interface IReceiptVoucherModel extends BaseModel {
    number: number;
    destination?: typeof ReceiptVoucherDestinationEnum;
    customer?: ICustomerModel;
    shiftId?: number;
    date: number;
    cash: number;
    network: number;
    note?: string;
}