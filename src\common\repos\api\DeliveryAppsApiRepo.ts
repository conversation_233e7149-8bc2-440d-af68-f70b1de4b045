import { IAdminDeliveryAppInputs } from "../../../app/admin/pages/deliveryApps/AdminDeliveryAppsInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IDeliveryAppModel } from "../../models/DeliveryAppModel";
import { debug } from "../../utils/CommonUtils";

export class DeliveryAppsApiRepo {
    static async getDeliveryApps() {
        try {
            const res = await AxiosHelper.get<IDeliveryAppModel | undefined>(EndPointsEnums.DELIVERY_APPS);
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DeliveryAppsApiRepo [getDeliveryApps] Error: `, error);
            throw error;
        }
    }

    static async editDeliveryApp(inputs: IAdminDeliveryAppInputs) {
        try {
            const body = {
                [inputs.name]: inputs.percentage,
            }
            const res = await AxiosHelper.post<IDeliveryAppModel>(EndPointsEnums.DELIVERY_APPS, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DeliveryAppsApiRepo [editDeliveryApp] Error: `, error);
            throw error;
        }
    }
}
