import { useMemo } from "react";
import useFetch from "../../../../../common/asyncController/useFetch";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import { CachedKeysEnums } from "../../../../../common/enums/CachedKeysEnums";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { IPaymentVoucherModel } from "../../../../../common/models/PaymentVoucherModel";
import useActions from "../../../../../common/redux/data/useActions";
import { maxStringLength } from "../../../../../common/utils/CommonUtils";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { PosPaymentVoucherRepo } from "../../../repo/PosPaymentVoucherRepo";
import { PosReceiptVoucherRepo } from "../../../repo/PosReceiptVoucherRepo";
import { PosVoucherDataTableHeaders } from "../PosInvoicesConstants";
import { PosInvoicesService } from "../PosInvoicesService";
import { IReceiptVoucherModel } from "../../../../../common/models/ReceiptVoucherModel";
import { PaymentVoucherDestinationEnum } from "../../../../../common/enums/DataEnums";
import { PrintersHelper } from "../../../../../common/helpers/PrintersHelper";

const PosVouchersFeature = () => {
    const actions = useActions();
    const { isVouchersPrintingActive } = PrintersHelper.getPrintingSettings();
    const {
        data: paymentVouchers,
        isLoading: paymentVouchersLoading,
        isError: paymentVouchersError,
    } = useFetch(
        CachedKeysEnums.POS_PAYMENT_VOUCHERS,
        PosPaymentVoucherRepo.getShiftPaymentVouchers,
        { autoFetchOnMount: true }
    );
    const {
        data: receiptVouchers,
        isLoading: receiptVouchersLoading,
        isError: receiptVouchersError,
    } = useFetch(
        CachedKeysEnums.POS_RECEIPT_VOUCHERS,
        PosReceiptVoucherRepo.getShiftReceiptVouchers,
        { autoFetchOnMount: true }
    );

    const data = useMemo(() => {
        return [
            ...(paymentVouchers || []),
            ...(receiptVouchers || []),
        ].sort((a, b) => b.date - a.date);
    }, [paymentVouchers, receiptVouchers]);

    const handleOnPrint = async (voucher: IPaymentVoucherModel | IReceiptVoucherModel) => {
        if (voucher.destination === PaymentVoucherDestinationEnum.pullFromShift) {
            PosInvoicesService.handleOnPrintPaymentVoucher(actions, voucher as IPaymentVoucherModel);
        } else {
            PosInvoicesService.handleOnPrintReceiptVoucher(actions, voucher as IReceiptVoucherModel);
        }
    }

    return (
        <StatusComponent
            isLoading={paymentVouchersLoading || receiptVouchersLoading}
            isError={paymentVouchersError || receiptVouchersError}
            isEmpty={!data.length}
            height={13}
        >
            <TableComponent
                headers={PosVoucherDataTableHeaders}
                items={data || []}
                selectors={(item: IPaymentVoucherModel | IReceiptVoucherModel) => {
                    return [
                        TranslateHelper.t(item.destination),
                        'customer' in item ? item.customer?.name : '',
                        'customer' in item ? item.customer?.mobile : '',
                        item.cash,
                        item.network,
                        DateUtils.format(item.date),
                        maxStringLength(item.note, 50),
                    ]
                }}
                showPrintButton={isVouchersPrintingActive}
                onPrint={handleOnPrint}
            />
        </StatusComponent>
    );
}

export default PosVouchersFeature;
