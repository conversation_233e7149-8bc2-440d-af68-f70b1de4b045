import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";

interface IProps {
    orderSum?: IOrderSumModel;
}

const AdminSalesReportTotalInfoFeature: FC<IProps> = ({ orderSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.SUB_TOTAL, value: orderSum?.subTotal, isHiddenInSm: true },
                { text: TranslateConstants.TAX, value: orderSum?.vat },
                { text: TranslateConstants.TOTAL, value: orderSum?.total },
                {
                    text: TranslateConstants.CASH,
                    value: orderSum?.cash,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.NETWORK,
                    value: orderSum?.network,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.CREDIT,
                    value: (orderSum?.deferred ?? 0) + (orderSum?.deliveryApps ?? 0),
                },
            ]}
        />
    );
};

export default AdminSalesReportTotalInfoFeature;
