import { initializeApp } from "firebase/app";
import { getDownloadURL, getStorage, ref, uploadBytes } from "firebase/storage";
import { v4 as uuidv4 } from "uuid";

const firebaseConfig = {
  apiKey: "AIzaSyCd9J4Hv-_gtFSnUnIDXf5iG8OH1bY50MI",
  authDomain: "mini-pos-8d31c.firebaseapp.com",
  projectId: "mini-pos-8d31c",
  storageBucket: "mini-pos-8d31c.appspot.com",
  messagingSenderId: "1088323001223",
  appId: "1:1088323001223:web:88a84e7f25c8a112f85122",
  measurementId: "G-2RTME1W6KZ"
};

const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

export const uploadImage = async (file: File) => {
  try {
    const refactorFileName = (fileName: string): string => {
      return fileName.split(".").slice(0, -1).join("_").split(" ").join("_").split("/").join("_");
    }

    const fileExtension = file.name.split(".").pop();
    const fileName = refactorFileName(file.name) + '+' + uuidv4() + "." + fileExtension;
    const path = `restaurant-app/${fileName}`;

    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
};
