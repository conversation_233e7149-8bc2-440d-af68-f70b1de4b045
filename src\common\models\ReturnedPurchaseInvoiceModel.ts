import { BaseModel } from ".";
import { IPurchaseInvoiceModel } from "./PurchaseInvoiceModel";
import { ISupplierModel } from "./SuppliersModel";

export interface IReturnedPurchaseInvoiceProductModel {
    name: string;
    isTaxable: boolean;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
    purchaseInvoiceProductId: number;
    returnedPurchaseInvoiceId: number;
}

export interface IReturnedPurchaseInvoiceModel extends BaseModel {
    number: number;
    date: number;
    note?: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
    purchaseInvoiceId?: number;
    purchaseInvoice?: IPurchaseInvoiceModel;
    supplier: ISupplierModel;
    returnedPurchaseInvoiceProducts: IReturnedPurchaseInvoiceProductModel[];
}