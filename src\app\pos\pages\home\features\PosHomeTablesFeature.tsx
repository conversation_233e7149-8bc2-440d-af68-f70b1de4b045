import { useAppSelector } from "../../../../../common/redux/store";
import { posTablesSelector } from "../../../redux/selector";
import usePosActions from "../../../redux/usePosActions";
import { ITableModel } from "../../../../../common/models/TableModel";
import { PosHomeService } from "../PosHomeService";
import PosTableCardComponent from "../../../components/PosTableCardComponent";
import { TableStatusEnum } from "../../../../../common/enums/DataEnums";

const PosHomeTablesFeature = () => {
    const posActions = usePosActions();
    const tables = useAppSelector(posTablesSelector);

    const handleOnClick = (table: ITableModel) =>
        PosHomeService.handleTableSelect(table, posActions);

    return (
        <>
            {tables.map((table) => {
                if (table.status === TableStatusEnum.OCCUPIED) return null;

                return (
                    <PosTableCardComponent
                        key={table.id}
                        onClick={() => handleOnClick(table)}
                        {...table}
                    />
                )
            })}
        </>
    );
};

export default PosHomeTablesFeature;
