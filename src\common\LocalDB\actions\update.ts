import { ILocalDBDataWithoutId } from "../LocalDBInterface";

const update = async (
    db: IDBDatabase | undefined,
    collection: string,
    id: string | number,
    data: ILocalDBDataWithoutId,
    addIfNotExist: boolean = false
): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        const transaction = db.transaction(collection, "readwrite");
        const store = transaction.objectStore(collection);

        const handleRequest = (request: IDBRequest) => {
            request.onsuccess = () => resolve(true);
            request.onerror = (e: any) => reject(e.target.error);
        };

        if (addIfNotExist) {
            handleRequest(store.put({ id, ...data }));
        } else {
            const getRequest = store.get(id);
            getRequest.onsuccess = () => {
                if (getRequest.result) {
                    handleRequest(store.put({ id, ...data }));
                } else {
                    reject(new Error("Key not found"));
                }
            };
            getRequest.onerror = (e: any) => reject(e.target.error);
        }
    });
};

export const LocalDbUpdateAction = (
    db: IDBDatabase | undefined,
    collection: string,
    id: string | number,
    data: ILocalDBDataWithoutId,
    addIfNotExist: boolean = false
): Promise<boolean> => {
    return update(db, collection, id, data, addIfNotExist);
};
