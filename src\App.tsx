import "react-toastify/dist/ReactToastify.css";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import LoadingComponent from "./common/components/LoadingComponent";
import ModalComponent from "./common/components/ModalComponent";
import { RoutsConstants } from "./common/constants/RoutesConstants";
import { testKeyConstant } from "./common/constants/ConfigConstants";
import { SessionStorageHelper } from "./common/helpers/SessionStorageHelper";
import useKeyboard from "./common/hooks/useKeyboard";
import { AppHelper } from "./common/helpers/AppHelper";
import ResetAppButtonComponent from "./common/components/ResetAppButtonComponent";
import { useEffect } from "react";
import { useTranslate } from "./common/hooks/useTranslate";

function App() {
  const { dir, setCurrentLanguage } = useTranslate();
  useKeyboard(["Shift", "Control", "F6"], () => {
    if (AppHelper.isTestMode()) SessionStorageHelper.remove(testKeyConstant);
    else SessionStorageHelper.set(testKeyConstant, "true");
    window.location.reload();
  });

  useEffect(() => {
    document.body.dir = dir;
    document.documentElement.dir = dir;
    setCurrentLanguage();
  }, [dir]);

  return (
    <Router>
      <Routes>
        <Route
          path={RoutsConstants.auth.path}
          element={<RoutsConstants.auth.component />}
        />
        <Route
          path={RoutsConstants.role.path}
          element={<RoutsConstants.role.component />}
        />
        <Route
          path={RoutsConstants.adminLayout.path}
          element={<RoutsConstants.adminLayout.component />}
        />
        <Route
          path={RoutsConstants.posLayout.path}
          element={<RoutsConstants.posLayout.component />}
        />
        <Route
          path={RoutsConstants.notFound.path}
          element={<RoutsConstants.notFound.component />}
        />
      </Routes>
      <ToastContainer
        autoClose={2000}
        closeOnClick={true}
        pauseOnHover={false}
        limit={1}
        newestOnTop={true}
        closeButton={false}
        rtl={true}
      />
      <ModalComponent />
      <LoadingComponent />
      <ResetAppButtonComponent />
    </Router>
  );
}

export default App;
