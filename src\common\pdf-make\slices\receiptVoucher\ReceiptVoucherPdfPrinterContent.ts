import { Content } from "pdfmake/interfaces";
import { IReceiptVoucherPdfPrinterModel } from "./ReceiptVoucherPdfPrinterModel";
import { PdfMakeHelper } from "../../PdfMakeHelper";
import { RsSvg } from "../../../assets/svgs/RsSvg";

export const ReceiptVoucherPdfPrinterContent = (
    model: IReceiptVoucherPdfPrinterModel
): Content[] => {
    return [
        {
            text: "سند قبض",
            style: { fontSize: 16, bold: true },
            marginBottom: 5,
            color: "#1F618D",
        },
        {
            text: "Receipt Voucher",
            style: { fontSize: 15, bold: true },
            marginBottom: 5,
            color: "#1F618D",
        },
        {
            layout: "noBorders",
            alignment: "right",
            marginBottom: 10,
            table: {
                widths: ["*", "auto"],
                body: [
                    [
                        { text: "" },
                        PdfMakeHelper.textWithBorder(model.amount ?? "0", {
                            widths: [80],
                            color: "#EBF5FB",
                            fontSize: 14,
                            additionContent: {
                                svg: RsSvg("black"),
                                width: 11,
                                marginTop: 4.5,
                            },
                        }),
                    ],
                ],
            },
        },
        PdfMakeHelper.multiText(
            [
                { text: "Receipt Voucher", alignment: "left", bold: true },
                {
                    text: model.number + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "رقم السند", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        PdfMakeHelper.multiText(
            [
                { text: "Date", alignment: "left", bold: true },
                {
                    text: model.date + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "التاريخ", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        PdfMakeHelper.multiText(
            [
                { text: "Received From", alignment: "left", bold: true },
                {
                    text: model.destination + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "جهة القبض", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        PdfMakeHelper.multiText(
            [
                { text: "Amount", alignment: "left", bold: true },
                {
                    text: model.amount + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "مبلغ وقدره", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        PdfMakeHelper.multiText(
            [
                { text: "Amount In Words", alignment: "left", bold: true },
                {
                    text: model.writtenAmount + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "المبلغ بالحروف", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        PdfMakeHelper.multiText(
            [
                { text: "For", alignment: "left", bold: true },
                {
                    text: model.note || "-" + " ",
                    alignment: "center",
                    width: "auto",
                    bold: true,
                    fontSize: 10,
                },
                { text: "وذلك مقابل", alignment: "right", bold: true },
            ],
            false,
            12,
            5
        ),
        {
            columns: [
                {
                    stack: [{ text: "المستلم", marginBottom: 5 }, { text: "Signature" }],
                    alignment: "center",
                    bold: true,
                    width: "*",
                    marginTop: 10,
                    marginBottom: 50,
                },
                {
                    text: " ",
                    width: "*",
                },
                {
                    stack: [
                        { text: "المحاسب", marginBottom: 5 },
                        { text: "Received By" },
                    ],
                    alignment: "center",
                    bold: true,
                    width: "*",
                    marginTop: 10,
                },
            ],
        },
        PdfMakeHelper.printDivider(false, false, 0, 175),
    ];
};
