import { IAdditionModel } from "../../../common/models/AdditionsModel";
import { ICategoryModel } from "../../../common/models/CategoryModel";
import { IDiscountModel } from "../../../common/models/DiscountModel";
import { IProductModel } from "../../../common/models/ProductModel";
import { ITableModel } from "../../../common/models/TableModel";
import { useAppDispatch } from "../../../common/redux/store";
import { IPosOrder, IPosOrderProduct, IPosOrderProductAddition } from "../interface";
import { IPosHomeState } from "./interface";
import {
    setDataAction,
    setOrderAction,
    setOrderProductsAction,
    setHomeAction,
    updateTableAction,
    setOrderProductAdditionAction,
} from "./slice";
import InitialState, { PosHomeGeneralInitialState } from "./state";

export default function usePosActions(): IPosActions {
    const dispatch = useAppDispatch();

    return {
        setProducts: (data: IProductModel[]) => {
            dispatch(setDataAction({ type: "products", data }));
        },
        setAdditions: (data: IAdditionModel[]) => {
            dispatch(setDataAction({ type: "additions", data }));
        },
        setCategories: (data: ICategoryModel[]) => {
            dispatch(setDataAction({ type: "categories", data }));
        },
        setTables: (data: ITableModel[]) => {
            dispatch(setDataAction({ type: "tables", data }));
        },
        setDiscounts: (data: IDiscountModel[]) => {
            dispatch(setDataAction({ type: "discounts", data }));
        },
        setOrder: (order: Partial<IPosOrder>) => {
            dispatch(setOrderAction(order));
        },
        resetOrder: (data?: Partial<IPosOrder>) => {
            dispatch(setOrderAction({ ...InitialState.order, ...data }));
        },
        setOrderProduct: (product: IPosOrderProduct, productIndex?: number) => {
            dispatch(setOrderProductsAction({ productIndex, product, isQuantityChange: false }));
        },
        setOrderProductQuantity: (product: IPosOrderProduct, productIndex?: number) => {
            dispatch(setOrderProductsAction({ productIndex, product, isQuantityChange: true }));
        },
        setOrderProductAddition: (productIndex: number, addition: IPosOrderProductAddition) => {
            dispatch(setOrderProductAdditionAction({ productIndex, addition, isQuantityChange: false }));
        },
        setOrderProductAdditionQuantity: (productIndex: number, addition: IPosOrderProductAddition) => {
            dispatch(setOrderProductAdditionAction({ productIndex, addition, isQuantityChange: true }));
        },
        setHome: (data: Partial<IPosHomeState>) => {
            dispatch(setHomeAction(data));
        },
        resetHome: () => {
            dispatch(setHomeAction(PosHomeGeneralInitialState));
        },
        updateTable: (data: ITableModel) => {
            dispatch(updateTableAction(data));
        },
    };
}

export type IPosActions = {
    setProducts: (data: IProductModel[]) => void;
    setAdditions: (data: IAdditionModel[]) => void;
    setCategories: (data: ICategoryModel[]) => void;
    setTables: (data: ITableModel[]) => void;
    setDiscounts: (data: IDiscountModel[]) => void;
    setOrder: (data: Partial<IPosOrder>) => void;
    resetOrder: (data?: Partial<IPosOrder>) => void;
    setOrderProduct: (product: IPosOrderProduct, productIndex?: number) => void;
    setOrderProductQuantity: (product: IPosOrderProduct, productIndex?: number) => void;
    setOrderProductAddition: (productIndex: number, addition: IPosOrderProductAddition) => void;
    setOrderProductAdditionQuantity: (productIndex: number, addition: IPosOrderProductAddition) => void;
    setHome: (data: Partial<IPosHomeState>) => void;
    resetHome: () => void;
    updateTable: (data: ITableModel) => void;
};
