import { PaymentsEnum } from "../../../../common/enums/DataEnums";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";

export interface IAdminReturnedPurchaseInvoiceInputs {
    name: string;
    isTaxable: boolean;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
    purchaseInvoiceProductId: number;
}

export interface IAdminReturnedPurchaseInvoiceState {
    paymentMethod: PaymentsEnum;
    purchaseInvoice?: IPurchaseInvoiceModel;
    note: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
    returnedPurchaseInvoiceProducts: IAdminReturnedPurchaseInvoiceInputs[];
}