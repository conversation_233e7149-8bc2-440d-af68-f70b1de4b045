import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { <PERSON>r<PERSON>elper } from "../../../../common/helpers/QrHelper";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { InvoicePdfPrinterContent } from "../../../../common/pdf-make/slices/invoice/InvoicePdfPrinterContent";
import { IInvoicePdfPrinterBodyModel } from "../../../../common/pdf-make/slices/invoice/InvoicePdfPrinterModel";
import { SalesReportPdfPrinterContent } from "../../../../common/pdf-make/slices/salesReport/SalesReportPdfPrinterContent";
import { ISalesReportPdfPrinterModel } from "../../../../common/pdf-make/slices/salesReport/SalesReportPdfPrinterModel";
import { PrintersUtils } from "../../../../common/utils/PrintersUtil";

export class AdminSalesReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        orders: IOrderModel[] | undefined,
        orderSum: IOrderSumModel | undefined
    ) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

        const model: ISalesReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: orders?.map((el) => ({
                invoiceNumber: el.invoiceNumber,
                orderType: el.deliveryApp ?? el.type,
                subTotal: el.subTotal,
                discount: el.discount,
                tobaccoTax: hasTobaccoTax ? el.tobaccoTax : undefined,
                vat: el.vat,
                total: el.total,
                cash: el.cash,
                network: el.network,
                credit: el.deliveryApp ? el.total : 0,
            })),
            totals: {
                subTotal: orderSum?.subTotal ?? 0,
                vat: orderSum?.vat ?? 0,
                total: orderSum?.total ?? 0,
                cash: orderSum?.cash ?? 0,
                network: orderSum?.network ?? 0,
                credit: orderSum?.deliveryApps ?? 0,
            },
        };
        PdfMakeUtils.download(SalesReportPdfPrinterContent(model), "Sales Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static async handleOnPrint(order?: IOrderModel) {
        if (!order) return;

        const model: IInvoicePdfPrinterBodyModel = {
            ...PrintersUtils.handleOrderData(order) as IInvoicePdfPrinterBodyModel,
            qrCode: QrHelper.vatQrData(order.total, order.vat),
            subTotal: order.subTotal.toFixed(2),
            vat: order.vat.toFixed(2),
            total: order.total.toFixed(2),
            totalDeliverApp: order.deliveryAppFee.toFixed(2),
            tobaccoTax: order.tobaccoTax.toFixed(2),
        };

        PdfMakeUtils.preview(
            InvoicePdfPrinterContent(model, {
                isSvgPlaceholder: false,
                reverseLongText: false,
                logo: await OrganizationHelper.getAsyncBase64Logo(),
            }),
            {
                showFooter: false,
                pageSize: { width: 200, height: "auto" },
                fontSize: 9,
                isFontBold: true,
                pageMargins: [0, 0, 5, 0],
            }
        );
    }
}
