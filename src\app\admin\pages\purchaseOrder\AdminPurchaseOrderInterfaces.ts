import { PaymentsEnum } from "../../../../common/enums/DataEnums";
import { IRawMaterialModel } from "../../../../common/models/RawMaterialModel";
import { ISupplierModel } from "../../../../common/models/SuppliersModel";

export interface IAdminPurchaseOrderInputs {
    id?: number;
    rawMaterial: IRawMaterialModel;
    isTaxable: boolean;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IAdminPurchaseOrderState {
    date: number;
    dueDate: number;
    paymentMethod: PaymentsEnum;
    supplier?: ISupplierModel;
    isPriceIncludingTax: boolean;
    purchaseOrderProducts: IAdminPurchaseOrderInputs[];
    note: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
}