import { FC, useMemo } from "react";

interface IProps {
    color?:
    | "bg-white"
    | "bg-black"
    | "bg-primary"
    | "bg-gray-300"
    | "bg-gray-400";
    className?: string;
    isVertical?: boolean;
}

const DividerComponent: FC<IProps> = ({
    color = "bg-gray-300",
    className,
    isVertical = false,
}) => {
    const isVerticalClass = useMemo(() => {
        return isVertical ? "h-full w-[1px]" : "w-full h-[1px]";
    }, [isVertical]);

    return (
        <div
            className={
                "rounded" + " " + isVerticalClass + " " + color + " " + className
            }
        ></div>
    );
};

export default DividerComponent;
