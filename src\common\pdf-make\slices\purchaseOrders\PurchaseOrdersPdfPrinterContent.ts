
import { Content } from "pdfmake/interfaces";
import { IPurchaseOrdersPdfPrinterModel } from "./PurchaseOrdersReportPdfPrinterModel";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const PurchaseOrdersPdfPrinterContent = (model: IPurchaseOrdersPdfPrinterModel): Content[] => {
    return [
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: [
                    "auto",
                    "auto",
                    "*",
                    "auto",
                    "auto",
                    "auto",
                    "*",
                ],
                body: [
                    [
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "ضريبة (15%)", style: "tableHeader" },
                        { text: "الإجمالي قبل الضريبة", style: "tableHeader" },
                        { text: "الخصم", style: "tableHeader" },
                        { text: "السعر", style: "tableHeader" },
                        { text: "الكمية", style: "tableHeader" },
                        { text: "الوصف", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.total,
                        el.vat,
                        el.subTotal,
                        el.discount,
                        el.price,
                        el.quantity,
                        PdfMakeHelper.longText(el.name, 20),
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            columns: [
                {
                    marginBottom: 5,
                    stack: [
                        {
                            columns: [
                                {
                                    alignment: "center",
                                    color: "black",
                                    table: {
                                        widths: ["*", "*"],
                                        body: [
                                            [
                                                { text: model.totals.nativeTotal.toFixed(2) },
                                                { text: "الإجمالي قبل الخصم" },
                                            ]
                                        ]
                                    },
                                    layout: { hLineWidth: (i: number) => (i === 0) ? 1 : 0 }
                                }
                            ]
                        },
                        {
                            columns: [
                                {
                                    alignment: "center",
                                    color: "black",
                                    table: {
                                        widths: ["*", "*"],
                                        body: [
                                            [
                                                { text: model.totals.productsDiscount.toFixed(2) },
                                                { text: "إجمالي خصم المنتجات" },
                                            ]
                                        ]
                                    },
                                    layout: { hLineWidth: (i: number) => (i === 0) ? 1 : 0 }
                                }
                            ]
                        },
                        {
                            columns: [
                                {
                                    alignment: "center",
                                    color: "black",
                                    table: {
                                        widths: ["*", "*"],
                                        body: [
                                            [
                                                { text: model.totals.subTotal.toFixed(2) },
                                                { text: "الإجمالي قبل الضريبة" },
                                            ]
                                        ]
                                    },
                                    layout: { hLineWidth: (i: number) => (i === 0) ? 1 : 0 }
                                }
                            ]
                        },
                        {
                            columns: [
                                {
                                    alignment: "center",
                                    color: "black",
                                    table: {
                                        widths: ["*", "*"],
                                        body: [
                                            [
                                                { text: model.totals.vat.toFixed(2) },
                                                { text: "ضريبة (15%)" },
                                            ]
                                        ]
                                    },
                                    layout: { hLineWidth: (i: number) => (i === 0) ? 1 : 0 }
                                }
                            ]
                        },
                        {
                            columns: [
                                {
                                    alignment: "center",
                                    color: "black",
                                    table: {
                                        widths: ["*", "*"],
                                        body: [
                                            [
                                                { text: model.totals.total.toFixed(2) },
                                                { text: "الإجمالي" },
                                            ]
                                        ]
                                    },
                                    layout: { hLineWidth: (i: number) => (i === 0 || i === 1) ? 1 : 0 }
                                }
                            ]
                        },
                    ],
                },
                PdfMakeHelper.titledTextWithBorder('ملاحظة: ', model.note || '', {
                    maxLength: 36,
                    showBorder: false,
                })
            ],
        }
    ];
}