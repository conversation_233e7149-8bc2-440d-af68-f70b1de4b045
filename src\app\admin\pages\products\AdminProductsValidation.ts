import { Size } from "../../../../common/constants/CommonConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminProductInputs } from "./AdminProductsInterface";

export class AdminProductsValidation {
    static inputsValidation = (values: IAdminProductInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        } else if (values.name.length > 30) {
            ToastHelper.error(TranslateConstants.NAME_SIZE_ERROR);
            isValid = false;
        } else if (!!values.secondName && values.secondName.length > 30) {
            ToastHelper.error(TranslateConstants.SECOND_NAME_SIZE_ERROR);
            isValid = false;
        } else if (!values.categoryId) {
            isValid = false;
            ToastHelper.error(TranslateConstants.CATEGORY_FIELD_IS_REQUIRED);
        } else if (
            values.productSizeType === ProductSizeTypeEnum.FIXED &&
            !values.price
        ) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRICE_FIELD_IS_REQUIRED);
        } else if (values.price && (values.price <= 0 || values.price > 100000)) {
            isValid = false;
            ToastHelper.error(
                TranslateConstants.PRICE_FIELD_MUST_BE_BETWEEN_0_1_AND_100000
            );
        } else if (values.isSubjectToTobaccoTax && values.price && (values.price < 30)) {
            isValid = false;
            ToastHelper.error(
                TranslateConstants.TOBACCO_TAX_PRICE_ERROR
            );
        } else if (values.image && values.image.size > Size.MB) {
            isValid = false;
            ToastHelper.error(TranslateConstants.IMAGE_SIZE_MUST_BE_LESS_THAN_1_MB);
        }
        if (
            values.productSizeType === ProductSizeTypeEnum.MULTIPLE &&
            (!values.sizes?.length || values.sizes.find((size) => !size.name || !size.price))
        ) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRODUCT_SIZE_NAME_AND_PRICE_ARE_REQUIRED);
        } else if (values.sizes?.some((size) => size.name.length > 30 || size.secondName.length > 30)) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_SIZE_ERROR);
        }

        return isValid;
    };
}
