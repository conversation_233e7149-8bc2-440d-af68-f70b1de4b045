export class RegexHelper {
    static isEmail(email: string): boolean {
        return /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
            email
        );
    }

    static isSaudiMobile(mobile: string): boolean {
        return /^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/.test(mobile);
    }

    static isMobile(mobile: string): boolean {
        return /^(\+?\d{1,4}?[\s-]?)?(?:\(?\d{1,4}?\)?[\s-]?)?\d{1,4}[\s-]?\d{1,4}[\s-]?\d{1,9}$/.test(mobile);
    }
}
