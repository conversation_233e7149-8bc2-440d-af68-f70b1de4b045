import AdminHeaderDropDownMenuComponent from "./AdminHeaderDropDownMenuComponent";
import usePageTitle from "../../../common/hooks/usePageTitle";
import { LuMenu } from "react-icons/lu";
import { SlScreenDesktop } from "react-icons/sl";
import { RoleService } from "../../role/RoleService";
import { useNavigate } from "react-router-dom";
import { TranslateHelper } from "../../../common/helpers/TranslateHelper";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";

function AdminHeaderComponent() {
    const navigate = useNavigate();
    const { title } = usePageTitle();

    const handleOnPosClick = () => RoleService.redirectToRolePage(navigate);

    return (
        <div className="navbar flex justify-between bg-base-100 z-10 border-b dark:border-b-slate-600 p-1">
            <div>
                <label
                    htmlFor="left-sidebar-drawer"
                    className={`btn btn-ghost bg-[#226bb2] hover:bg-[#387ec5] text-white drawer-button lg:hidden md:mx-1`}
                >
                    <LuMenu className="h-5 inline-block w-5" />
                </label>
                {<h1 className="text-lg lg:text-xl font-semibold mx-2">{title}</h1>}
            </div>

            <div className="order-last gap-2 flex items-center">
                <button
                    className="btn btn-ghost btn-circle w-max hidden sm:flex items-center gap-2 px-1"
                    onClick={handleOnPosClick}
                >
                    <SlScreenDesktop size={20} />
                    <span className="hidden md:inline-block">
                        {TranslateHelper.t(TranslateConstants.POS)}
                    </span>
                </button>
                <AdminHeaderDropDownMenuComponent />
            </div>
        </div>
    );
}

export default AdminHeaderComponent;
