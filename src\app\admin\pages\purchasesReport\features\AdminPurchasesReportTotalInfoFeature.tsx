import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IPurchaseInvoiceSumModel } from "../../../../../common/models/PurchaseInvoiceSumModel";

interface IProps {
    purchaseInvoicesSum?: IPurchaseInvoiceSumModel;
}

const AdminPurchasesReportTotalInfoFeature: FC<IProps> = ({ purchaseInvoicesSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.SUB_TOTAL, value: purchaseInvoicesSum?.subTotal },
                { text: TranslateConstants.TAX, value: purchaseInvoicesSum?.vat },
                { text: TranslateConstants.TOTAL, value: purchaseInvoicesSum?.total },
                {
                    text: TranslateConstants.CASH,
                    value: purchaseInvoicesSum?.cash,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.NETWORK,
                    value: purchaseInvoicesSum?.network,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.REMAINING,
                    value: purchaseInvoicesSum?.deferred,
                    isHiddenInSm: true,
                },
            ]}
        />
    );
};

export default AdminPurchasesReportTotalInfoFeature;
