import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import { IOrderProductSumModel } from "../../../../common/models/OrderProductSumModel";
import { IReturnedOrderProductSumModel } from "../../../../common/models/ReturnedOrderProductSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { MostSellingReportProductsPdfPrinterContent } from "../../../../common/pdf-make/slices/mostSellingProductsReport/MostSellingReportProductsPdfPrinterContent";
import { IMostSellingProductsReportPdfPrinterModel } from "../../../../common/pdf-make/slices/mostSellingProductsReport/MostSellingReportProductsPdfPrinterModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";

export class AdminMostSellingProductsReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: IMostSellingProductsModel[] | undefined,
    ) {
        const model: IMostSellingProductsReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: data?.map((el) => ({
                name: el.name,
                quantity: fixedNumber(el.quantity),
                discount: fixedNumber(el.discount),
                subTotal: fixedNumber(el.subTotal),
                tobaccoTax: fixedNumber(el.tobaccoTax),
                vat: fixedNumber(el.vat),
                total: fixedNumber(el.total),
            })),
        };

        PdfMakeUtils.download(MostSellingReportProductsPdfPrinterContent(model), "Most Selling Products Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleData(
        ordersProducts: IOrderProductSumModel[] = [],
        returnedOrderProducts: IReturnedOrderProductSumModel[] = []
    ): IMostSellingProductsModel[] {
        return ordersProducts.map((el) => {
            const returned = returnedOrderProducts.find((el2) => el2.name === el.name);
            return {
                name: el.name,
                price: el.price,
                quantity: el.quantity - (returned?.quantity ?? 0),
                subTotal: el.subTotal - (returned?.subTotal ?? 0),
                discount: el.discount - (returned?.discount ?? 0),
                tobaccoTax: el.tobaccoTax - (returned?.tobaccoTax ?? 0),
                vat: el.vat - (returned?.vat ?? 0),
                total: el.total - (returned?.total ?? 0),
            };
        }).filter((el) => el.quantity > 0);
    }
}