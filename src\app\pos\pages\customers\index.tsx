import useFetch from "../../../../common/asyncController/useFetch";
import InputComponent from "../../../../common/components/InputComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import useActions from "../../../../common/redux/data/useActions";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import { PosCustomersRepo } from "../../repo/PosCustomersRepo";
import PosReceiptVoucherModal from "../home/<USER>/PosReceiptVoucherModal";
import PosCustomersModal from "./modals/PosCustomersModal";
import { PosCustomersTableHeaderConstants } from "./PosCustomersConstants";

const PosCustomersPage = () => {
    const { translate } = useTranslate();
    const actions = useActions();

    const { data, isLoading, isError, refetch } = useFetch(
        "pos-customers",
        (search?: string) => PosCustomersRepo.getCustomers({ search }),
        { autoFetchOnMount: true }
    );

    const handleOnEdit = (item: ICustomerModel) => {
        if (!item.active) {
            ToastHelper.error(translate(TranslateConstants.CANT_EDIT_INACTIVE_CUSTOMER));
            return;
        }

        actions.openModal({
            size: "lg",
            title: TranslateHelper.t(TranslateConstants.EDIT_CUSTOMER),
            showButtons: false,
            component: <PosCustomersModal item={item} />,
        });
    };

    const handlePay = (customer: ICustomerModel) => {
        actions.openModal({
            component: <PosReceiptVoucherModal customer={customer} />,
            showButtons: false,
            size: "sm",
            title: TranslateHelper.t(TranslateConstants.CUSTOMER) + " (" + (customer?.name || "") + ")",
        });
    };

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="flex justify-between">
                <div className="font-tajawal-bold text-2xl">
                    {translate(TranslateConstants.CUSTOMERS)}
                </div>
                <InputComponent
                    containerClassName="w-1/4"
                    isDebounce={true}
                    placeholder={TranslateConstants.NAME_OR_MOBILE}
                    onChange={(search) => refetch(search)}
                />
            </div>
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={13}
            >
                <TableComponent
                    headers={PosCustomersTableHeaderConstants}
                    items={data || []}
                    selectors={(item: ICustomerModel) => {
                        const statusColor = () => {
                            if (item.active) return "text-green-600";
                            return "text-red-600";
                        };

                        return [
                            item.number,
                            item.name,
                            item.mobile,
                            item.taxNumber,
                            item.credit,
                            item.address,
                            <div className={statusColor()}>
                                {translate(
                                    item.active
                                        ? TranslateConstants.ACTIVE
                                        : TranslateConstants.INACTIVE
                                )}
                            </div>,
                        ];
                    }}
                    showEditButton={(item: ICustomerModel) => item.active}
                    onEdit={handleOnEdit}
                    customButtons={[
                        {
                            text: TranslateConstants.PAY,
                            onClick: handlePay,
                            showButton: (item: ICustomerModel) => item.credit > 0,
                        },
                    ]}
                />
            </StatusComponent>
            <PosBackButtonComponent />
        </div>
    );
};

export default PosCustomersPage;
