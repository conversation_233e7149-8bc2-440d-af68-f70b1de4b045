import { IAdminPurchaseOrderState } from "../../../app/admin/pages/purchaseOrder/AdminPurchaseOrderInterfaces";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IPurchaseOrderBody } from "../../interfaces/body/PurchaseOrderBody";
import { IPurchaseOrderModel } from "../../models/PurchaseOrderModel";
import { debug } from "../../utils/CommonUtils";
import { fixedNumber } from "../../utils/numberUtils";

export class PurchaseOrderApiRepo {
    static async getPurchaseOrders(options?: {
        getPurchaseOrderProducts?: boolean;
        getSupplier?: boolean;
        date?: number;
        supplierId?: number;
    }) {
        try {
            const res = await AxiosHelper.get<IPurchaseOrderModel[]>(
                EndPointsEnums.PURCHASE_ORDERS,
                {
                    params: {
                        getPurchaseOrderProducts: options?.getPurchaseOrderProducts ?? true,
                        getSupplier: options?.getSupplier ?? true,
                        date: options?.date,
                        supplierId: options?.supplierId,
                    },
                }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseOrderApiRepo [getPurchaseOrders] Error: `, error);
            throw error;
        }
    }

    static async addPurchaseOrder(body: IAdminPurchaseOrderState) {
        try {
            const formattedBody: IPurchaseOrderBody = {
                ...body,
                supplierId: body.supplier?.id ?? 0,
                purchaseOrderProducts: body.purchaseOrderProducts.map(
                    (rawMaterial) => ({
                        rawMaterialId: rawMaterial.rawMaterial.id,
                        isTaxable: rawMaterial.isTaxable,
                        name: rawMaterial.rawMaterial.name,
                        quantity: fixedNumber(rawMaterial.quantity),
                        price: fixedNumber(rawMaterial.price),
                        discount: fixedNumber(rawMaterial.discount),
                        subTotal: fixedNumber(rawMaterial.subTotal),
                        vat: fixedNumber(rawMaterial.vat),
                        total: fixedNumber(rawMaterial.total),
                    })
                ),
            };

            delete (formattedBody as any).supplier;

            const res = await AxiosHelper.post<IPurchaseOrderModel>(
                EndPointsEnums.PURCHASE_ORDERS,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseOrderApiRepo [addPurchaseOrder] Error: `, error);
            throw error;
        }
    }

    static async updatePurchaseOrder(id: number, body: IAdminPurchaseOrderState) {
        try {
            const formattedBody: IPurchaseOrderBody = {
                supplierId: body.supplier?.id ?? 0,
                purchaseOrderProducts: body.purchaseOrderProducts.map(
                    (rawMaterial) => ({
                        id: rawMaterial.id,
                        rawMaterialId: rawMaterial.rawMaterial.id,
                        isTaxable: rawMaterial.isTaxable,
                        name: rawMaterial.rawMaterial.name,
                        quantity: fixedNumber(rawMaterial.quantity),
                        price: fixedNumber(rawMaterial.price),
                        discount: fixedNumber(rawMaterial.discount),
                        subTotal: fixedNumber(rawMaterial.subTotal ?? 0),
                        vat: fixedNumber(rawMaterial.vat ?? 0),
                        total: fixedNumber(rawMaterial.total),
                    })
                ),
                date: body.date,
                dueDate: body.dueDate,
                paymentMethod: body.paymentMethod,
                isPriceIncludingTax: body.isPriceIncludingTax,
                note: body.note,
                nativeTotal: body.nativeTotal,
                productsDiscount: body.productsDiscount,
                subTotal: body.subTotal,
                vat: body.vat,
                total: body.total
            };

            const res = await AxiosHelper.patch<IPurchaseOrderModel>(
                EndPointsEnums.PURCHASE_ORDERS,
                id,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseOrderApiRepo [updatePurchaseOrder] Error: `, error);
            throw error;
        }
    }

    static async getPurchaseOrderCount() {
        try {
            const res = await AxiosHelper.get<number>(
                EndPointsEnums.PURCHASE_ORDERS_COUNT
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseOrderApiRepo [getPurchaseOrderCount] Error: `, error);
            throw error;
        }
    }
}
