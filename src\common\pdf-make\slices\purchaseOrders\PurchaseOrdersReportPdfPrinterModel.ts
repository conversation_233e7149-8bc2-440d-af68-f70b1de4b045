interface IPurchaseOrdersPdfPrinterItemModel {
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IPurchaseOrdersPdfPrinterModel {
    items?: IPurchaseOrdersPdfPrinterItemModel[];
    note?: string;
    totals: {
        nativeTotal: number;
        productsDiscount: number;
        subTotal: number;
        vat: number;
        total: number;
    };
}