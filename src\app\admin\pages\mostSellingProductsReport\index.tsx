import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminMostSellingProductsReportTableHeaders } from "./AdminMostSellingProductsReportsConstants";
import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrderProductsApiRepo } from "../../../../common/repos/api/OrderProductsApi";
import { ReturnedOrderProductsApiRepo } from "../../../../common/repos/api/ReturnedOrderProductsApiRepo";
import { useMemo, useState } from "react";
import { AdminMostSellingProductsReportService } from "./AdminMostSellingProductsReportService";

const AdminMostSellingProductsPage = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: ordersProducts,
        isLoading: ordersProductsLoading,
        isError: ordersProductsError,
        refetch: refetchOrderProducts,
    } = useFetch(
        "report-" + EndPointsEnums.ORDER_PRODUCTS_SUM_MANY,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrderProductsApiRepo.getSumMany(startTime, endTime, hasTobaccoTax),
        { autoFetchOnMount: true }
    );

    const {
        data: returnedOrderProducts,
        isLoading: returnedOrderProductsLoading,
        isError: returnedOrderProductsError,
        refetch: refetchReturnedOrderProducts,
    } = useFetch(
        "report-" + EndPointsEnums.RETURNED_ORDER_PRODUCTS_SUM_MANY,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrderProductsApiRepo.getSumMany(startTime, endTime, hasTobaccoTax),
        { autoFetchOnMount: true }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchOrderProducts(start, end, hasTobaccoTax);
        refetchReturnedOrderProducts(start, end, hasTobaccoTax);
    };

    const data = useMemo(() => {
        return AdminMostSellingProductsReportService.handleData(
            ordersProducts,
            returnedOrderProducts
        );
    }, [ordersProducts, returnedOrderProducts]);

    const onDownload = () =>
        AdminMostSellingProductsReportService.handleDownload(dateRange, data);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!data?.length}
            />
            <StatusComponent
                isEmpty={!data.length}
                isLoading={ordersProductsLoading || returnedOrderProductsLoading}
                isError={ordersProductsError || returnedOrderProductsError}
                height={isXs ? 10.6 : isSm ? 8 : 8.1}
            >
                <TableComponent
                    headers={AdminMostSellingProductsReportTableHeaders()}
                    items={data || []}
                    selectors={(item: IMostSellingProductsModel) => [
                        item.name,
                        item.quantity,
                        item.discount.toFixed(2),
                        item.subTotal.toFixed(2),
                        ...(hasTobaccoTax ? [item.tobaccoTax.toFixed(2)] : []),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminMostSellingProductsPage;
