import {
    BaseQueryFn,
    fetchBaseQuery,
    FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { accessTokenKeyConstant } from "../../constants/ConfigConstants";
import { CookiesHelper } from "../../helpers/CookiesHelper";
import { debug } from "../../utils/CommonUtils";
import { AuthService } from "../../../app/auth/AuthService";
import { getDomain } from "../../config/axios";

const getAccessToken = () =>
    `Bearer ${CookiesHelper.get(accessTokenKeyConstant)}`;

const baseQuery = fetchBaseQuery({
    baseUrl: getDomain(),
    prepareHeaders: (headers) => {
        headers.set("Authorization", getAccessToken());
        return headers;
    },
});

export const customBaseQuery: BaseQueryFn<
    string | { url: string },
    unknown,
    FetchBaseQueryError
> = async (args, api, extraOptions) => {
    const result = await baseQuery(args, api, extraOptions);

    if (result.error) {
        if (result.error.status === 401) AuthService.logout();
        debug(`customBaseQuery Error: `, result.error);
        return { error: (result.error as any).data ?? result.error };
    }

    if (result.data && (result.data as any).data) {
        return { data: (result.data as any).data };
    }

    return { data: result.data };
};

