import Datepicker, { DateValueType } from "react-tailwindcss-datepicker";
import { FC, useState } from "react";
import { DateUtils } from "../utils/DateUtils";

interface IProps {
    onChange?: (date: { start: Date; end: Date } | Date) => void;
    defaultValue?: DateValueType;
    label?: string;
    minDate?: Date;
    type?: "date" | "datetime";
}

const DatePickerComponent: FC<IProps> = ({
    onChange,
    defaultValue,
    label,
    minDate,
    type = "datetime",
}) => {
    const [state, setState] = useState<DateValueType>({
        startDate: defaultValue?.startDate || null,
        endDate: defaultValue?.endDate || null,
    });
    const [singleDate, setSingleDate] = useState(defaultValue?.startDate || null);

    const handleOnChange = (newValue: DateValueType | Date) => {
        if (type === "date") setState(newValue as DateValueType);
        if (type === "datetime" && DateUtils.isValidDate(newValue)) setSingleDate(newValue as Date);
        if (
            onChange &&
            type === "date" &&
            (newValue as DateValueType)?.startDate &&
            (newValue as DateValueType)?.endDate
        ) {
            onChange({
                start: DateUtils.getStartOfDay(
                    (newValue as DateValueType)?.startDate as Date
                ),
                end: DateUtils.getEndOfDay(
                    (newValue as DateValueType)?.endDate as Date
                ),
            });
        }
        if (onChange && type === "datetime" && newValue) {
            onChange(newValue as Date);
        }
    };

    return (
        <>
            {type === "date" && (
                <Datepicker
                    value={state}
                    onChange={handleOnChange}
                    containerClassName="ltr-direction relative w-full sm:w-36 md:w-48"
                    inputClassName="input input-bordered w-full rounded sm:w-36 md:w-48 border-gray-400 focus:outline-none h-[2.6rem] dark:text-white"
                    showShortcuts={false}
                    toggleIcon={(_) => (
                        <span className="flex items-center justify-center text-black dark:text-white">
                            {label}
                        </span>
                    )}
                    minDate={minDate}
                    maxDate={new Date()}
                    i18n="ar"
                    showFooter={false}
                    primaryColor="blue"
                    startWeekOn="sat"
                    placeholder="ي-ش-س"
                    useRange={false}
                    asSingle={true}
                    configs={{
                        shortcuts: {
                            today: "اليوم",
                            yesterday: "الأمس",
                            past: (period) => `الـ ${period} السابق`,
                            currentMonth: "الشهر الحالي",
                            pastMonth: "الشهر السابق",
                        },
                        footer: {
                            cancel: "إلغاء",
                            apply: "تطبيق",
                        },
                    }}
                />
            )}
            {type === "datetime" && (
                <div className="w-full sm:w-40 md:w-48 lg:w-52 flex border border-gray-400 rounded items-center justify-between bg-base-100">
                    <label className="text-black dark:text-white sm:mx-1 sm:border border-gray-600 dark:border-gray-200 sm:rounded-full sm:w-6 text-sm text-center">
                        {label}
                    </label>
                    <input
                        type="datetime-local"
                        className="flex-1 sm:flex-none !w-10 !p-0 sm:!w-32 md:!w-40 lg:!w-44 rounded focus:outline-none bg-base-100 dark:text-white"
                        onChange={(e) => handleOnChange(new Date(e.target.value))}
                        value={
                            singleDate
                                ? new Date(
                                    singleDate.getTime() -
                                    singleDate.getTimezoneOffset() * 60000
                                )
                                    .toISOString()
                                    .slice(0, 16) // yyyy-MM-ddTHH:mm
                                : ""
                        }
                    />
                </div>
            )}
        </>
    );
};

export default DatePickerComponent;
