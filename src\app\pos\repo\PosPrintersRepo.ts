import axios from "axios";
import { debug } from "../../../common/utils/CommonUtils";
import { IPrinterModel } from "../../../common/models/PrinterModel";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { PrintersUtils } from "../../../common/utils/PrintersUtil";
import { IPosOrder, IPosReturnedOrder } from "../interface";
import { KitchenOrderStatusEnum } from "../../../common/enums/DataEnums";
import { PrintersHelper } from "../../../common/helpers/PrintersHelper";
import { IShiftPdfPrinterBodyModel } from "../../../common/pdf-make/slices/shift/ShiftPdfPrinterModel";
import { IPdfMakePrinterModel } from "../../../common/pdf-make/PdfMakeInterfaces";
import { ShiftPdfPrinterContent } from "../../../common/pdf-make/slices/shift/ShiftPdfPrinterContent";
import { PrinterServerHost } from "../../../common/constants/CommonConstants";
import { Content } from "pdfmake/interfaces";

export class PosPrintersRepo {
    static async getAllPrinters(): Promise<IPrinterModel[]> {
        try {
            const res = await axios.get<IPrinterModel[]>(
                `${PrinterServerHost}/${EndPointsEnums.GET_PRINTERS}`
            );
            return res.data;
        } catch (error) {
            debug(`PosSettingsRepo [getAllPrinters] Error: ${error}`);
            throw error;
        }
    }

    static async printKitchenOrder(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): Promise<void> {
        const orderToPrint = await PrintersUtils.handelKitchenOrderPrint(
            order,
            orderStatus
        );

        try {
            for (const item of orderToPrint) {
                await axios.post(
                    `${PrinterServerHost}/${EndPointsEnums.PRINT}`,
                    item
                );
            }
        } catch (error) {
            debug(`PosSettingsRepo [printKitchenOrder] Error: ${error}`);
            throw error;
        }
    }

    static async printInvoiceOrder(order: IPosOrder): Promise<void> {
        const orderToPrint = await PrintersUtils.handleInvoiceOrderPrint(order);

        try {
            await axios.post(
                `${PrinterServerHost}/${EndPointsEnums.PRINT}`,
                orderToPrint
            );
        } catch (error) {
            debug(`PosSettingsRepo [printInvoiceOrder] Error: ${error}`);
            throw error;
        }
    }

    static async printReturnedInvoiceOrder(order: IPosReturnedOrder): Promise<void> {
        const orderToPrint = await PrintersUtils.handleReturnedInvoiceOrderPrint(order);

        try {
            await axios.post(
                `${PrinterServerHost}/${EndPointsEnums.PRINT}`,
                orderToPrint
            );
        } catch (error) {
            debug(`PosSettingsRepo [printReturnedInvoiceOrder] Error: ${error}`);
            throw error;
        }
    }

    static async printChequeOrder(order: IPosOrder): Promise<void> {
        const orderToPrint = await PrintersUtils.handleChequeOrderPrint(order);

        try {
            await axios.post(
                `${PrinterServerHost}/${EndPointsEnums.PRINT}`,
                orderToPrint
            );
        } catch (error) {
            debug(`PosSettingsRepo [printChequeOrder] Error: ${error}`);
            throw error;
        }
    }

    static async print(body: Content, printerId?: string): Promise<void> {
        try {
            const printer = printerId || PrintersHelper.getDefaultPrinter()?.printer;
            if (!printer) return;

            await axios.post(`${PrinterServerHost}/${EndPointsEnums.PRINT}`, {
                printerId: printer,
                body,
            });
        } catch (error) {
            debug(`PosSettingsRepo [printEmpty] Error: ${error}`);
            throw error;
        }
    }

    static async printEmpty(printerId: string): Promise<void> {
        try {
            await axios.post(`${PrinterServerHost}/${EndPointsEnums.PRINT}`, {
                printerId,
                body: [{ text: "" }],
            });
        } catch (error) {
            debug(`PosSettingsRepo [printEmpty] Error: ${error}`);
            throw error;
        }
    }

    static async printShift(shift: IShiftPdfPrinterBodyModel): Promise<void> {
        try {
            const printerId = PrintersHelper.getDefaultPrinter()?.printer;
            if (!printerId) return;

            const body: IPdfMakePrinterModel = {
                printerId,
                body: ShiftPdfPrinterContent(shift, {
                    // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                    logo: './images/logo.jpg',
                    showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
                }),
            }

            await axios.post(
                `${PrinterServerHost}/${EndPointsEnums.PRINT}`,
                body
            );
        } catch (error) {
            debug(`PosSettingsRepo [printShift] Error: ${error}`);
            throw error;
        }
    }
}
