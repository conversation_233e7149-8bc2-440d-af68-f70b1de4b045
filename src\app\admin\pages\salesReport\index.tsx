import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import {
    useGetOrdersQuery,
    useGetOrdersSumQuery,
} from "../../../../common/redux/api/slice";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminSalesReportTableHeaders } from "./AdminSalesReportsConstants";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminSalesReportTotalInfoFeature from "./features/AdminSalesReportTotalInfoFeature";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { AdminSalesReportService } from "./SalesReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";

const AdminSalesReportPage = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: orderSum,
        isFetching: orderSumLoading,
        isError: orderSumError,
    } = useGetOrdersSumQuery(dateRange, { refetchOnMountOrArgChange: true });

    const {
        data: orders,
        isFetching: ordersLoading,
        isError: ordersError,
    } = useGetOrdersQuery(dateRange, { refetchOnMountOrArgChange: true });

    const onDate = (startDate: Date, endDate: Date) => {
        setDateRange({
            startTime: startDate.getTime(),
            endTime: endDate.getTime(),
        });
    };

    const {
        isLoading: orderLoading,
        refetch: refetchOrder,
    } = useFetch(
        "report-" + EndPointsEnums.ORDERS_GET_ONE,
        (id: number) => OrdersApiRepo.getOne(id),
        {
            autoFetchIfEmpty: false,
            showDefaultErrorToast: true,
            onSuccess: ({ data }) => AdminSalesReportService.handleOnPrint(data),
        }
    );

    const onDownload = () =>
        AdminSalesReportService.handleDownload(dateRange, orders, orderSum);

    const onPrint = (id: number) => refetchOrder(id);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!orders?.length || !orderSum}
            />
            <AdminSalesReportTotalInfoFeature orderSum={orderSum} />
            <StatusComponent
                isEmpty={!orders?.length}
                isLoading={orderSumLoading || ordersLoading || orderLoading}
                isError={orderSumError || ordersError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
                showLogoLoading={orderLoading}
            >
                <TableComponent
                    headers={AdminSalesReportTableHeaders()}
                    items={orders || []}
                    selectors={(item: IOrderModel) => [
                        item.invoiceNumber,
                        TranslateHelper.t(item.deliveryApp ?? item.type),
                        item.subTotal.toFixed(2),
                        item.discount.toFixed(2),
                        ...(hasTobaccoTax ? [item.tobaccoTax.toFixed(2)] : []),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        ((item.deferred ?? 0) + (item.deliveryApp ? item.total : 0)).toFixed(2),
                    ]}
                    onPrint={(order) => onPrint(order.id)}
                    showPrintButton={true}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminSalesReportPage;
