import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminPurchaseInvoiceState } from "./AdminPurchaseInvoiceInterfaces";

export class AdminPurchaseInvoiceValidation {
    static inputsValidation = (values: IAdminPurchaseInvoiceState) => {
        let isValid = true;

        if (!values.date) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DATE_FIELD_IS_REQUIRED);
        } else if (!values.dueDate) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DUE_DATE_FIELD_IS_REQUIRED);
        } else if (!values.supplier) {
            isValid = false;
            ToastHelper.error(TranslateConstants.SUPPLIER_FIELD_IS_REQUIRED);
        } else if (
            !values.purchaseInvoiceProducts.length ||
            values.purchaseInvoiceProducts.some((rawMaterial) =>
                !rawMaterial.rawMaterial ||
                !rawMaterial.quantity || Number(rawMaterial.quantity) <= 0 ||
                [0, undefined].includes(rawMaterial.price) || Number(rawMaterial.price) < 0 ||
                !rawMaterial.total || Number(rawMaterial.total) < 0 ||
                Number(rawMaterial.discount) < 0
            )
        ) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRODUCTS_FIELD_IS_NOT_VALID);
        }

        return isValid;
    };
}
