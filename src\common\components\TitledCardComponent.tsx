import { FC } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";

interface IProps {
    title?: string;
    children: React.ReactNode;
    className?: string;
}

const TitledCardComponent: FC<IProps> = ({
    title,
    children,
    className = "",
}) => {
    return (
        <div
            className={
                "w-full rounded-lg bg-base-100 border border-gray-300 dark:border-gray-500 shadow h-full flex flex-col" +
                " " +
                className
            }
        >
            {title && (
                <h1 className="font-bold dark:text-white text-lg bg-base-200 rounded-t-lg p-2 text-center flex-shrink-0">
                    {TranslateHelper.t(title)}
                </h1>
            )}
            <div className="w-full p-2 flex-1 min-h-0">{children}</div>
        </div>
    );
};

export default TitledCardComponent;
