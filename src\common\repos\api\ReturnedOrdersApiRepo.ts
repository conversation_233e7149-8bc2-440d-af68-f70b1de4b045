import { DeliveryAppEnum } from "../../enums/DataEnums";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IReturnedOrderModel } from "../../models/ReturnedOrderModel";
import { IReturnedOrderSumModel } from "../../models/ReturnedOrderSumModel";
import { debug } from "../../utils/CommonUtils";

export class ReturnedOrdersApiRepo {
    static async getReturnedOrdersSum(
        startTime?: number,
        endTime?: number,
        customerId?: number,
        deliveryApp?: DeliveryAppEnum,
        isCash?: boolean,
        isBank?: boolean,
    ): Promise<IReturnedOrderSumModel | undefined> {
        try {
            const res = await AxiosHelper.get<IReturnedOrderSumModel>(
                EndPointsEnums.RETURNED_ORDERS_SUM,
                { params: { startTime, endTime, customerId, deliveryApp, isCash, isBank } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReturnedOrdersRepo [getReturnedOrdersSum] Error: `, error);
            throw error;
        }
    }

    static async getReturnedOrders(
        startTime?: number,
        endTime?: number,
        customerId?: number,
        getProducts?: boolean,
        deliveryApp?: DeliveryAppEnum,
        isCash?: boolean,
        isBank?: boolean,
    ): Promise<IReturnedOrderModel[] | undefined> {
        try {
            const res = await AxiosHelper.get<IReturnedOrderModel[]>(
                EndPointsEnums.RETURNED_ORDERS,
                { params: { startTime, endTime, customerId, getProducts, deliveryApp, isCash, isBank } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReturnedOrdersRepo [getReturnedOrders] Error: `, error);
            throw error;
        }
    }
}