import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { IShiftModel } from "../../../../common/models/ShiftModel";
import { IShiftSumModel } from "../../../../common/models/ShiftsSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { ShiftPdfPrinterContent } from "../../../../common/pdf-make/slices/shift/ShiftPdfPrinterContent";
import { IShiftPdfPrinterBodyModel } from "../../../../common/pdf-make/slices/shift/ShiftPdfPrinterModel";
import { ShiftReportPdfPrinterContent } from "../../../../common/pdf-make/slices/shiftReport/ShiftReportPdfPrinterContent";
import { IShiftReportPdfPrinterModel } from "../../../../common/pdf-make/slices/shiftReport/ShiftReportPdfPrinterModel";

export class AdminShiftReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        shifts: IShiftModel[] | undefined,
        shiftsSum: IShiftSumModel | undefined
    ) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

        const model: IShiftReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: shifts?.map((el) => ({
                ordersCount: el.ordersCount,
                startAmount: el.startAmount,
                endAmount: el.endAmount,
                discountAmount: el.discountAmount,
                tobaccoTaxAmount: hasTobaccoTax ? el.tobaccoTaxAmount : undefined,
                vatAmount: el.vatAmount,
                totalAmount: el.totalAmount,
                cashAmount: el.cashAmount,
                networkAmount: el.networkAmount,
                deliveryAppsAmount: el.deliveryAppsAmount ?? 0,
                additionAmount: el.additionAmount ?? 0,
                shortageAmount: el.shortageAmount ?? 0,
            })),
            totals: {
                ordersCount: shiftsSum?.ordersCount ?? 0,
                totalAmount: shiftsSum?.totalAmount ?? 0,
                vatAmount: shiftsSum?.vatAmount ?? 0,
                additionAmount: shiftsSum?.additionAmount ?? 0,
                shortageAmount: shiftsSum?.shortageAmount ?? 0,
            },
        };

        PdfMakeUtils.download(ShiftReportPdfPrinterContent(model), "Shift Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static async handleOnPrint(shift?: IShiftModel) {
        if (!shift) return;

        const model: IShiftPdfPrinterBodyModel = {
            ...shift,
            paymentAmounts: [],
        };

        PdfMakeUtils.preview(
            ShiftPdfPrinterContent(model, {
                isSvgPlaceholder: false,
                reverseLongText: false,
                logo: await OrganizationHelper.getAsyncBase64Logo(),
            }),
            {
                showFooter: false,
                pageSize: { width: 200, height: "auto" },
                fontSize: 9,
                isFontBold: true,
                pageMargins: [0, 0, 5, 0],
            }
        );
    }
}