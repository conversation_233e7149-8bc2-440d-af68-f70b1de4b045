import { EndPointsEnums } from "../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../common/helpers/AxiosHelper";
import { IAuthRegisterInputs } from "./AuthInterface";
import { debug } from "../../common/utils/CommonUtils";

export class AuthRepo {
    static async login(email: string, password: string) {
        try {
            return await AxiosHelper.post<never>(EndPointsEnums.AUTH, {
                email,
                password,
            });
        } catch (error) {
            debug(`AuthRepo [Login] Error: ${error}`);
            throw error;
        }
    }

    static async register(values: IAuthRegisterInputs) {
        try {
            return await AxiosHelper.post<never>(
                EndPointsEnums.REGISTRATION_REQUESTS,
                values
            );
        } catch (error) {
            debug(`AuthRepo [Register] Error: ${error}`);
            throw error;
        }
    }
}
