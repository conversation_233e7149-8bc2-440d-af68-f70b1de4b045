import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ExcelSheetHelper } from "../../../../common/helpers/ExcelSheetHelper";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IOrderProductSumModel } from "../../../../common/models/OrderProductSumModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { TobaccoReportPdfPrinterContent } from "../../../../common/pdf-make/slices/tobaccoReport/TobaccoReportPdfPrinterContent";
import { ITobaccoReportPdfPrinterModel } from "../../../../common/pdf-make/slices/tobaccoReport/TobaccoReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminTobaccoTaxReportService {
    static async handlePdfDownload(
        dates: { startDate: Date; endDate: Date },
        ordersProducts: IOrderProductSumModel[] | undefined,
        ordersProductsSum: IOrderSumModel | undefined
    ) {
        const model: ITobaccoReportPdfPrinterModel = {
            startDate: dates.startDate,
            endDate: dates.endDate,
            items: ordersProducts?.map((el) => ({
                invoiceNumber: el.invoiceNumber,
                name: el.name,
                price: el.price,
                quantity: el.quantity,
                discount: el.discount,
                subTotal: el.subTotal,
                tobaccoTax: el.tobaccoTax,
                vat: el.vat,
                total: el.total,
                startTime: DateUtils.format(el.startTime),
            })),
            totals: {
                subTotal: ordersProductsSum?.subTotal ?? 0,
                discount: ordersProductsSum?.discount ?? 0,
                tobaccoTax: ordersProductsSum?.tobaccoTax ?? 0,
                vat: ordersProductsSum?.vat ?? 0,
                total: ordersProductsSum?.total ?? 0,
            },
        };

        PdfMakeUtils.download(
            TobaccoReportPdfPrinterContent(model),
            "Tobacco Report",
            { isLandScape: true, header: await PdfMakeHeaders.normal(true) }
        );
    }

    static async handleExcelDownload(
        ordersProducts: IOrderProductSumModel[] | undefined,
    ) {
        ExcelSheetHelper.create({
            fileName: "Tobacco Report",
            data: ordersProducts || [],
            cols: [
                { name: TranslateHelper.t(TranslateConstants.INVOICE_NUMBER), key: "invoiceNumber" },
                { name: TranslateHelper.t(TranslateConstants.NAME), key: "name" },
                { name: TranslateHelper.t(TranslateConstants.PRICE), key: "price" },
                { name: TranslateHelper.t(TranslateConstants.QUANTITY), key: "quantity" },
                { name: TranslateHelper.t(TranslateConstants.DISCOUNT), key: "discount" },
                { name: TranslateHelper.t(TranslateConstants.SUB_TOTAL), key: "subTotal" },
                { name: TranslateHelper.t(TranslateConstants.TOBACCO_TAX), key: "tobaccoTax" },
                { name: TranslateHelper.t(TranslateConstants.TAX), key: "vat" },
                { name: TranslateHelper.t(TranslateConstants.TOTAL), key: "total" },
                { name: TranslateHelper.t(TranslateConstants.DATE), key: "startTime", format: (val) => DateUtils.format(val) },
            ]
        });
    }
}
