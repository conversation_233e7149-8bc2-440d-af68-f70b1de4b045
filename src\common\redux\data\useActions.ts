import { IModalProps } from "../../components/ModalComponent";
import {
    setLoadingAction,
    setModalAction,
    closeModalAction,
} from "./slice";
import { useAppDispatch } from "../store";

export default function useActions(): IActions {
    const dispatch = useAppDispatch();

    return {
        openModal: (payload: Partial<IModalProps>) => {
            dispatch(setModalAction({ ...payload, show: true }));
        },
        closeModal: () => {
            dispatch(closeModalAction());
        },
        setLoading: (loading = true) => {
            dispatch(setLoadingAction(loading));
        },
    };
}

export type IActions = {
    openModal: (payload: Partial<IModalProps>) => void;
    closeModal: () => void;
    setLoading: (loading?: boolean) => void;
};
