import { FC } from "react";
import ListComponent from "../../../../../common/components/ListComponent";
import { useAppSelector } from "../../../../../common/redux/store";
import { posAdditionsSelector, posOrderSelector } from "../../../redux/selector";
import EmptyComponent from "../../../../../common/components/EmptyComponent";
import usePosActions from "../../../redux/usePosActions";
import { IAdditionModel } from "../../../../../common/models/AdditionsModel";
import PosBackButtonComponent from "../../../components/PosBackButtonComponent";
import PriceComponent from "../../../../../common/components/PriceComponent";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";
import { PriceUtils } from "../../../../../common/utils/PriceUtils";
import { useTranslate } from "../../../../../common/hooks/useTranslate";

interface IProps {
    productIndex?: number;
    onBack?: () => void;
}

const PosOrderAdditionsModal: FC<IProps> = ({ productIndex, onBack }) => {
    const additions = useAppSelector(posAdditionsSelector);
    const order = useAppSelector(posOrderSelector);
    const posActions = usePosActions();
    const { isArabic } = useTranslate();

    const filteredAdditions = additions.filter((addition) => {
        if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
            const { isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(order.deliveryApp);
            return (addition.deliveryApps as any)?.[isActiveKey];
        }

        return true;
    });

    const handleClick = (addition: IAdditionModel) => {
        const index: number = productIndex ?? order.orderProducts?.length - 1;
        posActions.setOrderProductAddition(index, {
            addition,
            quantity: 1,
            name: addition.name,
            secondName: addition.secondName,
            price: PriceUtils.getProductPrice(addition, order),
        });
    };

    if (!additions.length) return <EmptyComponent className="h-60" />;

    return (
        <>
            <ListComponent padding="0 px-2" cols={3} calcHeight={30}>
                {onBack && <PosBackButtonComponent className="!w-full" onClick={onBack} />}
                {filteredAdditions.map((el, index) => {
                    const name = (!isArabic && el.secondName) ? el.secondName : el.name;
                    return (
                        <div
                            key={index}
                            className={
                                "flex flex-col border border-gray-400 text-lg rounded p-2 py-4 items-center justify-center" +
                                " " +
                                "font-tajawal-bold cursor-pointer hover:scale-95 active:bg-base-200 shadow-md min-h-28"
                            }
                            onClick={() => handleClick(el)}
                        >
                            <span>{name}</span>
                            <span className={el.price === 0 ? "hidden" : ""}>
                                <PriceComponent price={PriceUtils.getProductPrice(el, order)} iconWidth="w-4" />
                            </span>
                        </div>
                    );
                })}
            </ListComponent>
        </>
    );
};

export default PosOrderAdditionsModal;
