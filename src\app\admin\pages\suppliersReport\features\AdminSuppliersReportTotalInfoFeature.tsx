import { FC, useMemo } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IPurchaseInvoiceSumModel } from "../../../../../common/models/PurchaseInvoiceSumModel";
import { IReturnedPurchaseInvoiceSumModel } from "../../../../../common/models/ReturnedPurchaseInvoiceSumModel";

interface IProps {
    purchaseInvoiceSum?: IPurchaseInvoiceSumModel;
    returnedPurchaseInvoiceSum?: IReturnedPurchaseInvoiceSumModel;
    purchaseInvoicePayments?: IPurchaseInvoiceSumModel;
}

const AdminSuppliersReportTotalInfoFeature: FC<IProps> = ({
    purchaseInvoiceSum,
    returnedPurchaseInvoiceSum,
    purchaseInvoicePayments,
}) => {
    const data = useMemo(() => {
        return {
            count: purchaseInvoiceSum?.count ?? 0,
            returnedCount: returnedPurchaseInvoiceSum?.count ?? 0,
            debit: (purchaseInvoicePayments?.cash ?? 0) + (purchaseInvoicePayments?.network ?? 0) + (returnedPurchaseInvoiceSum?.total ?? 0),
            credit: (purchaseInvoiceSum?.total ?? 0)
        };
    }, [
        purchaseInvoiceSum,
        returnedPurchaseInvoiceSum,
        purchaseInvoicePayments,
    ]);

    return (
        <ReportTotalInfoComponent
            items={[
                {
                    text: TranslateConstants.ORDERS_COUNT,
                    value: data?.count,
                    fixedVal: 0,
                },
                {
                    text: TranslateConstants.RETURNED_ORDERS_COUNT,
                    value: data?.returnedCount,
                    fixedVal: 0,
                },
                { text: TranslateConstants.DEBIT_PERSON, value: data?.debit },
                {
                    text: TranslateConstants.CREDIT_PERSON,
                    value: data?.credit,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.CREDIT,
                    value: data?.debit - data?.credit,
                    isHiddenInSm: true,
                },
            ]}
        />
    );
};

export default AdminSuppliersReportTotalInfoFeature;
