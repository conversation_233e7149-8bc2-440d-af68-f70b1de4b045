import { Content } from 'pdfmake/interfaces';
import { IKitchenPdfPrinterBodyModel } from './KitchenPdfPrinterModel';
import { PdfMakeHelper } from '../../PdfMakeHelper';
import { DateUtils } from '../../../utils/DateUtils';
import { IPdfMakePrinterContentOptionsModel } from '../../PdfMakeInterfaces';
import { DefaultPdfMakePrinterContentOptions } from '../../PdfMakeConstants';

export const KitchenPdfPrinterContent = (
    invoice: IKitchenPdfPrinterBodyModel,
    options: IPdfMakePrinterContentOptionsModel = DefaultPdfMakePrinterContentOptions,
): Content[] => {
    const defaultOptions: IPdfMakePrinterContentOptionsModel = { ...DefaultPdfMakePrinterContentOptions, ...options };

    return [
        ...PdfMakeHelper.optionalDocItem(!!defaultOptions?.showLogo && !!defaultOptions.logo, [
            {
                image: defaultOptions.logo!,
                width: 150,
                height: 100,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.organizationName, [
            {
                text: invoice.organizationName!,
                bold: true,
                fontSize: 15,
                marginTop: 5,
            },
        ]),
        PdfMakeHelper.printDivider(),
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderNumber, [
            PdfMakeHelper.textWithBorder(`Order # ${invoice.orderNumber}`),
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderTitle, [
            {
                text: invoice.orderTitle!,
                bold: true,
                fontSize: 14,
                marginTop: 3,
            },
        ]),
        PdfMakeHelper.printDivider(),
        {
            columns: [
                {
                    text: DateUtils.format(Date.now(), "dd-MM-yyyy hh:mm A", true),
                    alignment: 'left',
                    marginBottom: 5,
                },
                {
                    text: 'التاريخ:',
                    alignment: 'right',
                },
            ],
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderType, [
            {
                columns: [
                    {
                        text: invoice.orderType!,
                        alignment: 'left',
                        marginBottom: 5,
                    },
                    {
                        text: 'نوع الطلب:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.table, [
            {
                columns: [
                    {
                        text: invoice.table || '',
                        alignment: 'left',
                    },
                    {
                        text: 'الطاولة:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        PdfMakeHelper.printDivider(),
        {
            table: {
                headerRows: 1,
                widths: ['auto', '*'],
                body: [
                    [
                        { text: 'الكمية', bold: true },
                        { text: 'المنتج', bold: true, alignment: 'right' },
                    ],
                    ...invoice.items.map((product) => {
                        const additions = product.additions?.map((a, index) => {
                            let adName = a.quantity + ' * ' + a.name
                            if (a.secondName) adName += " - " + a.secondName
                            if (index < (product.additions?.length || 0) - 1) adName += " ,"
                            return adName
                        }).join(' ');

                        return [
                            { text: product.quantity, fontSize: 8 },
                            [
                                {
                                    text: product.name,
                                    alignment: 'right',
                                    fontSize: 8,
                                    decoration: product.isDeleted ? 'lineThrough' : undefined,
                                },
                                ...PdfMakeHelper.optionalDocItem(!!product.secondName, [
                                    {
                                        text: product.secondName!,
                                        alignment: 'right',
                                        fontSize: 8,
                                        decoration: product.isDeleted ? 'lineThrough' : undefined,
                                        marginTop: 3,
                                    },
                                ]),
                                !!product.additions?.length
                                    ? PdfMakeHelper.longText(' ) ' + additions + ' ( ', 55, {
                                        alignment: 'right',
                                        fontSize: 8,
                                        decoration: product.isDeleted ? 'lineThrough' : undefined,
                                        margin: [0, 3],
                                    })
                                    : null,
                            ],
                        ];
                    }),
                ],
            },
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.note, [
            PdfMakeHelper.printDivider(),
            {
                text: `ملاحظة: ${invoice.note}`,
                alignment: 'right',
                fontSize: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!invoice.note, [
            PdfMakeHelper.printDivider(false, false, 5),
        ]),
    ];
};
