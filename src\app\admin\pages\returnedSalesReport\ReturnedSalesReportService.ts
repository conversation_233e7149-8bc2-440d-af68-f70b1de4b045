import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { ReturnedSalesReportPdfPrinterContent } from "../../../../common/pdf-make/slices/returnedSalesReport/ReturnedSalesReportPdfPrinterContent";
import { IReturnedSalesReportPdfPrinterModel } from "../../../../common/pdf-make/slices/returnedSalesReport/ReturnedSalesReportPdfPrinterModel";

export class AdminReturnedSalesReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        returnedOrders: IReturnedOrderModel[] | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined
    ) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

        const model: IReturnedSalesReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: returnedOrders?.map((el) => ({
                returnedInvoiceNumber: el.returnedInvoiceNumber,
                invoiceNumber: el.invoiceNumber,
                orderType: el.deliveryApp ?? el.type,
                subTotal: el.subTotal,
                discount: el.discount,
                tobaccoTax: hasTobaccoTax ? el.tobaccoTax : undefined,
                vat: el.vat,
                total: el.total,
                cash: el.cash,
                network: el.network,
                credit: el.deliveryApp ? el.total : 0,
            })),
            totals: {
                count: returnedOrderSum?.count ?? 0,
                vat: returnedOrderSum?.vat ?? 0,
                total: returnedOrderSum?.total ?? 0,
                cash: returnedOrderSum?.cash ?? 0,
                network: returnedOrderSum?.network ?? 0,
                credit: returnedOrderSum?.deliveryApps ?? 0,
            },
        };

        PdfMakeUtils.download(
            ReturnedSalesReportPdfPrinterContent(model),
            "Returned Sales Report",
            { isLandScape: true, header: await PdfMakeHeaders.normal(true) }
        );
    }
}
