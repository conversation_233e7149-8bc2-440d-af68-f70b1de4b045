import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { PurchaseUtils } from "../../../../common/utils/PurchaseUtils";
import { IAdminPurchaseOrderInputs, IAdminPurchaseOrderState } from "./AdminPurchaseOrderInterfaces";

export class AdminPurchaseOrderUtil {
    static handlePurchaseOrderProductsData = (
        purchaseOrderProducts: IAdminPurchaseOrderInputs[],
        isPriceIncludingTax: boolean = true
    ): IAdminPurchaseOrderInputs[] => {
        return purchaseOrderProducts.map((el) => {
            const { subTotal, vat } = PurchaseUtils.getPurchaseProductData(el, isPriceIncludingTax);
            const id = el.id ?? (el as any)?.item?.id ?? (el as any)?.item?.item?.id ?? undefined; // TODO: fix and remove this

            return {
                ...el,
                id,
                subTotal: fixedNumber(subTotal),
                vat: fixedNumber(vat)
            };
        });
    }

    static handelUpdateSelector = (
        item: IPurchaseOrderModel
    ): IAdminPurchaseOrderState => ({
        ...item,
        purchaseOrderProducts: item.purchaseOrderProducts.map((item) => ({
            ...item,
            rawMaterial: {
                id: item.rawMaterialId,
                name: item.name,
                price: item.price,
            } as any,
        })),
    })
}