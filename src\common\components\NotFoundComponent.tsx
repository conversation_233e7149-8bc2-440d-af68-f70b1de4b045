import { useLocation } from "react-router-dom";
import { useTranslate } from "../hooks/useTranslate";
import { TranslateConstants } from "../constants/TranslateConstants";

const NotFoundComponent = () => {
    const { pathname } = useLocation();
    const isNotPage = pathname.includes("admin");
    const { translate } = useTranslate();

    return (
        <div
            className={
                "w-full flex flex-col justify-center items-center" +
                " " +
                (isNotPage ? "h-full" : "h-s")
            }
        >
            <h1 className="font-bold text-lg">
                {translate(TranslateConstants.PAGE_NOT_FOUND)}
            </h1>
        </div>
    );
};

export default NotFoundComponent;
