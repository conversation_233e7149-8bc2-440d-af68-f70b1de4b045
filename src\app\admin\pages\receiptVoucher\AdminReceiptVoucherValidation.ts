import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ReceiptVoucherDestinationEnum } from "../../../../common/enums/DataEnums";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminReceiptVoucherInputs } from "./AdminReceiptVoucherInterface";

export class AdminReceiptVoucherValidation {
    static inputsValidation = (values: IAdminReceiptVoucherInputs) => {
        let isValid = true;

        if (!values.date) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DATE_FIELD_IS_REQUIRED);
        } else if (values.destination === ReceiptVoucherDestinationEnum.CUSTOMER && !values.customer) {
            isValid = false;
            ToastHelper.error(TranslateConstants.CUSTOMER_FIELD_IS_REQUIRED);
        } else if (values.customer && values.customer.credit < values.cash) {
            isValid = false;
            ToastHelper.error(TranslateConstants.CASH_BALANCE_IS_NOT_ENOUGH);
        } else if (values.cash < 0 || values.network < 0 || (values.cash + values.network) === 0) {
            isValid = false;
            ToastHelper.error(TranslateConstants.AMOUNT_MUST_BE_POSITIVE);
        } else if (values.note?.length > 200) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NOTE_SIZE_ERROR);
        }

        return isValid;
    };
}
