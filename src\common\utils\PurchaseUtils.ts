import { fixedNumber } from "./numberUtils";

interface IProduct {
    price: number;
    discount: number;
    quantity: number;
    isTaxable: boolean;
}

export class PurchaseUtils {
    static getPurchaseProductData(
        product: IProduct,
        isPriceIncludingTax: boolean = true
    ) {
        const productPrice = fixedNumber(product.price);
        const productDiscount = fixedNumber(product.discount);
        const productQuantity = fixedNumber(product.quantity);
        const priceAfterDiscount = productPrice - productDiscount;

        if (product.isTaxable) {
            return {
                nativeTotal: fixedNumber((isPriceIncludingTax ? productPrice / 1.15 : productPrice) * productQuantity, 3),
                subTotal: fixedNumber((isPriceIncludingTax ? priceAfterDiscount / 1.15 : priceAfterDiscount) * productQuantity, 3),
                vat: fixedNumber((isPriceIncludingTax ? priceAfterDiscount / 1.15 : priceAfterDiscount) * productQuantity * 0.15, 3),
                total: fixedNumber(((isPriceIncludingTax ? priceAfterDiscount : priceAfterDiscount * 1.15) * productQuantity), 3),
            };
        }

        return {
            nativeTotal: fixedNumber(productPrice * productQuantity, 3),
            subTotal: fixedNumber(priceAfterDiscount * productQuantity, 3),
            vat: 0,
            total: fixedNumber(priceAfterDiscount * productQuantity, 3),
        };
    }

    static getPurchaseData(
        products: IProduct[],
        isPriceIncludingTax: boolean = true
    ) {
        let nativeTotal = 0; // the total before any taxes or discounts
        let productsDiscount = 0; // the total discount on products before taxes
        let subTotal = 0; // the total before taxes and discounts
        let total = 0; // the total after taxes and discounts

        products.forEach((el) => {
            const {
                nativeTotal: productNativeTotal,
                subTotal: productSubTotal,
                total: productTotal
            } = this.getPurchaseProductData(el, isPriceIncludingTax);

            nativeTotal += productNativeTotal;
            subTotal += productSubTotal;
            total += productTotal;
            productsDiscount += (fixedNumber(el.discount) * fixedNumber(el.quantity));
        });

        return {
            nativeTotal: fixedNumber(nativeTotal, 3),
            productsDiscount: fixedNumber(productsDiscount, 3),
            subTotal: fixedNumber(subTotal, 3),
            vat: fixedNumber(total - subTotal, 3),
            total: fixedNumber(total, 3),
        };
    }
}
