import { IAdditionModel } from "../../../common/models/AdditionsModel";
import { ICategoryModel } from "../../../common/models/CategoryModel";
import { IDiscountModel } from "../../../common/models/DiscountModel";
import { IPosOrderModel } from "../../../common/models/PosOrderModel";
import { IProductModel } from "../../../common/models/ProductModel";
import { ITableModel } from "../../../common/models/TableModel";
import { IPosOrder, PosHomeViewType } from "../interface";

export interface IPosHomeState {
    view: PosHomeViewType;
    selectedCategory?: ICategoryModel;
    isShiftOpen?: boolean;
}

export default interface IState {
    products: IProductModel[];
    additions: IAdditionModel[];
    categories: ICategoryModel[];
    tables: ITableModel[];
    discounts: IDiscountModel[];
    order: IPosOrder | IPosOrderModel;
    home: IPosHomeState;
}
