import useFetch from "../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import useActions from "../../../../common/redux/data/useActions";
import { AdminRawMaterialsDataTableHeaders } from "./AdminRawMaterialsConstants";
import AdminRawMaterialsModal from "./modals/AdminRawMaterialsModal";
import { RawMaterialApiRepo } from "../../../../common/repos/api/RawMaterialApiRepo";
import { IRawMaterialModel } from "../../../../common/models/RawMaterialModel";

const AdminRawMaterialsPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.RAW_MATERIALS,
        () => RawMaterialApiRepo.getRawMaterials(undefined, undefined, true)
    );

    const handleOnClick = (isEdit?: boolean, item?: IRawMaterialModel) => {
        actions.openModal({
            component: <AdminRawMaterialsModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminRawMaterialsDataTableHeaders}
                    items={data || []}
                    selectors={(item: IRawMaterialModel) => [
                        item.number,
                        item.name,
                        item.supplier?.name,
                        item.price,
                    ]}
                    showEditButton={true}
                    onEdit={(item: IRawMaterialModel) => handleOnClick(true, item)}
                />
            </StatusComponent>
        </>
    );
};

export default AdminRawMaterialsPage;
