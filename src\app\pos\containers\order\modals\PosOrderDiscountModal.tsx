import { useEffect, useState } from "react";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { IDiscountModel } from "../../../../../common/models/DiscountModel";
import { useAppSelector } from "../../../../../common/redux/store";
import { posDiscountsSelector, posOrderSelector } from "../../../redux/selector";
import usePosActions from "../../../redux/usePosActions";
import useActions from "../../../../../common/redux/data/useActions";

const PosOrderDiscountModal = () => {
    const [selected, setSelected] = useState<IDiscountModel>();
    const discounts = useAppSelector(posDiscountsSelector);
    const order = useAppSelector(posOrderSelector);
    const actions = useActions();
    const posActions = usePosActions();

    useEffect(() => {
        if (order.selectedDiscount) {
            const discount = discounts.find((el) => el.id === order.selectedDiscount?.id);
            if (discount) setSelected(discount);
        }
    }, [order]);

    const handleOnSelect = (el: IDiscountModel) => {
        if (selected?.id === el.id) setSelected(undefined);
        else setSelected(el);
    };

    const handleOnSave = () => {
        posActions.setOrder({ selectedDiscount: selected });
        actions.closeModal();
    }

    return (
        <>
            <ListComponent calcHeight={40} cols={2} padding="0 px-2">
                {discounts.map((el) => (
                    <ButtonComponent
                        key={el.id}
                        text={el.name}
                        className="h-20 text-2xl"
                        bgColor={el.id === selected?.id ? "primary" : "transparent"}
                        textColor={el.id === selected?.id ? "white" : "primary"}
                        borderColor="primary"
                        onClick={() => handleOnSelect(el)}
                    />
                ))}
            </ListComponent>
            <ModalButtonsComponent
                text={TranslateConstants.SAVE}
                isDisabled={order.selectedDiscount?.id === selected?.id}
                onClick={handleOnSave}
            />
        </>
    );
};

export default PosOrderDiscountModal;
