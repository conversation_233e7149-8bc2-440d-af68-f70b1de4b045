import { IAdminAdditionInputs } from "../../../app/admin/pages/additions/AdminAdditionsInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IAdditionModel } from "../../models/AdditionsModel";
import { IMostSellingProductsModel } from "../../models/MostSellingProductsModel";
import { debug } from "../../utils/CommonUtils";

export class AdditionsApiRepo {
    static async getAdditions(active?: boolean, deliveryApps?: boolean) {
        try {
            const res = await AxiosHelper.get<IAdditionModel[]>(
                EndPointsEnums.ADDITIONS,
                { params: { active, deliveryApps } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`AdditionsApiRepo [getAdditions] Error: `, error);
            throw error;
        }
    }

    static async addAddition(body: IAdminAdditionInputs) {
        try {
            const res = await AxiosHelper.post<IAdditionModel>(
                EndPointsEnums.ADDITIONS,
                body
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`AdditionsApiRepo [addAddition] Error: `, error);
            throw error;
        }
    }

    static async updateAddition(id: number, body: IAdminAdditionInputs) {
        try {
            const res = await AxiosHelper.patch<IAdditionModel>(
                EndPointsEnums.ADDITIONS,
                id,
                body
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`AdditionsApiRepo [updateAddition] Error: `, error);
            throw error;
        }
    }

    static async getSum(startTime: number, endTime: number) {
        try {
            const res = await AxiosHelper.get<IMostSellingProductsModel[]>(
                EndPointsEnums.ORDER_PRODUCTS_ADDITIONS_SUM,
                { params: { startTime, endTime } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`AdditionsApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}