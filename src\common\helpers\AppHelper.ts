import { testKeyConstant } from "../constants/ConfigConstants";
import { SessionStorageHelper } from "./SessionStorageHelper";

export class AppHelper {
    static isTestMode() {
        return SessionStorageHelper.get(testKeyConstant) === "true";
    }

    static isDevMode() {
        return process.env.NODE_ENV === "development" || this.isTestMode();
    }

    static isDevEnv() {
        return process.env.NODE_ENV === "development";
    }
}