import { FC } from "react";
import { IAdminCustomerInputs } from "../AdminCustomersInterface";
import { AdminCustomerInputs } from "../AdminCustomersConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { CustomersApiRepo } from "../../../../../common/repos/api/CustomerApiRepo";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminCustomersValidation } from "../AdminCustomersValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";
import { ICustomerModel } from "../../../../../common/models/CustomerModel";

interface IProps {
    item?: ICustomerModel;
}

const AdminCustomersModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminCustomerInputs>(
        AdminCustomerInputs,
        { updateState: item }
    );
    const addCustomer = useFlatMutate(CustomersApiRepo.addCustomer, {
        updateCached: { key: EndPointsEnums.CUSTOMERS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateCustomer = useFlatMutate(CustomersApiRepo.updateCustomer, {
        updateCached: {
            key: EndPointsEnums.CUSTOMERS,
            operation: "update",
            selector: (data: ICustomerModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminCustomersValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateCustomer(item.id, state);
        addCustomer(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(value) => setState({ ...state, name: value })}
                    value={state.name}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.MOBILE}
                    onChange={(value) => setState({ ...state, mobile: value })}
                    value={state.mobile}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.TAX_NUMBER}
                    onChange={(value) => setState({ ...state, taxNumber: value })}
                    value={state.taxNumber}
                />
                <InputComponent
                    label={TranslateConstants.ADDRESS}
                    onChange={(value) => setState({ ...state, address: value })}
                    value={state.address}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminCustomersModal;
