import { BaseModel } from ".";
import { DeliveryAppEnum, OrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";
import { ICustomerModel } from "./CustomerModel";

export interface IOrderProductAdditionModel extends BaseModel {
    additionId: number;
    quantity: number;
    price: number;
    name: string;
    secondName?: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    startTime: number;
}

export interface IOrderProductModel extends BaseModel {
    productId: number;
    quantity: number;
    price: number;
    name: string;
    secondName?: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: number;
    isSubjectToTobaccoTax: boolean;
    orderProductAdditions: IOrderProductAdditionModel[];
}

export interface IOrderModel extends BaseModel {
    number: number;
    orderId: number;
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    orderProducts: IOrderProductModel[];
    tableId?: number;
    selectedDiscountId?: number;
    status: OrderStatusEnum;
    invoiceNumber: string;
    orderNumber: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
    cash: number;
    network: number;
    deferred: number;
    startTime: number;
    endTime: number;
    customerId?: number;
    customer?: ICustomerModel;
}