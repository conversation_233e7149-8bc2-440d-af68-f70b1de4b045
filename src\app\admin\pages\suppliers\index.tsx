import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import useActions from "../../../../common/redux/data/useActions";
import { AdminSuppliersDataTableHeaders } from "./AdminSuppliersConstants";
import AdminSuppliersModal from "./modals/AdminSuppliersModal";
import { ISupplierModel } from "../../../../common/models/SuppliersModel";
import { SuppliersApiRepo } from "../../../../common/repos/api/SupplierApiRepo";

const AdminSuppliersPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.SUPPLIERS,
        SuppliersApiRepo.getSuppliers
    );

    const updateSupplier = useFlatMutate(SuppliersApiRepo.updateSupplier, {
        updateCached: {
            key: EndPointsEnums.SUPPLIERS,
            operation: "update",
            selector: (data: ISupplierModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: ISupplierModel) =>
        updateSupplier(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: ISupplierModel) => {
        actions.openModal({
            component: <AdminSuppliersModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminSuppliersDataTableHeaders}
                    items={data || []}
                    selectors={(item: ISupplierModel) => [
                        item.number,
                        item.name,
                        item.mobile,
                        item.taxNumber,
                        item.address,
                    ]}
                    showEditButton={true}
                    onEdit={(item: ISupplierModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: ISupplierModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminSuppliersPage;
