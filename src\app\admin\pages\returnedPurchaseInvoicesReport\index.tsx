import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminReturnedPurchasesReportTableHeaders } from "./AdminReturnedPurchasesReportsConstants";
import AdminReturnedPurchasesReportTotalInfoFeature from "./features/AdminPurchasesReportTotalInfoFeature";
import { AdminReturnedPurchasesReportService } from "./ReturnedPurchasesReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { AdminReturnedPurchaseInvoiceService } from "../returnedPurchaseInvoices/AdminReturnedPurchaseInvoiceService";
import { IReturnedPurchaseInvoiceModel } from "../../../../common/models/ReturnedPurchaseInvoiceModel";

const AdminReturnedPurchasesReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: returnedPurchaseInvoicesSum,
        isLoading: returnedPurchaseInvoicesSumLoading,
        isError: returnedPurchaseInvoicesSumError,
        refetch: refetchReturnedPurchaseInvoicesSum,
    } = useFetch(
        "report-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedPurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { autoFetchOnMount: true }
    );

    const {
        data: returnedPurchaseInvoices,
        isLoading: returnedPurchaseInvoicesLoading,
        isError: returnedPurchaseInvoicesError,
        refetch: refetchReturnedPurchaseInvoices,
    } = useFetch(
        "report-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedPurchaseInvoiceApiRepo.getReturnedPurchaseInvoices({ startTime, endTime }),
        { autoFetchOnMount: true }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchReturnedPurchaseInvoicesSum(start, end);
        refetchReturnedPurchaseInvoices(start, end);
    };

    const onDownload = () =>
        AdminReturnedPurchasesReportService.handleDownload(
            dateRange,
            returnedPurchaseInvoices,
            returnedPurchaseInvoicesSum
        );

    const handleOnPrint = (item: IReturnedPurchaseInvoiceModel) =>
        AdminReturnedPurchaseInvoiceService.handlePreview(item);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!returnedPurchaseInvoices?.length || !returnedPurchaseInvoicesSum}
            />
            <AdminReturnedPurchasesReportTotalInfoFeature
                returnedPurchaseInvoicesSum={returnedPurchaseInvoicesSum}
            />
            <StatusComponent
                isEmpty={!returnedPurchaseInvoices?.length}
                isLoading={returnedPurchaseInvoicesSumLoading || returnedPurchaseInvoicesLoading}
                isError={returnedPurchaseInvoicesSumError || returnedPurchaseInvoicesError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminReturnedPurchasesReportTableHeaders}
                    items={returnedPurchaseInvoices || []}
                    selectors={(item: IReturnedPurchaseInvoiceModel) => [
                        item.number.toString(),
                        item.supplier?.name,
                        item.subTotal.toFixed(2),
                        item.productsDiscount.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                    ]}
                    showPrintButton={true}
                    onPrint={handleOnPrint}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminReturnedPurchasesReportPage;
