import { useState } from "react";
import AdminReturnedPurchaseInvoiceInputsFeature from "./features/AdminReturnedPurchaseInvoiceInputsFeature";
import AdminReturnedPurchaseInvoiceViewFeature from "./features/AdminReturnedPurchaseInvoiceViewFeature";

const AdminReturnedPurchaseInvoicePage = () => {
    const [isView, setIsView] = useState(true);

    return (
        <>
            {isView && (
                <AdminReturnedPurchaseInvoiceViewFeature setIsView={setIsView} />
            )}
            {!isView && (
                <AdminReturnedPurchaseInvoiceInputsFeature setIsView={setIsView} />
            )}
        </>
    );
};

export default AdminReturnedPurchaseInvoicePage;
