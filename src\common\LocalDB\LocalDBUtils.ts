import { ILocalDBWhere } from "./LocalDBInterface";

export const LocalDbCheckConditionUtil = <T>(
    fieldValue: T,
    operator: string,
    value: any
): boolean => {
    switch (operator) {
        case "in":
            return Array.isArray(fieldValue) && fieldValue.includes(value);
        case "not in":
            return Array.isArray(fieldValue) && !fieldValue.includes(value);
        case "like":
            return (
                (typeof fieldValue === "string" || typeof fieldValue === "number") &&
                fieldValue
                    .toString()
                    .toLowerCase()
                    .includes(value.toString().toLowerCase())
            );
        case "not like":
            return (
                (typeof fieldValue === "string" || typeof fieldValue === "number") &&
                !fieldValue
                    .toString()
                    .toLowerCase()
                    .includes(value.toString().toLowerCase())
            );
        case ">date": {
            const date = new Date(`${fieldValue}`);
            return !isNaN(date.getTime()) && date >= new Date(value);
        }
        case "<date": {
            const date = new Date(`${fieldValue}`);
            return !isNaN(date.getTime()) && date <= new Date(value);
        }
        default:
            return eval(`fieldValue ${operator} value`);
    }
};

export const LocalDbHandleWhereUtil = <T>(
    valueToCheck: any,
    where?: ILocalDBWhere<T>
): boolean => {
    if (!where) return true;

    return where.some((conditions) => {
        return conditions.every(([key, operator, value]) => {
            const keyString = key as string;
            if (keyString.includes(".")) {
                const keys = keyString.split(".");
                let temp = valueToCheck;
                keys.forEach((k) => {
                    temp = temp[k];
                });

                return LocalDbCheckConditionUtil(temp, operator, value);
            }
            return LocalDbCheckConditionUtil(valueToCheck[key], operator, value);
        });
    });
};

export const LocalDbHandleOrderByUtil = <T>(
    data: T[],
    [key, order]: [string, "desc" | "asc"]
): T[] => {
    return data.sort((a: any, b: any) => {
        if (typeof a[key] === "string") {
            const numberRegex = /^[0-9]+$/;
            if (numberRegex.test(a[key]) && numberRegex.test(b[key])) {
                return order === "asc"
                    ? parseInt(a[key]) - parseInt(b[key])
                    : parseInt(b[key]) - parseInt(a[key]);
            }
            return order === "asc"
                ? a[key].localeCompare(b[key])
                : b[key].localeCompare(a[key]);
        } else if (a[key] instanceof Date) {
            return order === "asc"
                ? a[key].getTime() - b[key].getTime()
                : b[key].getTime() - a[key].getTime();
        }
        return order === "asc" ? a[key] - b[key] : b[key] - a[key];
    });
};

export const LocalDbHandleGetKeyRangeUtil = (
    skip: number = 0,
    skipUntil: number = Infinity,
    key?: IDBValidKey,
): IDBValidKey | IDBKeyRange | undefined => {
    if (key) {
        return key;
    } else if (skip !== 0 && skipUntil !== Infinity) {
        return IDBKeyRange.bound(skip, skipUntil, true, true);
    } else if (skip !== 0) {
        return IDBKeyRange.lowerBound(skip, true);
    } else if (skipUntil !== Infinity) {
        return IDBKeyRange.upperBound(skipUntil, true);
    }
    return undefined;
};
