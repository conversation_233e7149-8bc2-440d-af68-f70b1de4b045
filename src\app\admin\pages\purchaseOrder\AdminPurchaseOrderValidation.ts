import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { IAdminPurchaseOrderState } from "./AdminPurchaseOrderInterfaces";

export class AdminPurchaseOrderValidation {
    static inputsValidation = (values: IAdminPurchaseOrderState) => {
        let isValid = true;

        if (!values.date) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DATE_FIELD_IS_REQUIRED);
        } else if (!values.dueDate) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DUE_DATE_FIELD_IS_REQUIRED);
        } else if (!values.supplier) {
            isValid = false;
            ToastHelper.error(TranslateConstants.SUPPLIER_FIELD_IS_REQUIRED);
        } else if (
            !values.purchaseOrderProducts.length ||
            values.purchaseOrderProducts.some((rawMaterial) =>
                !rawMaterial.rawMaterial ||
                !rawMaterial.quantity || Number(rawMaterial.quantity) <= 0 ||
                [0, undefined].includes(rawMaterial.price) || Number(rawMaterial.price) < 0 ||
                !rawMaterial.total || Number(rawMaterial.total) < 0 ||
                Number(rawMaterial.discount) < 0
            )
        ) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRODUCTS_FIELD_IS_NOT_VALID);
        }

        return isValid;
    };

    static checkDeeplyEqual = (values: IAdminPurchaseOrderState, purchaseOrder?: IPurchaseOrderModel) => {
        if (!purchaseOrder) return false;

        return (
            values.date === purchaseOrder.date &&
            values.dueDate === purchaseOrder.dueDate &&
            values.paymentMethod === purchaseOrder.paymentMethod &&
            values.supplier?.id === purchaseOrder.supplier.id &&
            values.isPriceIncludingTax === purchaseOrder.isPriceIncludingTax &&
            values.purchaseOrderProducts.length === purchaseOrder.purchaseOrderProducts.length &&
            values.purchaseOrderProducts.every((rawMaterial, index) => {
                const purchaseOrderProduct = purchaseOrder.purchaseOrderProducts[index];
                return (
                    rawMaterial.rawMaterial.id == purchaseOrderProduct.rawMaterialId &&
                    rawMaterial.isTaxable == purchaseOrderProduct.isTaxable &&
                    rawMaterial.quantity == purchaseOrderProduct.quantity &&
                    rawMaterial.price == purchaseOrderProduct.price &&
                    rawMaterial.discount == purchaseOrderProduct.discount &&
                    rawMaterial.subTotal == purchaseOrderProduct.subTotal &&
                    rawMaterial.vat == purchaseOrderProduct.vat &&
                    rawMaterial.total == purchaseOrderProduct.total
                );
            }) &&
            values.note?.trim() === purchaseOrder.note?.trim() &&
            values.nativeTotal === purchaseOrder.nativeTotal &&
            values.productsDiscount === purchaseOrder.productsDiscount &&
            values.subTotal === purchaseOrder.subTotal &&
            values.vat === purchaseOrder.vat &&
            values.total === purchaseOrder.total
        );
    };
}
