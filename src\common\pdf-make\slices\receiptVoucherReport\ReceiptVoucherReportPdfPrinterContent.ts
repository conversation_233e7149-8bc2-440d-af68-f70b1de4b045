import { Content } from "pdfmake/interfaces";
import { IReceiptVoucherReportPdfPrinterModel } from "./ReceiptVoucherReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const ReceiptVoucherReportPdfPrinterContent = (model: IReceiptVoucherReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "تقرير سندات القبض", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: ["*", "auto", "auto", "auto", "*", "auto"],
                body: [
                    [
                        { text: "البيان", style: "tableHeader" },
                        { text: "التاريخ", style: "tableHeader" },
                        { text: "نقداً", style: "tableHeader" },
                        { text: "شبكة", style: "tableHeader" },
                        { text: "الوجهة", style: "tableHeader" },
                        { text: "رقم السند", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        PdfMakeHelper.reverseTextIfArabic(el.note),
                        el.date,
                        el.cash.toFixed(2),
                        el.network.toFixed(2),
                        el.destination ? PdfMakeHelper.reverseTextIfArabic(el.destination) : "-",
                        el.number,
                    ]),
                ],
            },
        },
        {
            marginTop: 40,
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { text: model.totals.count.toString(), margin: [0, 3, 0, 0] },
                                { text: "عدد السندات:", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { svg: RsSvg("#1F618D"), width: 14 },
                                { text: model.totals.cash.toFixed(2), margin: [0, 3, 0, 0] },
                                { text: "نقداً: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
        {
            columns: [
                {
                    layout: "noBorders",
                    alignment: "right",
                    table: {
                        widths: ["*", "auto", "auto", "auto"],
                        body: [
                            [
                                { text: "" },
                                { svg: RsSvg("#1F618D"), width: 14 },
                                { text: model.totals.network.toFixed(2), margin: [0, 3, 0, 0] },
                                { text: "شبكة: ", margin: [0, 2, 0, 0] },
                            ]
                        ]
                    }
                }
            ]
        },
    ];
}
