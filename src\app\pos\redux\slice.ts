import { createSlice } from "@reduxjs/toolkit";
import InitialState from "./state";
import {
    setData,
    setHome,
    setOrder,
    setOrderProduct,
    setOrderProductAddition,
    updateTable,
} from "./actions";

export const PosSlice = createSlice({
    name: "pos",
    initialState: InitialState,
    reducers: {
        setDataAction: setData,
        updateTableAction: updateTable,
        setOrderAction: setOrder,
        setOrderProductsAction: setOrderProduct,
        setHomeAction: setHome,
        setOrderProductAdditionAction: setOrderProductAddition,
    },
});

export const {
    setDataAction,
    setOrderAction,
    setOrderProductsAction,
    setHomeAction,
    updateTableAction,
    setOrderProductAdditionAction,
} = PosSlice.actions;

export default PosSlice.reducer;
