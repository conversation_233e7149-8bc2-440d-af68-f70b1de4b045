import { FC, useMemo } from "react";
import { IProfitAndLossReportTotalInfoInterface } from "../ProfitAndLossReportInterface";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    data: IProfitAndLossReportTotalInfoInterface;
}

const AdminProfitAndLossReportDataFeature: FC<IProps> = ({ data }) => {
    const formatData = useMemo(() => {
        return [
            { text: TranslateConstants.TOTAL_SALES, value: data.ordersTotal },
            {
                text: TranslateConstants.TOTAL_RETURNED_SALES,
                value: data.returnedOrdersTotal,
            },
            {
                text: TranslateConstants.SALES_NET,
                value: data.salesNet,
                isColored: true,
            },
            {
                text: TranslateConstants.TOTAL_PURCHASES,
                value: data.purchaseInvoicesTotal,
            },
            {
                text: TranslateConstants.TOTAL_RETURNED_PURCHASE_INVOICES,
                value: data.returnedPurchaseInvoicesTotal,
            },
            {
                text: TranslateConstants.PURCHASES_NET,
                value: data.purchasesNet,
                isColored: true,
            },
            ...(data.paymentVouchersSum ?? [])
                .filter((el) => el.total > 0)
                .map((el) => ({ text: el.destination, value: el.total })),
            {
                text: TranslateConstants.EXPENSES_NET,
                value: data.paymentVouchersTotal,
                isColored: true,
            },
            {
                text:
                    data.totalNet >= 0
                        ? TranslateConstants.TOTAL_PROFIT
                        : TranslateConstants.TOTAL_LOSS,
                value: data.totalNet,
            },
        ];
    }, [data]);

    return (
        <div className="sm:text-lg font-tajawal-bold">
            {formatData.map((item, index) => (
                <div
                    key={index}
                    className={
                        "border-b border-gray-400 text-center flex" +
                        " " +
                        (item.isColored ? "bg-[#226bb2] !text-white" : "")
                    }
                >
                    <div className="p-2 flex-1 border-l border-gray-400">
                        {TranslateHelper.t(item.text)}
                    </div>
                    <div className="p-2 w-1/3 sm:flex-1">{fixedNumber(item.value)}</div>
                </div>
            ))}
        </div>
    );
};

export default AdminProfitAndLossReportDataFeature;
