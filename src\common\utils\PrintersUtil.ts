import { IPosOrder, IPosReturnedOrder } from "../../app/pos/interface";
import { TranslateConstants } from "../constants/TranslateConstants";
import { KitchenOrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";
import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { PrintersHelper } from "../helpers/PrintersHelper";
import { QrHelper } from "../helpers/QrHelper";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { IKitchenPrinter } from "../interfaces";
import { IOrderModel } from "../models/OrderModel";
import { IPdfMakePrinterModel } from "../pdf-make/PdfMakeInterfaces";
import { ChequePdfPrinterContent } from "../pdf-make/slices/cheque/chequePdfPrinterContent";
import { InvoicePdfPrinterContent } from "../pdf-make/slices/invoice/InvoicePdfPrinterContent";
import {
    IInvoicePdfPrinterBodyModel,
    IInvoicePrinterItemModel
} from "../pdf-make/slices/invoice/InvoicePdfPrinterModel";
import { KitchenPdfPrinterContent } from "../pdf-make/slices/kitchen/KitchenPdfPrinterContent";
import { IKitchenPdfPrinterItemModel } from "../pdf-make/slices/kitchen/KitchenPdfPrinterModel";
import { ReturnedInvoicePdfPrinterContent } from "../pdf-make/slices/returnedInvoice/ReturnedInvoicePdfPrinterContent";
import { IReturnedInvoicePrinterItemModel } from "../pdf-make/slices/returnedInvoice/ReturnedInvoicePdfPrinterModel";
import { getOrderType } from "./CommonUtils";
import { DateUtils } from "./DateUtils";
import { fixedNumber } from "./numberUtils";
import { orderUtils } from "./OrderUtils";

export class PrintersUtils {
    static handleOrderData(order: IPosOrder | IOrderModel): Partial<IInvoicePdfPrinterBodyModel> {
        const organization = OrganizationHelper.getOrganization();
        const hasVat = OrganizationHelper.hasVat();
        const invoiceTitle = TranslateHelper.t(
            hasVat
                ? TranslateConstants.SIMPLE_VAT_INVOICE
                : TranslateConstants.SIMPLE_INVOICE
        );

        const items: IInvoicePrinterItemModel[] = order.orderProducts.map((el) => {
            return {
                name: el.name,
                secondName: el.secondName,
                price: (el.price * el.quantity).toFixed(2),
                quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                additions: el.orderProductAdditions?.map((ad) => ({
                    name: ad.name,
                    secondName: ad.secondName,
                    price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                    quantity:
                        fixedNumber(ad.price) +
                        " x " +
                        fixedNumber(ad.quantity * el.quantity),
                })),
            };
        });

        return {
            organizationName: organization?.name || "",
            organizationSubName: organization?.subName || "",
            orderType: getOrderType(order),
            invoiceNumber: order.invoiceNumber || "",
            orderNumber: order.orderNumber || "",
            items,
            invoiceTitle,
            address: organization?.address || "",
            vatNo: organization?.taxNumber || "",
            crNo: organization?.registrationNumber || "",
            licenseNo: organization?.licenseNumber || "",
            phone: organization?.mobile || "",
            cash: order.cash.toFixed(2),
            network: order.network.toFixed(2),
            deferred: order.deferred.toFixed(2),
            customerName: order.customer?.name || "",
            customerMobile: order.customer?.mobile || "",
            customerTaxNumber: order.customer?.taxNumber || "",
            customerAddress: order.customer?.address || "",
            footer: organization?.invoiceFooter || "",
            invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
        }
    }

    static async handleInvoiceOrderPrint(order: IPosOrder): Promise<IPdfMakePrinterModel> {
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        if (!printerId) throw new Error("Printer not found");

        const { subTotal, vat, total, tobaccoTax, nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order);

        return {
            printerId,
            body: InvoicePdfPrinterContent({
                ...this.handleOrderData(order) as IInvoicePdfPrinterBodyModel,
                qrCode: QrHelper.vatQrData(total, vat),
                subTotal: subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                vat: vat.toFixed(2),
                total: total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                totalDeliverApp:
                    order.type === OrderTypeEnum.DELIVERY_APP
                        ? order.total.toFixed(2)
                        : "",
                tobaccoTax: tobaccoTax.toFixed(2),
            }, {
                // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                logo: './images/logo.jpg',
                showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
            }),
        };
    }

    static async handleReturnedInvoiceOrderPrint(
        order: IPosReturnedOrder
    ): Promise<IPdfMakePrinterModel> {
        const organization = OrganizationHelper.getOrganization();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const invoiceTitle = TranslateHelper.t(TranslateConstants.RETURNED_INVOICE);

        if (!printerId) throw new Error("Printer not found");

        const { nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order as IPosOrder);

        const items: IReturnedInvoicePrinterItemModel[] = order.orderProducts.filter((el) => el.quantity !== 0)
            .map((el) => {
                return {
                    name: el.name,
                    secondName: el.secondName,
                    price: (el.price * el.quantity).toFixed(2),
                    quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                    additions: el.orderProductAdditions?.map((ad) => ({
                        name: ad.name,
                        secondName: ad.secondName,
                        price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                        quantity:
                            fixedNumber(ad.price) +
                            " x " +
                            fixedNumber(ad.quantity * el.quantity),
                    })),
                };
            });

        return {
            printerId,
            body: ReturnedInvoicePdfPrinterContent({
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                invoiceNumber: order.invoiceNumber || "",
                returnedInvoiceNumber: order.returnedInvoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                licenseNo: organization?.licenseNumber || "",
                phone: organization?.mobile || "",
                subTotal: order.subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                vat: order.vat.toFixed(2),
                total: order.total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                cash: order.cash.toFixed(2),
                network: order.network.toFixed(2),
                deferred: order.deferred.toFixed(2),
                tobaccoTax: order.tobaccoTax.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                footer: organization?.invoiceFooter || "",
                invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
            }, {
                // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                logo: './images/logo.jpg',
                showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
            }),
        };
    }

    static async handleChequeOrderPrint(order: IPosOrder): Promise<IPdfMakePrinterModel> {
        const organization = OrganizationHelper.getOrganization();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const chequeTitle = TranslateHelper.t(TranslateConstants.CHEQUE);

        if (!printerId) throw new Error("Printer not found");

        const items: IInvoicePrinterItemModel[] = order.orderProducts.map((el) => {
            return {
                name: el.name,
                secondName: el.secondName,
                price: (el.price * el.quantity).toFixed(2),
                quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                additions: el.orderProductAdditions?.map((ad) => ({
                    name: ad.name,
                    secondName: ad.secondName,
                    price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                    quantity:
                        fixedNumber(ad.price) +
                        " x " +
                        fixedNumber(ad.quantity * el.quantity),
                })),
            };
        });

        const { subTotal, vat, total, tobaccoTax, nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order);

        return {
            printerId,
            body: ChequePdfPrinterContent({
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                invoiceNumber: order.invoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle: chequeTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                phone: organization?.mobile || "",
                subTotal: subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                tobaccoTax: tobaccoTax.toFixed(2),
                vat: vat.toFixed(2),
                total: total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
                footer: organization?.invoiceFooter || "",
            }, {
                // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                logo: './images/logo.jpg',
                showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
            }),
        };
    }

    static async handelKitchenOrderPrint(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): Promise<IPdfMakePrinterModel[]> {
        const kitchenPrinterItems = this.handleKitchenPrinterItems(order, orderStatus);
        const organization = OrganizationHelper.getOrganization();
        const result: IPdfMakePrinterModel[] = [];

        kitchenPrinterItems.forEach(async (item) => {
            result.push({
                printerId: item.printerId,
                body: KitchenPdfPrinterContent({
                    items: item.products,
                    organizationName: organization?.name || "",
                    orderNumber: order.orderNumber || "",
                    orderType: TranslateHelper.t(order.deliveryApp ?? order.type),
                    table: order.table?.name || "",
                    orderTitle: TranslateHelper.t(orderStatus),
                }, {
                    // logo: await OrganizationHelper.getAsyncBase64Logo() || './images/logo.jpg',
                    logo: './images/logo.jpg',
                    showLogo: PrintersHelper.getPrintingSettings().isLogoPrintingActive,
                }),
            });
        });

        return result;
    }

    private static handleKitchenPrinterItems(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): IKitchenPrinter[] {
        const result: IKitchenPrinter[] = [];
        const printers = PrintersHelper.getPrinters();

        order.orderProducts.forEach((el) => {
            const printer = printers.find(
                (p) => p.categoryId === el.product.categoryId.toString()
            );
            if (!printer?.printer) return;

            const kitchenOrder = result.find(
                (ko) => ko.printerId === printer.printer
            );
            const isDeleted = orderStatus === KitchenOrderStatusEnum.DELETE || el.isDeleted;

            const item: IKitchenPdfPrinterItemModel = {
                name: el.name,
                secondName: el.secondName,
                quantity: (isDeleted ? "-" : "") + fixedNumber(el.quantity).toString(),
                additions: el.orderProductAdditions?.map((ad) => ({
                    name: ad.name,
                    secondName: ad.secondName,
                    quantity: fixedNumber(ad.quantity).toString(),
                })),
                isDeleted
            };

            if (kitchenOrder) {
                kitchenOrder.products.push(item);
            } else {
                result.push({
                    printerId: printer.printer,
                    products: [item],
                });
            }
            return;
        });

        return result;
    }
}
