import { useEffect, useState } from "react";
import AdminPurchaseInvoiceInputsFeature from "./features/AdminPurchaseInvoiceInputsFeature";
import AdminPurchaseInvoiceViewFeature from "./features/AdminPurchaseInvoiceViewFeature";
import { useLocation, useNavigate } from "react-router-dom";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { AdminPurchaseInvoiceUtil } from "./AdminPurchaseInvoiceUtil";
import { IAdminPurchaseInvoiceState } from "./AdminPurchaseInvoiceInterfaces";

const AdminPurchaseInvoicePage = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [isView, setIsView] = useState(true);
    const [convertedItem, setConvertedItem] = useState<
        IAdminPurchaseInvoiceState | undefined
    >(undefined);
    const [purchaseOrderId, setPurchaseOrderId] = useState<number | undefined>(undefined);

    useEffect(() => {
        if (isView) {
            setConvertedItem(undefined);
            navigate(".", { state: undefined });
            setPurchaseOrderId(undefined);
        }
    }, [isView]);

    useEffect(() => {
        if (location.state) {
            setConvertedItem(
                AdminPurchaseInvoiceUtil.convertPurchaseOrderToPurchaseInvoice(
                    location.state as IPurchaseOrderModel
                )
            );
            setPurchaseOrderId((location.state as IPurchaseOrderModel).id);
            setIsView(false);
        }
    }, [location.state]);

    return (
        <>
            {isView && (
                <AdminPurchaseInvoiceViewFeature
                    setIsView={setIsView}
                />
            )}
            {!isView && (
                <AdminPurchaseInvoiceInputsFeature
                    setIsView={setIsView}
                    convertedItem={convertedItem}
                    purchaseOrderId={purchaseOrderId}
                />
            )}
        </>
    );
};

export default AdminPurchaseInvoicePage;
