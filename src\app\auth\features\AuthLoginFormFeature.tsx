import { useState } from "react";
import { AuthService } from "../AuthService";
import { IAuthLoginInputs, IAuthLoginState } from "../AuthInterface";
import { AuthLoginInitialState } from "../AuthConstants";
import { useNavigate } from "react-router-dom";
import { useTranslate } from "../../../common/hooks/useTranslate";
import InputComponent from "../../../common/components/InputComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import ButtonComponent from "../../../common/components/ButtonComponent";

const AuthLoginFormFeature = () => {
    const { translate } = useTranslate()
    const navigate = useNavigate()
    const [state, setState] = useState<IAuthLoginState>(AuthLoginInitialState);

    const handleOnInputChange = (key: keyof IAuthLoginInputs, value: string) => {
        setState({
            ...state,
            inputs: { ...state.inputs, [key]: value },
        });
    };

    const onSubmit = async () => {
        await AuthService.login(
            state.inputs.email,
            state.inputs.password,
            setState,
            navigate
        );
    };

    return (
        <form className="w-full">
            <InputComponent
                type="email"
                placeholder={translate(TranslateConstants.EMAIL)}
                value={state.inputs.email}
                className="border-b-0 rounded-none rounded-t bg-white"
                onChange={(val) => handleOnInputChange("email", val)}
            />
            <InputComponent
                type="password"
                placeholder={translate(TranslateConstants.PASSWORD)}
                value={state.inputs.password}
                className="rounded-none rounded-b bg-white"
                onChange={(val) => handleOnInputChange("password", val)}
            />
            {!!state.error && (
                <p className="text-red-500 text-center w-full mt-1">{translate(state.error)}</p>
            )}
            <ButtonComponent
                text={translate(TranslateConstants.LOGIN)}
                isLoading={state.loading}
                onClick={onSubmit}
                isDisabled={state.inputs.email === "" || state.inputs.password === ""}
                className="mt-4"
                type="submit"
            />
        </form>
    );
};

export default AuthLoginFormFeature;
