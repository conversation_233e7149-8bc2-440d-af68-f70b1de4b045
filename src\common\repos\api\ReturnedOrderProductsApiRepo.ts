import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IReturnedOrderProductSumModel } from "../../models/ReturnedOrderProductSumModel";
import { debug } from "../../utils/CommonUtils";

export class ReturnedOrderProductsApiRepo {
    static async getSumMany(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IReturnedOrderProductSumModel[]>(
                EndPointsEnums.RETURNED_ORDER_PRODUCTS_SUM_MANY,
                { params: { startTime, endTime, isTobaccoTax } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ReturnedOrderProductsApiRepo [getSumMany] Error: `, error);
            throw error;
        }
    }
}