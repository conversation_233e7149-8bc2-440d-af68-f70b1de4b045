interface IReturnedInvoiceAdditionModel {
    name: string;
    secondName?: string;
    quantity: string;
    price: string;
}

export interface IReturnedInvoicePrinterItemModel {
    quantity: string;
    name: string;
    secondName?: string;
    price: string;
    additions?: IReturnedInvoiceAdditionModel[];
}

export interface IReturnedInvoicePdfPrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    invoiceNumber: string;
    returnedInvoiceNumber: string;
    orderNumber: string;
    items: IReturnedInvoicePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    licenseNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    cash: string;
    network: string;
    deferred: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
    invoiceDate: string;
}