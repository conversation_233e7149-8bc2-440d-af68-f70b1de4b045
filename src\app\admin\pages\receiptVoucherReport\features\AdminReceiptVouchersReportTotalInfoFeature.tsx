import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IPaymentVoucherSumModel } from "../../../../../common/models/PaymentVoucherSumModel";

interface IProps {
    receiptVoucherSum?: IPaymentVoucherSumModel;
}

const AdminReceiptVoucherReportTotalInfoFeature: FC<IProps> = ({ receiptVoucherSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.ORDERS_COUNT, value: receiptVoucherSum?.count, fixedVal: 0 },
                { text: TranslateConstants.CASH, value: receiptVoucherSum?.cash },
                { text: TranslateConstants.NETWORK, value: receiptVoucherSum?.network },
            ]}
        />
    );
};

export default AdminReceiptVoucherReportTotalInfoFeature;
