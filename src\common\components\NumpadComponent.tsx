import { FC, useEffect, useState } from "react";
import { LuDelete } from "react-icons/lu";
import { TranslateConstants } from "../constants/TranslateConstants";
import { useTranslate } from "../hooks/useTranslate";
import { fixedNumber } from "../utils/numberUtils";
export const useClearNumpad = () => {
    const [result, setResult] = useState(0);
    const clearNumpad = () => setResult(result + 1);
    return { clear: result, clearNumpad };
};

export const useNumpadResult = () => {
    const [val, setVal] = useState<[ItemNumPad | "delete"]>(["delete"]);
    const [result, setResult] = useState("");

    useEffect(() => {
        if (val[0] === "delete") {
            if (!result.length) return;
            setResult(result.slice(0, -1));
        } else setResult(result + val[0]);
    }, [val]);

    return { val: result, fixedVal: fixedNumber(result || "0"), setVal };
};

export const getNumpadResult = (val: ItemNumPad | "delete", result: string) => {
    if (val === "delete") {
        if (!result.length)
            return {
                numbers: "",
                fixedNumbers: 0,
            };
        return {
            numbers: result.slice(0, -1),
            fixedNumbers: fixedNumber(result.slice(0, -1)),
        };
    }
    return {
        numbers: result + val,
        fixedNumbers: fixedNumber(result + val),
    };
};

interface NumpadButtonComponentProps {
    text: string;
    className?: string;
    onClick: (text: string) => void;
    isDisabled?: boolean;
    show?: boolean;
}

export type ItemNumPad =
    | "1"
    | "2"
    | "3"
    | "4"
    | "5"
    | "6"
    | "7"
    | "8"
    | "9"
    | "delete"
    | "0"
    | ".";

const items: ItemNumPad[] = [
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "delete",
    "0",
    ".",
];

const basicClassName: string =
    "bg-white text-slate-500 rounded-lg p-3 active:bg-base-200 active:text-slate-500 text-3xl w-full h-full";

const NumpadButtonComponent: FC<NumpadButtonComponentProps> = ({
    text,
    className,
    onClick,
    isDisabled = false,
    show = true,
}) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        if (isDisabled) return;
        onClick(text);
    };

    if (!show) return <></>;

    return (
        <div className={"col-span-1"}>
            <button
                className={
                    basicClassName +
                    " btn btn-ghost border-gray-200 " +
                    className +
                    " " +
                    (isDisabled ? "disabled:bg-slate-300" : "")
                }
                onClick={handleClick}
                disabled={isDisabled}
            >
                {text}
            </button>
        </div>
    );
};

interface NumpadComponentProps {
    type?: "number" | "text" | "password" | "fixed";
    onChange?: (value: string) => void;
    buttonClassName?: string;
    isSecured?: boolean;
    resultClassName?: string;
    showResult?: boolean;
    allowZeroAtFirst?: boolean;
    placeHolder?: string;
    textCenter?: boolean;
    button?: {
        text: string;
        onClick: (text: number | string) => void;
        isDisabled?: boolean;
    };
    value?: string;
    disableButtons?: ItemNumPad[];
    onCancelButtonClick?: () => void;
    showCancelButton?: boolean;
    title?: string;
    disableResultInput?: boolean;
    allowDot?: boolean;
    padding?: "p-0" | "p-2" | "p-4";
    onNumberClick?: (text: ItemNumPad | "delete") => void;
    onDelete?: () => void;
    clear?: number;
    setValOnClick?: React.Dispatch<React.SetStateAction<[ItemNumPad | "delete"]>>;
    placeholderSize?: "text-xl" | "text-2xl" | "text-3xl";
}

const NumpadComponent: FC<NumpadComponentProps> = ({
    type = "number",
    onChange,
    buttonClassName = "py-5 !px-0",
    resultClassName,
    showResult = true,
    isSecured = false,
    allowZeroAtFirst = false,
    placeHolder,
    textCenter = true,
    button,
    value: defaultValue = "",
    disableButtons = [],
    onCancelButtonClick,
    showCancelButton = false,
    title,
    disableResultInput = true,
    allowDot = true,
    padding = "p-4",
    onNumberClick,
    onDelete,
    clear,
    setValOnClick,
    placeholderSize = "text-3xl",
}) => {
    const { translate } = useTranslate();
    const [result, setResult] = useState(defaultValue);

    const handleClick = (text: string) => {
        setValOnClick?.([text as ItemNumPad]);
        onNumberClick?.(text as ItemNumPad);
        if (text === "." && !result) text = "0.";
        if (text === "." && result.includes(".")) return;
        if (text === "0" && result.length === 0 && !allowZeroAtFirst) return;
        setResult(result + text);
    };

    useEffect(() => {
        onChange && onChange(result);
    }, [result]);

    useEffect(() => {
        setResult(defaultValue);
    }, [defaultValue]);

    useEffect(() => {
        setResult("");
    }, [clear]);

    const handleDelete = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        if (setValOnClick) {
            setValOnClick(["delete"]);
            return;
        }
        if (onNumberClick) {
            onNumberClick("delete");
            return;
        }
        if (result.length === 0) return;
        const lastResult = result[result.length - 2];
        if (lastResult === ".") {
            setResult(result.slice(0, -2));
            return;
        }
        setResult(result.slice(0, -1));
        onDelete?.();
    };

    const handelAcceptOnlyNumberOnChangeEvent = (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        let value = e.target.value;
        if (value[0] === ".") value = "0" + value;
        const regex = /^[0-9]*\.?[0-9]*$/;
        if (!regex.test(value)) return;
        setResult(value);
    };

    const handleOnSubmit = () => {
        const number = parseFloat(result);
        if (isNaN(number)) return;
        button?.onClick(type === "number" ? number : result);
    };

    return (
        <div className={"bg-base-200 rounded" + " " + padding} data-theme="winter">
            {!!title && (
                <div className="text-center font-tajawal-bold text-2xl mb-3 pb-2 border-b border-gray-300">
                    {translate(title)}
                </div>
            )}
            {showResult && (
                <div>
                    <input
                        type={isSecured ? "password" : type}
                        className={
                            basicClassName +
                            " active:bg-white !h-14 active:text-slate-500 py-2 mb-2 focus:border-slate-100 outline-gray-300 border border-gray-200 " +
                            ` ${!result && !!placeHolder ? `!text-gray-300 ${placeholderSize}` : ""} ` +
                            " " +
                            (textCenter ? "text-center" : "") +
                            " " +
                            resultClassName
                        }
                        value={result}
                        onChange={handelAcceptOnlyNumberOnChangeEvent}
                        placeholder={placeHolder || "0"}
                        disabled={disableResultInput}
                    />
                </div>
            )}
            <div className="grid grid-cols-3 gap-2 w-full" dir="ltr">
                {items.map((number, index) => {
                    if (number === "delete") {
                        return (
                            <div className={allowDot ? "col-span-1" : "col-span-2"} key={index}>
                                <button
                                    className={
                                        basicClassName +
                                        " " +
                                        "btn btn-ghost border-gray-200 " +
                                        buttonClassName
                                    }
                                    onClick={handleDelete}
                                >
                                    <LuDelete />
                                </button>
                            </div>
                        );
                    }
                    return (
                        <NumpadButtonComponent
                            key={index}
                            text={number}
                            onClick={handleClick}
                            className={buttonClassName}
                            isDisabled={disableButtons.includes(number)}
                            show={number !== "." || allowDot}
                        />
                    );
                })}
            </div>
            {(button || showCancelButton) && (
                <div className="flex gap-2 mt-2">
                    {button && (
                        <button
                            className="bg-white text-slate-500 rounded-lg p-4 w-full font-bold btn btn-ghost border border-gray-200 flex-1"
                            onClick={handleOnSubmit}
                            disabled={button.isDisabled || isNaN(parseFloat(result))}
                        >
                            {translate(button.text)}
                        </button>
                    )}
                    {showCancelButton && (
                        <button
                            className="bg-white text-red-700 rounded-lg p-4 w-full font-bold btn btn-ghost border border-gray-200 flex-1"
                            onClick={onCancelButtonClick}
                        >
                            {translate(TranslateConstants.CANCEL)}
                        </button>
                    )}
                </div>
            )}
        </div>
    );
};

export default NumpadComponent;
