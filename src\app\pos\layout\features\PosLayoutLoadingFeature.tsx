import { FC } from "react";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import logo from "/logo.svg";

interface IProps { 
    progress: number;
}

const PosLayoutLoadingFeature: FC<IProps> = ({ 
    progress
}) => {
    const { translate } = useTranslate();

    return (
        <div className="h-s w-s fixed z-50 top-0">
            <div className="text-center fixed top-0 bg- flex justify-center items-center h-s w-s flex-col gap-2">
                <div
                    className="radial-progress flex flex-col items-center justify-center"
                    style={{ "--value": progress, "--size": "15rem", "--thickness": "12px" } as React.CSSProperties}
                    role="progressbar"
                >
                    <img src={logo} alt="logo" className="animate-h_spin w-20 mb-2" />
                    <h1 className={`text-xl text-black font-tajawal-bold`}>
                        {translate(TranslateConstants.APP_NAME)}
                    </h1>
                    <h1 className={`text-xl text-black`}>
                        {progress + "%"}
                    </h1>
                </div>
            </div>
        </div>
    );
};

export default PosLayoutLoadingFeature;
