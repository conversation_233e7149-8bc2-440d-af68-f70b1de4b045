import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import { useTranslate } from "../hooks/useTranslate";

interface IProps {
    className?: string;
}

const EmptyComponent: FC<IProps> = ({ className }) => {
    const { translate } = useTranslate();

    return (
        <div
            className={
                "w-full h-full flex justify-center items-center font-bold" +
                " " +
                className
            }
        >
            {translate(TranslateConstants.EMPTY)}
        </div>
    );
};

export default EmptyComponent;
