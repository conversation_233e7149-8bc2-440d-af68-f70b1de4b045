import { FC } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";

interface IProps {
    onChange?: (checked: boolean) => void;
    label?: string;
    value?: boolean;
    defaultChecked?: boolean;
    disabled?: boolean;
    className?: string;
    activatedLabel?: string;
    deactivatedLabel?: string;
}

const ToggleButtonComponent: FC<IProps> = ({
    onChange,
    label,
    value,
    defaultChecked = false,
    disabled = false,
    className = "",
    activatedLabel = "",
    deactivatedLabel = "",
}) => {
    return (
        <div className={`flex items-center gap-2 ${className}`}>
            <input
                type="checkbox"
                {...(value !== undefined ? { checked: value } : { defaultChecked })}
                className="toggle w-10 h-6 [--handleoffset:1rem] border-slate-500 bg-slate-400 checked:bg-[#226bb2] checked:border-[#3b71a7]"
                onChange={(e) => onChange?.(e.target.checked)}
                disabled={disabled}
            />
            {label && <label>{TranslateHelper.t(label)}</label>}
            {activatedLabel && deactivatedLabel && (
                <div>
                    {value ? TranslateHelper.t(activatedLabel) : TranslateHelper.t(deactivatedLabel)}
                </div>
            )}
        </div>
    );
};

export default ToggleButtonComponent;
