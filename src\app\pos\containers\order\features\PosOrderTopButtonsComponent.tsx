import { LuTrash2 } from "react-icons/lu";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { FC, useMemo } from "react";
import { IPosOrder } from "../../../interface";
import { PosOrderService } from "../PosOrderService";
import usePosActions from "../../../redux/usePosActions";
import { OrderStatusEnum, OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import useActions from "../../../../../common/redux/data/useActions";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { IoMdAdd } from "react-icons/io";
import { IoIosPause } from "react-icons/io";

interface IProps {
    order: IPosOrder;
    oldOrder: IPosOrder;
}

const PosOrderTopButtonsComponent: FC<IProps> = ({ order, oldOrder }) => {
    const actions = useActions();
    const posActions = usePosActions();

    const disableNewOrderButton = useMemo(() => {
        return (
            !order?.orderProducts?.length &&
            !order.table &&
            !order.customer &&
            !order.deliveryApp
        );
    }, [order]);

    const disableDeleteButton = useMemo(() => {
        return order.status !== OrderStatusEnum.IN_PROGRESS;
    }, [order]);

    const disablePauseButton = useMemo(() => {
        return (
            !order.orderProducts.length ||
            order.type !== OrderTypeEnum.TAKE_AWAY ||
            order.status !== OrderStatusEnum.PENDING ||
            order.isPaused
        );
    }, [order]);

    const handleNewOrder = () => PosOrderService.resetOrder(posActions);

    const handleTrash = () => {
        actions.openModal({
            title: TranslateConstants.DELETE_ORDER,
            component: (
                <div className="p-2 text-center">
                    {TranslateHelper.t(TranslateConstants.CONFIRM_DELETE_ORDER)}
                </div>
            ),
            size: "sm",
            submitButton: {
                onClick: handleDeleteOrder,
                text: TranslateConstants.DELETE,
            },
        });
    };

    const handleDeleteOrder = () => PosOrderService.handleDeleteOrder(
        oldOrder,
        actions,
        posActions
    );

    const handlePauseOrder = () => PosOrderService.handlePauseOrder(
        order,
        actions,
        posActions
    );

    return (
        <div className="flex">
            <IconButtonComponent
                icon={<IoMdAdd />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                isDisabled={disableNewOrderButton}
                onClick={handleNewOrder}
                className="border-l"
            />
            <IconButtonComponent
                icon={<IoIosPause />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                isDisabled={disablePauseButton}
                onClick={handlePauseOrder}
                className="border-l"
            />
            <IconButtonComponent
                icon={<LuTrash2 />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                isDisabled={disableDeleteButton}
                bgColor="red"
                onClick={handleTrash}
            />
        </div>
    );
};

export default PosOrderTopButtonsComponent;
