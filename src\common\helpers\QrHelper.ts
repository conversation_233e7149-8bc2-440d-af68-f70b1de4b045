import { OrganizationHelper } from "./OrganizationHelper";

export class QrHelper {
    static vatQrData(totalPrice: number, vat: number) {
        const organization = OrganizationHelper.getOrganization();

        return this.vatQrDataBinaryConvert([
            organization?.name ?? "",
            organization?.taxNumber ?? "",
            this.vatFormattedDate(new Date()),
            totalPrice.toFixed(2),
            vat.toFixed(2),
        ])
    }

    private static vatFormattedDate(date: Date): string {
        return `${date.getFullYear()}/${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")} T ${date
                .getHours()
                .toString()
                .padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}Z`;
    }

    private static vatQrDataBinaryConvert(data: string[]): string {
        const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
            let binary = "";
            let bytes = new Uint8Array(buffer);
            let len = bytes.byteLength;
            for (let i = 0; i < len; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return window.btoa(binary);
        };

        let bytesArray: number[] = [];

        for (let i = 0; i < data.length; i++) {
            bytesArray.push(i + 1);
            let elementInBytes = new TextEncoder().encode(data[i]);
            bytesArray.push(elementInBytes.length);
            bytesArray.push(...Array.from(elementInBytes));
        }

        let qrCodeAsBytes = new Uint8Array(bytesArray);
        return arrayBufferToBase64(qrCodeAsBytes);
    }
}