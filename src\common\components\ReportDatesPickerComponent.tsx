import { FC, useState } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import DatePickerComponent from "./DatePickerComponent";
import { DateUtils } from "../utils/DateUtils";
// import useScreenSize from "../hooks/useScreenSize";
import { ToastHelper } from "../helpers/ToastHelper";
// import { useTranslate } from "../hooks/useTranslate";
import IconButtonComponent from "./IconButtonComponent";
import { FiSearch } from "react-icons/fi";

interface IProps {
    onClick?: (startDate: Date, endDate: Date) => void;
    disableButton?: boolean;
    disableSearchButtonByDefault?: boolean;
    dateType?: "date" | "datetime";
}

const ReportDatesPickerComponent: FC<IProps> = ({
    onClick,
    disableButton,
    disableSearchButtonByDefault = true,
    dateType = "datetime",
}) => {
    // const { translate } = useTranslate();
    // const { isSm } = useScreenSize();
    const [state, setState] = useState({
        startDate: DateUtils.getStartOfDay(),
        endDate: DateUtils.getEndOfDay(),
        disabled: disableSearchButtonByDefault,
    });

    const handleOnCLik = () => {
        if (state.startDate > state.endDate) {
            ToastHelper.error(TranslateConstants.START_DATE_GREATER_THAN_END_DATE);
            return;
        }
        onClick?.(state.startDate, state.endDate);
        setState({ ...state, disabled: true });
    };

    return (
        <div className="flex gap-2 w-full sm:w-min">
            <DatePickerComponent
                type={dateType}
                onChange={(startDate) => setState({
                    ...state,
                    disabled: false,
                    startDate: dateType === "date" ? (startDate as any)?.start : startDate as Date,
                })}
                // label={translate(
                //     (isSm || dateType === "datetime") ? TranslateConstants.FROM : TranslateConstants.FROM_DATE
                // )}
                label={"1"}
                defaultValue={{
                    startDate: state.startDate,
                    endDate: state.startDate,
                }}
            />
            <DatePickerComponent
                type={dateType}
                onChange={(endDate) => setState({
                    ...state,
                    disabled: false,
                    endDate: dateType === "date" ? (endDate as any)?.end : endDate as Date,
                })}
                // label={translate(
                //     (isSm || dateType === "datetime") ? TranslateConstants.TO : TranslateConstants.TO_DATE
                // )}
                label={"2"}
                defaultValue={{
                    startDate: state.endDate,
                    endDate: state.endDate,
                }}
                minDate={state.startDate}
            />
            <IconButtonComponent
                icon={<FiSearch />}
                onClick={handleOnCLik}
                className="!w-16 px-4 py-[0.55rem]"
                isDisabled={state.disabled && disableButton}
            />
        </div>
    );
};

export default ReportDatesPickerComponent;
