import { FC, useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { useTranslate } from "../hooks/useTranslate";
import { useAppSelector } from "../redux/store";
import { loadingSelector } from "../redux/data/selector";

interface IProps {
    isMenuExpanded?: boolean;
    setExpandedMenuIndex?: (index: number) => void;
    onClick?: () => void;
    onNavClick?: () => void;
    name: string;
    submenu: { path: string; name: string }[];
    index: number;
}

const SideBarSubMenuComponent: FC<IProps> = ({
    submenu,
    name,
    isMenuExpanded = false,
    setExpandedMenuIndex,
    onClick,
    onNavClick,
    index,
}) => {
    const location = useLocation();
    const { translate } = useTranslate();
    const isLoading = useAppSelector(loadingSelector);

    /** Open Submenu list if path found in routes, this is for directly loading submenu routes  first time */
    useEffect(() => {
        const findIndex = submenu.findIndex((m) => m.path === location.pathname);
        if (findIndex !== -1) setExpandedMenuIndex?.(index);
    }, [isLoading]);

    return (
        <div
            className={
                "flex-col text-inherit !p-0 !gap-0" +
                " " +
                (isMenuExpanded ? "bg-[#418dd9] dark:bg-inherit" : "bg-inherit")
            }
        >
            {/** Route header */}
            <div
                className="w-full flex justify-between py-2 px-4"
                onClick={() => {
                    onClick && onClick();
                }}
            >
                <span className="text-sm">{translate(name)}</span>
                {isMenuExpanded ? (
                    <span className="font-bold">-</span>
                ) : (
                    <span className="font-bold">+</span>
                )}
            </div>

            {/** Submenu list */}
            <div className={` w-full ` + (isMenuExpanded ? "" : "hidden")}>
                <ul className={`menu-compact bg-inherit text-inherit`}>
                    {submenu?.map((m, k) => {
                        return (
                            <li key={k} className="bg-inherit">
                                <NavLink
                                    to={m.path}
                                    className={({ isActive }) =>
                                        "active:bg-inherit active:text-slate-900 dark:active:text-gray-200 font-semibold" +
                                        " px-8 mt-[1px] " +
                                        `${isActive
                                            ? "!bg-white text-black dark:!bg-[#226bb2] dark:text-white "
                                            : "hover:bg-white hover:text-black dark:hover:bg-[#226bb2] dark:hover:text-white"
                                        }`
                                    }
                                    onClick={() => {
                                        onNavClick && onNavClick();
                                    }}
                                >
                                    {translate(m.name)}
                                </NavLink>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </div>
    );
};

export default SideBarSubMenuComponent;
