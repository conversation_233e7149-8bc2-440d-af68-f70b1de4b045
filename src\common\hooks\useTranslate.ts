import cookies from 'js-cookie'
import { useTranslation } from 'react-i18next'
import i18next from 'i18next'
import saImage from "/images/sa.png";
import ukImage from "/images/uk.png";
import { LocalStorageHelper } from '../helpers/LocalStorageHelper';

export interface ILanguage {
    code: "en" | "ar";
    name: string;
    country_code: string;
    dir: string;
    image: string;
}

export const langList: ILanguage[] = [
    {
        code: "ar",
        name: "العربية",
        country_code: "sa",
        dir: "rtl",
        image: saImage,
    },
    {
        code: "en",
        name: "English",
        country_code: "gb",
        dir: "ltr",
        image: ukImage,
    },
];

export interface translateOptions {
    specificLanguage?: 'en' | 'ar',
    isArabic?: boolean
}

export const IsArabic = () => {
    const currentLanguageCode = cookies.get('i18next') || 'en'
    const selectedLanguage = langList.find(
        (l: ILanguage) => l.code === currentLanguageCode
    )
    return selectedLanguage?.code === 'ar'
}

export const useTranslate = () => {
    {
        /* @ts-ignore */
    }
    const { t } = useTranslation()
    const currentLanguageCode = LocalStorageHelper.get('language') || cookies.get('i18next') || 'en'
    const selectedLanguage = langList.find(
        (l: ILanguage) => l.code === currentLanguageCode
    )
    const switchLang = (code: string) => {
        i18next.changeLanguage(code)
        LocalStorageHelper.set('language', code)
    }
    const toggleEnAr = () =>
        i18next.changeLanguage(
            selectedLanguage?.code === 'ar'
                ? langList[0].code
                : langList[1].code
        )
    const setCurrentLanguage = () => {
        i18next.changeLanguage(selectedLanguage?.code ?? "en")
    }
    const translate = (
        text: string | string[],
        options: translateOptions = { specificLanguage: selectedLanguage?.code ?? "en", isArabic: false }
    ) => {
        const textToTranslate = typeof text === 'string' ? text.split(' ') : text
        const translatedText = textToTranslate.map(word => {
            return t(word, { lng: options.isArabic ? "ar" : options?.specificLanguage })
        })
        return translatedText.join(' ')
    }

    return {
        code: selectedLanguage?.code,
        country_code: selectedLanguage?.country_code,
        name: selectedLanguage?.name,
        dir: selectedLanguage?.dir ?? 'ltr',
        isArabic: selectedLanguage?.code === 'ar',
        translate,
        switchLang,
        toggleEnAr,
        setCurrentLanguage,
        isRtl: selectedLanguage?.dir === 'rtl',
    }
}
