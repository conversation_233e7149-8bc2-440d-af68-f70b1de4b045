export const LocalDbCountAction = (
    db: IDBDatabase | undefined,
    collection: string
): Promise<number> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readonly");
        const store = transaction?.objectStore(collection);
        const request = store?.count();

        if (request) {
            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};
