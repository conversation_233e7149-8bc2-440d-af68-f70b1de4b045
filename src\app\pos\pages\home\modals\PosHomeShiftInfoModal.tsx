import ButtonComponent from "../../../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ListComponent from "../../../../../common/components/ListComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import useActions from "../../../../../common/redux/data/useActions";
import { PosHomeUtils } from "../PosHomeUtils";
import useFetch from "../../../../../common/asyncController/useFetch";
import { useMemo } from "react";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { PosHomeShiftInfoConstant } from "../PosHomeConstants";
import PosReceiptVoucherModal from "./PosReceiptVoucherModal";
import PosPaymentVoucherModal from "./PosPaymentVoucherModal";

const PosHomeShiftInfoModal = () => {
    const actions = useActions();

    const { data: shift } = useFetch(
        "pos-shift-info",
        () => PosHomeUtils.getCurrentSiftDataWithDeliveryApps(0),
        { autoFetchOnMount: true }
    );

    const formattedShift = useMemo(() => {
        return PosHomeShiftInfoConstant(shift)
            ?.filter((el) => el.value || el.keepIfZero);
    }, [shift]);

    const handleOnAdd = () => {
        actions.openModal({
            component: <PosReceiptVoucherModal />,
            title: TranslateConstants.ADD_TO_SHIFT,
            size: "sm",
            showButtons: false,
        });
    };

    const handleOnPull = () => {
        actions.openModal({
            component: <PosPaymentVoucherModal shift={shift} />,
            title: TranslateConstants.PULL_FROM_SHIFT,
            size: "sm",
            showButtons: false,
        });
    };

    return (
        <div className="px-2">
            <ListComponent calcHeight={30} allowScrollBar={false} padding="0">
                {formattedShift?.map((el, index) => {
                    const isLast = index === formattedShift.length - 1;
                    return (
                        <div
                            key={el.text}
                            // className="flex justify-between py-1 font-tajawal-medium border-b border-gray-300"
                            className={
                                "flex justify-between py-1 font-tajawal-medium" +
                                " " +
                                (!isLast ? "border-b border-gray-300" : "")
                            }
                        >
                            <span>{TranslateHelper.t(el.text)}</span>
                            <span>{el.value}</span>
                        </div>
                    );
                })}
            </ListComponent>
            <DividerComponent className="my-2" />
            <div className="flex justify-between gap-2">
                <ButtonComponent
                    text={TranslateConstants.ADD}
                    onClick={handleOnAdd}
                />
                <ButtonComponent
                    text={TranslateConstants.PULL}
                    onClick={handleOnPull}
                />
            </div>
        </div>
    );
};

export default PosHomeShiftInfoModal;
