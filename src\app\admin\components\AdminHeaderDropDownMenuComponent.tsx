import logo from "/logo.svg";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import SwitchThemeComponent from "../../../common/components/SwitchTheme";
import { useNavigate } from "react-router-dom";
import { useTranslate } from "../../../common/hooks/useTranslate";
import { AuthService } from "../../auth/AuthService";
import { SlScreenDesktop } from "react-icons/sl";
import { RoleService } from "../../role/RoleService";
import { MdOutlineGTranslate } from "react-icons/md";
import { ShowLanguageModal } from "../../../common/components/LanguageComponent";

export default function AdminHeaderDropDownMenuComponent() {
  const navigate = useNavigate();
  const { translate, isRtl } = useTranslate();

  const handleOnPosClick = () =>
    RoleService.redirectToRolePage(navigate);

  const handleLogout = async () => {
    await AuthService.logout(navigate);
  };

  return (
    <div className={`dropdown ${"dropdown-right"} mx-auto md:!mx-2`}>
      <label tabIndex={0} className="btn btn-ghost btn-circle avatar">
        <div className="w-10 rounded-full">
          <img src={logo} alt="profile" />
        </div>
      </label>
      <ul
        tabIndex={0}
        className={`menu menu-compact dropdown-content p-2 shadow bg-base-100 rounded-box w-52 mt-16 ${isRtl ? '!left-0' : '!-left-44'}`}
      >
        <li>
          <a className="hover:bg-base-200 flex justify-between items-center sm:hidden"
            onClick={handleOnPosClick}
          >
            <span>{translate(TranslateConstants.POS)}</span>
            <span>
              <SlScreenDesktop size={20} />
            </span>
          </a>
        </li>
        <li>
          <a className="hover:bg-base-200 flex justify-between items-center"
            onClick={ShowLanguageModal}
          >
            <span>{translate(TranslateConstants.LANGUAGE)}</span>
            <span>
              <MdOutlineGTranslate size={20} />
            </span>
          </a>
        </li>
        <li>
          <a className="hover:bg-base-100 flex justify-between items-center cursor-default">
            <span>{translate(TranslateConstants.DARK_MODE)}</span>
            <span className="h-6">
              <SwitchThemeComponent />
            </span>
          </a>
        </li>
        <div className="divider mt-0 mb-0"></div>
        <li>
          <a
            className="hover:bg-red-700 hover:text-white active:bg-red-700"
            onClick={handleLogout}
          >
            {translate(TranslateConstants.LOGOUT)}
          </a>
        </li>
      </ul>
    </div>
  );
}
