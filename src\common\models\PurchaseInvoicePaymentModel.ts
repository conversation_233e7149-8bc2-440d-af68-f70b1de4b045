import { BaseModel } from ".";
import { IPurchaseInvoiceModel } from "./PurchaseInvoiceModel";
import { ISupplierModel } from "./SuppliersModel";

export interface IPurchaseInvoicePaymentModel extends BaseModel {
    date: number;
    cash: number;
    network: number;
    deferred: number;
    note?: string;
    purchaseInvoice: IPurchaseInvoiceModel;
    purchaseInvoiceId?: number;
    supplier: ISupplierModel;
}