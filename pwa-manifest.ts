import { VitePWAOptions } from "vite-plugin-pwa";

const manifestForPlugIn: Partial<VitePWAOptions> = {
    registerType: "prompt",
    devOptions: {
        enabled: false,
    },
    workbox: {
        clientsClaim: true,
        skipWaiting: true,
        runtimeCaching: [
            {
                urlPattern: ({ request }) => request.destination === "image",
                handler: "CacheFirst",
                options: {
                    cacheName: "image-cache",
                    expiration: {
                        maxEntries: 100, // max number of images to cache
                        maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days to cache images then expire
                    },
                    cacheableResponse: {
                        statuses: [0, 200],
                    },
                },
            },
        ],
        globPatterns: ['**/*.{js,css,html,png,jpg,json,woff2,ttf}'], // Make sure html files are included
    },
    includeAssets: [
        "logo.svg",
        "images/background1.jpg",
        "images/background2.jpg",
        "images/background3.jpg",
        "locales/ar/translation.json",
        "locales/en/translation.json",
    ],
    manifest: {
        name: "الرؤيا الرقمية",
        short_name: "الرؤيا الرقمية",
        description: "برنامج الرؤيا الرقمية للمطاعم الشامل",
        icons: [
            {
                src: "/pwa-192x192.png",
                sizes: "192x192",
                type: "image/png",
                purpose: "any",
            },
            {
                src: "/pwa-512x512.png",
                sizes: "512x512",
                type: "image/png",
                purpose: "any",
            },
            {
                src: "/pwa-maskable-192x192.png",
                sizes: "192x192",
                type: "image/png",
                purpose: "maskable",
            },
            {
                src: "/pwa-maskable-512x512.png",
                sizes: "512x512",
                type: "image/png",
                purpose: "maskable",
            },
            {
                src: "/apple-touch-icon.png",
                sizes: "180x180",
                type: "image/png",
            },
        ],
        theme_color: "#171717",
        background_color: "#f0e7db",
        display: "fullscreen",
        scope: "/",
        start_url: "/",
        orientation: "portrait",
    },
};

export default manifestForPlugIn;
