import React, { FC, useMemo } from "react";
import { useTranslate } from "../hooks/useTranslate";
import { useNavigate } from "react-router-dom";

interface IProps {
    icon: React.ReactNode;
    text?: string;
    onClick?: () => void;
    route?: string;
    bgColor?: "primary" | "white" | "slate" | "red" | "transparent";
    textColor?: "primary" | "white" | "black" | "red";
    borderColor?: "white" | "black" | "primary" | "gray-400" | "slate-600";
    rounded?: boolean;
    width?: "w-full" | "w-1/2" | "w-1/3" | "w-1/4" | "w-1/5" | "w-1/6";
    height?: "h-full" | "h-12" | "h-16" | "h-20" | "";
    padding?: "p-1" | "p-2";
    className?: string;
    textSize?: "text-sm" | "text-lg" | "text-xl" | "text-2xl" | "text-3xl";
    iconSize?:
    | "text-lg"
    | "text-2xl"
    | "text-3xl"
    | "text-4xl"
    | "text-5xl"
    | "text-6xl";
    isDisabled?: boolean;
}

const IconButtonComponent: FC<IProps> = ({
    icon,
    text,
    onClick,
    route,
    bgColor = "primary",
    textColor = "white",
    rounded = true,
    width = "w-full",
    height = "",
    padding = "p-1",
    className = "",
    textSize = "text-sm",
    iconSize = "text-2xl",
    isDisabled,
    borderColor,
}) => {
    const { translate } = useTranslate();
    const navigate = useNavigate();

    const getBgColor = useMemo(() => {
        switch (bgColor) {
            case "primary":
                return "bg-[#226bb2] hover:bg-[#1e5a9d] text-white";
            case "white":
                return "bg-white hover:bg-gray-200";
            case "slate":
                return "bg-slate-600 hover:bg-slate-500 text-white";
            case "red":
                return "bg-red-800 hover:bg-red-700 text-white";
            case "transparent":
                return "bg-transparent hover:bg-gray-200 text-black";
        }
    }, [bgColor]);

    const getIconColor = useMemo(() => {
        switch (textColor) {
            case "primary":
                return "!text-[#226bb2]";
            case "white":
                return "!text-white";
            case "black":
                return "!text-black";
            case "red":
                return "!text-red-600";
        }
    }, [textColor]);

    const getBorderColor = useMemo(() => {
        switch (borderColor) {
            case "white":
                return "border border-white";
            case "black":
                return "border border-black";
            case "primary":
                return "border border-[#226bb2]";
            case "gray-400":
                return "border border-gray-400";
            case "slate-600":
                return "border border-slate-600";
        }
    }, [borderColor]);

    const handleOnClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (isDisabled) return;
        if (route) navigate(route);
        onClick?.();
    };

    return (
        <div
            className={
                "flex flex-col items-center justify-center h-full" +
                " " +
                (rounded ? "rounded" : "") +
                " " +
                getBgColor +
                " " +
                getIconColor +
                " " +
                getBorderColor +
                " " +
                (isDisabled
                    ? "!bg-slate-400 hover:bg-slate-400 cursor-not-allowed text-white"
                    : "active:scale-95 cursor-pointer group") +
                " " +
                width +
                " " +
                height +
                " " +
                padding +
                " " +
                className
            }
            onClick={handleOnClick}
        >
            <div
                className={
                    "group-hover:scale-105 transition-transform" + " " + iconSize
                }
            >
                {icon}
            </div>
            {text && <div className={textSize}>{translate(text)}</div>}
        </div>
    );
};

export default IconButtonComponent;
