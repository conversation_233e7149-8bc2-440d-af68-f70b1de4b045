import { useEffect, useState } from 'react'
import { themeChange } from 'theme-change'
import { IoMoon } from "react-icons/io5";
import { IoIosSunny } from "react-icons/io";

export default function SwitchThemeComponent() {
    const [currentTheme, setCurrentTheme] = useState(
        localStorage.getItem('theme')
    )

    useEffect(() => {
        themeChange(false)
        if (currentTheme === null) {
            setCurrentTheme('winter')
        }
    }, [])

    return (
        <label className='swap pointer-events-auto'>
            <input type='checkbox' className='border-none'/>
            <IoIosSunny
                data-set-theme='winter'
                data-act-class='ACTIVECLASS'
                className={
                    'fill-current w-6 h-6 ' +
                    (currentTheme === 'dark' ? 'swap-on' : 'swap-off')
                }
            />
            <IoMoon
                data-set-theme='dark'
                data-act-class='ACTIVECLASS'
                className={
                    'fill-current w-6 h-6 ' +
                    (currentTheme === 'winter' ? 'swap-on' : 'swap-off')
                }
            />
        </label>
    )
}
