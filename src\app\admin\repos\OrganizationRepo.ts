import { IAdminSettingsFormattedData, IAdminSettingsInputs } from "../pages/settings/AdminSettingsInterface";
import { uploadImage } from "../../../common/config/firebase";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { OrganizationHelper } from "../../../common/helpers/OrganizationHelper";
import { IOrganizationSensitiveModel, OrganizationModel } from "../../../common/models/OrganizationModel";
import { debug } from "../../../common/utils/CommonUtils";
import { IOrganizationUpdatesCountModel } from "../../../common/models/OrganizationUpdatesCountModel";
import { OrganizationRoleEnum } from "../../../common/enums/DataEnums";
import { ImagesUtils } from "../../../common/utils/ImagesUtils";

export class OrganizationRepo {
    static async getOwn() {
        try {
            const res = await AxiosHelper.get<OrganizationModel>(
                EndPointsEnums.ORGANIZATIONS_OWN
            );

            if (!res.success || !res.data) {
                throw new Error(res.message);
            }

            OrganizationHelper.setOrganization(res.data);
            const logo = await ImagesUtils.getBase64ImageFromUrl(res.data.logo);
            OrganizationHelper.setBase64Logo(logo);
        } catch (error) {
            debug(`OrganizationRepo [getOwn] Error: `, error);
            throw error;
        }
    }

    static async getSensitives() {
        try {
            const res = await AxiosHelper.get<IOrganizationSensitiveModel>(
                EndPointsEnums.ORGANIZATIONS_OWN_SENSITIVES
            );

            if (!res.success) {
                throw new Error(res.message);
            }

            return res.data;
        } catch (error) {
            debug(`OrganizationRepo [getSensitives] Error: `, error);
            throw error;
        }
    }

    static async checkRole(password: string) {
        try {
            const res = await AxiosHelper.post<OrganizationRoleEnum>(
                EndPointsEnums.ORGANIZATIONS_CHECK_ROLE,
                { password }
            );

            if (!res.success) throw new Error(res.message);

            return res.data;
        } catch (error) {
            debug(`OrganizationRepo [checkRole] Error: `, error);
            throw error;
        }
    }

    static async update(inputs: IAdminSettingsInputs) {
        try {
            const data: IAdminSettingsFormattedData = { ...inputs, logo: undefined };

            if (inputs.logo) data.logo = await uploadImage(inputs.logo);

            const res = await AxiosHelper.patch<OrganizationModel>(
                EndPointsEnums.ORGANIZATIONS,
                undefined,
                data
            );

            if (!res.success || !res.data) {
                throw new Error(res.message);
            }

            OrganizationHelper.setOrganization({ ...res.data, role: OrganizationRoleEnum.ADMIN });

            if (inputs.logo) {
                const logo = await ImagesUtils.getBase64ImageFromUrl(res.data.logo);
                OrganizationHelper.setBase64Logo(logo);
            }
        } catch (error) {
            debug(`OrganizationRepo [update] Error: `, error);
            throw error;
        }
    }

    static async getUpdatesCount() {
        try {
            const res = await AxiosHelper.get<IOrganizationUpdatesCountModel>(
                EndPointsEnums.ORGANIZATIONS_UPDATES_COUNT
            );

            if (!res.success || !res.data) {
                throw new Error(res.message);
            }

            return res.data;
        } catch (error) {
            debug(`OrganizationRepo [getUpdatesCount] Error: `, error);
            throw error;
        }
    }
}