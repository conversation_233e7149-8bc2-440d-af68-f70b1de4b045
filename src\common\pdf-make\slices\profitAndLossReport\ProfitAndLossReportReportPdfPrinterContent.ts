import { Content } from "pdfmake/interfaces";
import { IProfitAndLossReportPdfPrinterModel } from "./ProfitAndLossReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { TranslateHelper } from "../../../helpers/TranslateHelper";
import { TranslateConstants } from "../../../constants/TranslateConstants";

export const ProfitAndLossReportPdfPrinterContent = (model: IProfitAndLossReportPdfPrinterModel): Content[] => {
    const salesNet = model.data.ordersTotal - model.data.returnedOrdersTotal;
    const purchasesNet = model.data.purchaseInvoicesTotal - model.data.returnedPurchaseInvoicesTotal;
    const totalNet = salesNet - purchasesNet - model.data.paymentVouchersTotal;

    return [
        {
            text: "تقرير الأرباح والخسائر", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: {
                fontSize: 14,
                bold: true,
                alignment: "center",
            },
            table: {
                headerRows: 1,
                widths: "*",
                body: [
                    [
                        { text: model.data.ordersTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_SALES) },
                    ],
                    [
                        { text: model.data.returnedOrdersTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_RETURNED_SALES) },
                    ],
                    [
                        { text: salesNet, fillColor: "#1F618D", color: "white" },
                        { text: TranslateHelper.t(TranslateConstants.SALES_NET), fillColor: "#1F618D", color: "white" },
                    ],
                    [
                        { text: model.data.purchaseInvoicesTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_PURCHASES) },
                    ],
                    [
                        { text: model.data.returnedPurchaseInvoicesTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_RETURNED_PURCHASE_INVOICES) },
                    ],
                    [
                        { text: purchasesNet, fillColor: "#1F618D", color: "white" },
                        { text: TranslateHelper.t(TranslateConstants.PURCHASES_NET), fillColor: "#1F618D", color: "white" },
                    ],
                    ...(model.data.paymentVouchersSum ?? [])
                        .filter((el) => el.total > 0)
                        .map((el) => [
                            { text: el.total },
                            { text: TranslateHelper.t(el.destination) },
                        ]),
                    [
                        { text: model.data.paymentVouchersTotal, fillColor: "#1F618D", color: "white" },
                        { text: TranslateHelper.t(TranslateConstants.EXPENSES_NET), fillColor: "#1F618D", color: "white" },
                    ],
                    [
                        { text: totalNet },
                        { text: TranslateHelper.t( totalNet >= 0 ? TranslateConstants.TOTAL_PROFIT : TranslateConstants.TOTAL_LOSS) },
                    ],
                ],
            },
        },
    ];
}
