export type IOperator =
    | "==="
    | "=="
    | "!=="
    | "<"
    | ">"
    | "<="
    | ">="
    | "in"
    | "not in"
    | "like"
    | "not like"
    | ">date"
    | "<date";

export type ILocalDBWhere<T> = [keyof T, IOperator, any][][];

export interface ILocalDBGetOptionsProperties<T> {
    where?: ILocalDBWhere<T>;
    orderBy?: [string, "desc" | "asc"];
    limit?: number;
    skip?: number;
    skipUntil?: number;
    join?: {
        collection: string,
        fk: string,
        alias?: string,
        type?: "one" | "many"
    }[];
    batchSize?: number;
    index?: string;
    whereKey?: string | number;
}

export interface ILocalDBDataWithId {
    id: string | number;
    [key: string]: any;
}

export interface ILocalDBDataWithoutId {
    [key: string]: any;
}

export interface ILocalDBStore<T extends string = string> {
    collection: string;
    indexes: { name: T; key: string; options: IDBIndexParameters }[];
}
