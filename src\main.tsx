import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { Provider } from "react-redux";
import store from "./common/redux/store";
import { Suspense } from 'react'
import SplashScreenComponent from './common/components/SplashScreenComponent.tsx'
import './common/config/i18n.ts'
import './common/config/localDB.ts'
import { ErrorBoundaryComponent } from './common/components/ErrorBoundaryComponent.tsx'

createRoot(document.getElementById('root')!).render(
  <ErrorBoundaryComponent>
    <Suspense fallback={<SplashScreenComponent />}>
      <Provider store={store}>
        <App />
      </Provider>
    </Suspense>
  </ErrorBoundaryComponent>
)