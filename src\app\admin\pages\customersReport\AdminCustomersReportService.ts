import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { CustomerReportPdfPrinterContent } from "../../../../common/pdf-make/slices/customerReport/CustomerReportPdfPrinterContent";
import { ICustomerReportPdfPrinterModel } from "../../../../common/pdf-make/slices/customerReport/CustomerReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { ICustomersReportDataInterface, ICustomersReportTotalInfoInterface } from "./AdminCustomersReportInterface";

export class AdminCustomersReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: ICustomersReportDataInterface[],
        totals: ICustomersReportTotalInfoInterface,
        customer?: ICustomerModel,
    ) {

        const model: ICustomerReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: data?.map((el) => ({
                date: DateUtils.format(el.startTime),
                number: el.customNumber,
                operationType: TranslateHelper.t(el.operationType),
                orderType: TranslateHelper.t(el.type),
                subTotal: el.subTotal,
                discount: el.discount,
                vat: el.vat,
                total: el.total,
                cash: el.cash,
                network: el.network,
                credit: el.deferred,
            })),
            totals,
            customer: {
                name: customer?.name || "",
                mobile: customer?.mobile || "",
                credit: customer?.credit?.toFixed(2) || "0",
                taxNumber: customer?.taxNumber,
            },
        };
        PdfMakeUtils.download(CustomerReportPdfPrinterContent(model), "Customers Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleTotalInfo(
        orderSum: IOrderSumModel | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined,
        receiptVoucherSum: IPaymentVoucherSumModel | undefined,
    ): ICustomersReportTotalInfoInterface {
        const data = {
            count: orderSum?.count ?? 0,
            returnedCount: returnedOrderSum?.count ?? 0,
            vat: (orderSum?.vat ?? 0) - (returnedOrderSum?.vat ?? 0),
            totalReturned: (returnedOrderSum?.total ?? 0),
            cash: (orderSum?.cash ?? 0) + (receiptVoucherSum?.cash ?? 0) - (returnedOrderSum?.cash ?? 0),
            network: (orderSum?.network ?? 0) + (receiptVoucherSum?.network ?? 0) - (returnedOrderSum?.network ?? 0),
            pay: (receiptVoucherSum?.total ?? 0),
            credit: (orderSum?.count || returnedOrderSum?.count) ?
                (orderSum?.deferred ?? 0) - ((returnedOrderSum?.deferred ?? 0) + (receiptVoucherSum?.total ?? 0)) : 0,
        };

        return {
            ...data,
            total: data.totalReturned + data.cash + data.network + data.pay + data.credit,
        }
    }

    static handleData(
        orders: IOrderModel[] = [],
        returnedOrders: IReturnedOrderModel[] = [],
        receiptVouchers: IReceiptVoucherModel[] = [],
    ): ICustomersReportDataInterface[] {
        return [
            ...orders.map((el) => ({
                ...el,
                customNumber: el.invoiceNumber,
                isReturned: false,
                operationType: TranslateConstants.SELL,
            })),
            ...returnedOrders.map((el) => ({
                ...el,
                customNumber: el.returnedInvoiceNumber,
                isReturned: true,
                operationType: TranslateConstants.RETURNED,
            })) as any[],
            ...receiptVouchers.map((el) => ({
                ...el,
                customNumber: el.number,
                isReturned: false,
                type: "-",
                subTotal: 0,
                discount: 0,
                tobaccoTax: 0,
                vat: 0,
                total: el.cash + el.network,
                cash: el.cash,
                network: el.network,
                deferred: 0,
                startTime: el.date,
                operationType: TranslateConstants.RECEIPT_VOUCHER,
            })) as any[],
        ].sort((a, b) => b.startTime - a.startTime);
    }
}