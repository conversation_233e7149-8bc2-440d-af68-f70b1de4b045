import { FC, useState } from "react";
import { IoImageOutline } from "react-icons/io5";

interface IDeliveryItemComponentProps {
    src?: string;
    alt?: string;
    className?: string;
    onErrorClassName?: string;
}

const ImageComponent: FC<IDeliveryItemComponentProps> = ({
    src,
    alt,
    className,
    onErrorClassName,
}) => {
    const [isImageLoading, setImageLoading] = useState(true);
    const [isError, setIsError] = useState(false);

    return (
        <>
            {isImageLoading && (
                <IoImageOutline
                    className={
                        (isError ? "text-red-800" : "text-slate-500") + " " + onErrorClassName
                    }
                />
            )}
            <img
                src={src}
                alt={alt}
                className={
                    (isImageLoading || isError ? "hidden" : "block") +
                    " " +
                    className
                }
                onLoad={() => setImageLoading(false)}
                onError={(_) => {
                    setIsError(true);
                }}
            />
        </>
    );
};

export default ImageComponent;
