import { FC, Suspense } from "react";
import { Route, Routes } from "react-router-dom";
import SplashScreenComponent from "./SplashScreenComponent";
import { RoutsConstants } from "../constants/RoutesConstants";

interface IProps {
    type?: 'admin' | 'pos';
}

const SetRoutesComponent: FC<IProps> = ({ type = 'admin' }) => {
    return (
        <Suspense
            fallback={<SplashScreenComponent height="h-full" width="w-full" />}
        >
            <Routes>
                {Object.keys((RoutsConstants as any)[`${type}`]).map((route, key) => {
                    const Component = (RoutsConstants as any)[`${type}`][route].component;
                    const path = (RoutsConstants as any)[`${type}`][route].path;

                    return <Route key={key} path={path} element={<Component />} />;
                })}
                <Route
                    path={RoutsConstants.notFound.path}
                    element={<RoutsConstants.notFound.component />}
                />
            </Routes>
        </Suspense>
    );
};

export default SetRoutesComponent;
