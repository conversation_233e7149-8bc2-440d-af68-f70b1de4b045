import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { IPurchaseInvoiceSumModel } from "../../../../common/models/PurchaseInvoiceSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { PurchasesReportPdfPrinterContent } from "../../../../common/pdf-make/slices/purchasesReport/PurchasesReportPdfPrinterContent";
import { IPurchasesReportPdfPrinterModel } from "../../../../common/pdf-make/slices/purchasesReport/PurchasesReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminPurchasesReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        purchaseInvoices: IPurchaseInvoiceModel[] | undefined,
        purchaseInvoicesSum: IPurchaseInvoiceSumModel | undefined
    ) {
        const model: IPurchasesReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: purchaseInvoices?.map((el) => ({
                invoiceNumber: el.number.toString(),
                supplierName: el.supplier?.name,
                subTotal: el.subTotal,
                discount: el.productsDiscount,
                vat: el.vat,
                total: el.total,
                cash: el.cash,
                network: el.network,
                credit: el.deferred,
                date: DateUtils.format(el.date),
                dueDate: DateUtils.format(el.dueDate),
            })),
            totals: {
                subTotal: purchaseInvoicesSum?.subTotal ?? 0,
                vat: purchaseInvoicesSum?.vat ?? 0,
                total: purchaseInvoicesSum?.total ?? 0,
                cash: purchaseInvoicesSum?.cash ?? 0,
                network: purchaseInvoicesSum?.network ?? 0,
                credit: purchaseInvoicesSum?.deferred ?? 0,
            },
        };
        PdfMakeUtils.download(PurchasesReportPdfPrinterContent(model), "Purchases Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }
}
