import TitledCardComponent from "../../../../../common/components/TitledCardComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { Bar } from "react-chartjs-2";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";
import { FC } from "react";
import { IReturnedOrderSumModel } from "../../../../../common/models/ReturnedOrderSumModel";

interface IProps {
    orderSum?: IOrderSumModel;
    returnedOrderSum?: IReturnedOrderSumModel;
}

const AdminDashboardPaymentMethodsFeature: FC<IProps> = ({
    orderSum,
    returnedOrderSum,
}) => {
    const barData = {
        labels: [
            TranslateHelper.t(TranslateConstants.APPS),
            TranslateHelper.t(TranslateConstants.CREDIT),
            TranslateHelper.t(TranslateConstants.NETWORK),
            TranslateHelper.t(TranslateConstants.CASH),
        ],
        datasets: [
            {
                label: 'Store 2',
                data: [
                    (orderSum?.deliveryApps ?? 0) - (returnedOrderSum?.deliveryApps ?? 0),
                    (orderSum?.deferred ?? 0) - (returnedOrderSum?.deferred ?? 0),
                    (orderSum?.network ?? 0) - (returnedOrderSum?.network ?? 0),
                    (orderSum?.cash ?? 0) - (returnedOrderSum?.cash ?? 0),
                ],
                backgroundColor: 'rgba(53, 162, 235, 1)'
            }
        ]
    }

    return (
        <TitledCardComponent title={TranslateConstants.PAYMENT_METHODS}>
            <div className="flex justify-center items-center h-full">
                <Bar
                    options={{
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false,
                            }
                        }
                    }}
                    data={barData}
                />
            </div>
        </TitledCardComponent>
    );
}

export default AdminDashboardPaymentMethodsFeature;
