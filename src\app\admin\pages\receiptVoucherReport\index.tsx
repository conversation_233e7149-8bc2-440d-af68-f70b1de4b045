import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminReceiptReportVoucherDestinationsConstant, AdminReceiptVouchersReportTableHeaders } from "./AdminReceiptVouchersConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminReceiptVouchersReportTotalInfoFeature from "./features/AdminReceiptVouchersReportTotalInfoFeature";
import { AdminReceiptVouchersReportService } from "./ReceiptVouchersReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { ReceiptVouchersApiRepo } from "../../../../common/repos/api/ReceiptVouchersApiRepo";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";

const AdminReceiptVouchersReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: receiptVouchersSum,
        isLoading: receiptVouchersSumLoading,
        isError: receiptVouchersSumError,
        refetch: refetchReceiptVouchersSum,
    } = useFetch(
        "report-" + EndPointsEnums.RECEIPT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
            destination?: string
        ) => ReceiptVouchersApiRepo.getSum(startTime, endTime, destination),
        { autoFetchOnMount: true }
    );

    const {
        data: receiptVouchers,
        isLoading: receiptVouchersLoading,
        isError: receiptVouchersError,
        refetch: refetchReceiptVouchers,
    } = useFetch(
        "report-" + EndPointsEnums.RECEIPT_VOUCHERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
            destination?: string
        ) => ReceiptVouchersApiRepo.getReceiptVouchers({ startTime, endTime, destination, getCustomers: true }),
        { autoFetchOnMount: true }
    );

    const onDate = (
        startDate: Date,
        endDate: Date,
        destination?: { name: string; }
    ) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchReceiptVouchersSum(start, end, destination?.name);
        refetchReceiptVouchers(start, end, destination?.name);
    };

    const onDownload = () =>
        AdminReceiptVouchersReportService.handleDownload(
            dateRange,
            receiptVouchers,
            receiptVouchersSum,
        );

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!receiptVouchers?.length || !receiptVouchersSum}
                showSearch={true}
                items={AdminReceiptReportVoucherDestinationsConstant}
                isSearchable={false}
                titleSelector={(item) => TranslateHelper.t(item.name || TranslateConstants.ALL)}
                defaultValue={TranslateHelper.t(TranslateConstants.ALL)}
            />
            <AdminReceiptVouchersReportTotalInfoFeature
                receiptVoucherSum={receiptVouchersSum}
            />
            <StatusComponent
                isEmpty={!receiptVouchers?.length}
                isLoading={receiptVouchersSumLoading || receiptVouchersLoading}
                isError={receiptVouchersSumError || receiptVouchersError}
                height={isXs ? 17.5 : isSm ? 15 : 15.6}
            >
                <TableComponent
                    headers={AdminReceiptVouchersReportTableHeaders}
                    items={receiptVouchers || []}
                    selectors={(item: IReceiptVoucherModel) => [
                        DateUtils.format(item.date),
                        item.number,
                        item.destination ? TranslateHelper.t(item.destination) : "-",
                        item.customer?.name,
                        item.cash,
                        item.network,
                        item.note,
                    ]}
                    showPrintButton={true}
                    onPrint={AdminReceiptVouchersReportService.handleOnPrint}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminReceiptVouchersReportPage;
