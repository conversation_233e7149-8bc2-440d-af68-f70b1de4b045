import DividerComponent from "../../../../../common/components/DividerComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import useOnlineStatus from "../../../../../common/hooks/useOnlineStatus";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import logo from "/logo.svg";

const PosHomeHeaderFeature = () => {
    const { translate } = useTranslate();
    const isOnline = useOnlineStatus();

    return (
        <>
            <div className="h-[4.2rem] flex justify-between w-full gap-2 p-2 items-center font-tajawal-bold">
                <div className="flex items-center gap-1">
                    <img src={logo} alt="logo" className="w-14" />
                    <div>
                        <p>
                            {translate(TranslateConstants.APP_NAME_EN)}{" "}
                            {translate(TranslateConstants.APP_SLOGAN_EN)}
                        </p>
                        <p>
                            {translate(TranslateConstants.APP_NAME)}{" "}
                            {translate(TranslateConstants.APP_SLOGAN)}
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-1">
                    <div
                        className={
                            "w-2 h-2 rounded-full" +
                            " " +
                            (isOnline ? "bg-green-700" : "bg-red-700")
                        }
                    ></div>
                    <p>{isOnline ? "متصل" : "غير متصل"}</p>
                </div>
                <div className="text-left flex flex-col gap-1 justify-center">
                    <p className="text-sm">المستخدم الحالي: كاشير</p>
                    <p className="text-sm">الفرع : فرع 1</p>
                </div>
            </div>
            <DividerComponent color="bg-gray-400" />
        </>
    );
};

export default PosHomeHeaderFeature;
