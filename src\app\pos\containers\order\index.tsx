import { useAppSelector } from "../../../../common/redux/store";
import { posOrderSelector } from "../../redux/selector";
import PosOrderTopButtonsComponent from "./features/PosOrderTopButtonsComponent";
import PosOrderControlButtonsComponent from "./features/PosOrderControlButtonsComponent";
import PosOrderInfoComponent from "./features/PosOrderInfoComponent";
import PosOrderProductComponent from "./features/PosOrderProductComponent";
import PosOrderSubmitButtonComponent from "./features/PosOrderSubmitButtonComponent";
import { useMemo } from "react";
import { IPosOrder } from "../../interface";
import { EqualityUtils } from "../../../../common/utils/EqualityUtils";
import { PosOrderUtils } from "./PosOrderUtils";

const PosOrderComponent = () => {
    const order = useAppSelector(posOrderSelector);
    const oldOrder: IPosOrder = useMemo(() => order, []);
    const isOrderEqual = useMemo(() => {
        return EqualityUtils.isOrderEqual(oldOrder, order);
    }, [oldOrder, order]);
    const orderDiff = useMemo(() => {
        return PosOrderUtils.handleProductDifference(oldOrder, order);
    }, [oldOrder, order]);

    return (
        <div className="flex flex-col h-full">
            <div className="border-r border-gray-400 bg-white pb-2 h-full w-full flex flex-col justify-between">
                <PosOrderTopButtonsComponent order={order} oldOrder={oldOrder}/>
                <PosOrderProductComponent order={order} />
                <div className="flex flex-col gap-2 mt-2">
                    <PosOrderControlButtonsComponent
                        order={order}
                        orderDiff={orderDiff}
                        {...isOrderEqual}
                    />
                    <PosOrderInfoComponent order={order} />
                </div>
            </div>
            <PosOrderSubmitButtonComponent order={order} {...isOrderEqual} />
        </div>
    );
};

export default PosOrderComponent;
