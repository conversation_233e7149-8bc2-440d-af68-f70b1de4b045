interface IInvoiceAdditionModel {
    name: string;
    secondName?: string;
    quantity: string;
    price: string;
}

export interface IInvoicePrinterItemModel {
    quantity: string;
    name: string;
    secondName?: string;
    price: string;
    additions?: IInvoiceAdditionModel[];
}

export interface IInvoicePdfPrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    orderType: string;
    invoiceNumber: string;
    orderNumber: string;
    items: IInvoicePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    licenseNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    cash: string;
    network: string;
    deferred: string;
    totalDeliverApp: string;
    qrCode: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
    invoiceDate: string;
}