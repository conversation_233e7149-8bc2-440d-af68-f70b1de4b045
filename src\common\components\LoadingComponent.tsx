import { TranslateConstants } from "../constants/TranslateConstants";
import { loadingSelector } from "../redux/data/selector";
import { useAppSelector } from "../redux/store";
import logo from "/logo.svg";
import { useTranslate } from "../hooks/useTranslate";

export const LoadingLogoComponent = ({
    className = "fixed top-0 h-s w-s text-white",
}) => {
    const { translate } = useTranslate();

    return (
        <div className={"text-center flex justify-center items-center flex-col gap-2" + " " + className}>
            <img src={logo} alt="logo" className="animate-h_spin w-20 mb-2" />
            <h1
                className={`text-xl`}
            >
                {translate(TranslateConstants.APP_NAME)}
            </h1>
        </div>
    );
};

const LoadingComponent = () => {
    const loading = useAppSelector(loadingSelector);

    if (!loading) return <></>;

    return (
        <div className="h-s w-s fixed z-50 top-0">
            <div className="bg-gray-800 opacity-75 h-s w-s"></div>
            <LoadingLogoComponent />
        </div>
    );
};

export default LoadingComponent;
