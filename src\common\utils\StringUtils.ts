type stringTypes = "camel" | "snake" | "kebab"| "CapitalCamel" | "CapitalKebab" | "CapitalSnake" | "UpperKebab" | "UpperSnake";

export const stringConvert = (
    str: string,
    type: stringTypes  = "camel"
): string => {
    const charactersToReplace = /[_-]/g;
    const words = str.split(charactersToReplace);

    if (type === "kebab") return words.map((word) => word.toLowerCase()).join("-").toLowerCase();
    if (type === "snake") return words.map((word) => word.toLowerCase()).join("_").toLowerCase();
    if (type === "CapitalCamel") return words.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join("");
    if (type === "CapitalKebab") return words.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join("-");
    if (type === "CapitalSnake") return words.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join("_");
    if (type === "UpperKebab") return words.map((word) => word.toUpperCase()).join("-");
    if (type === "UpperSnake") return words.map((word) => word.toUpperCase()).join("_");

    // default is camel
    const firstWord = words[0].toLowerCase();
    const restWords = words.slice(1).map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());
    return firstWord + restWords.join("");
}