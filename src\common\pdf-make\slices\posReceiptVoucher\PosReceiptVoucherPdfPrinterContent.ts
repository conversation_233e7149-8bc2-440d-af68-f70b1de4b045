import { Content } from "pdfmake/interfaces";
import { PdfMakeHelper } from "../../PdfMakeHelper";
import { DefaultPdfMakePrinterContentOptions, RsSvgPlaceholder } from "../../PdfMakeConstants";
import { IPosReceiptVoucherPdfPrinterBodyModel } from "./PosReceiptVoucherPdfPrinterModel";
import { IPdfMakePrinterContentOptionsModel } from "../../PdfMakeInterfaces";

export const PosReceiptVoucherPdfPrinterContent = (
    receiptVoucher: IPosReceiptVoucherPdfPrinterBodyModel,
    options: IPdfMakePrinterContentOptionsModel = DefaultPdfMakePrinterContentOptions,
): Content[] => {
    const defaultOptions: IPdfMakePrinterContentOptionsModel = { ...DefaultPdfMakePrinterContentOptions, ...options };

    return [
        ...PdfMakeHelper.optionalDocItem(!!defaultOptions?.showLogo && !!defaultOptions.logo, [
            {
                image: defaultOptions.logo!,
                width: 150,
                height: 100,
            },
        ]),
        {
            text: receiptVoucher.organizationName,
            bold: true,
            fontSize: 15,
            marginTop: 5,
        },
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.organizationSubName, [
            {
                text: receiptVoucher.organizationSubName,
                bold: true,
                marginTop: 5,
            },
        ]),
        PdfMakeHelper.printDivider(),
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.address, [
            PdfMakeHelper.longText(receiptVoucher.address!, 45, {
                margin: [0, 5, 0, 5],
            }),
        ]),
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.crNo, [
            {
                text: `Cr No: ${receiptVoucher.crNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.vatNo, [
            {
                text: `Vat No: ${receiptVoucher.vatNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.phone, [
            {
                text: `Phone: ${receiptVoucher.phone}`,
                marginBottom: 5,
            },
        ]),
        {
            text: receiptVoucher.invoiceTitle,
        },
        PdfMakeHelper.printDivider(),
        {
            columns: [
                { text: receiptVoucher.date, alignment: "left", fontSize: 8 },
                { text: "تاريخ السند", alignment: "right", fontSize: 8 },
            ],
        },
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.customerName, [
            {
                marginTop: 10,
                columns: [
                    {
                        text: receiptVoucher.customerName!,
                        alignment: "left",
                        fontSize: 8,
                    },
                    {
                        text: "اسم العميل",
                        alignment: "right",
                        fontSize: 8,
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!receiptVoucher.customerMobile, [
            {
                marginTop: 10,
                columns: [
                    {
                        text: receiptVoucher.customerMobile!,
                        alignment: "left",
                        fontSize: 8,
                    },
                    {
                        text: "رقم الجوال",
                        alignment: "right",
                        fontSize: 8,
                    },
                ],
            },
        ]),
        {
            marginTop: 10,
            marginBottom: 10,
            columns: [
                {
                    columns: [
                        {
                            svg: RsSvgPlaceholder,
                            marginLeft: 1,
                            width: 8,
                        },
                        {
                            marginTop: 1,
                            marginLeft: 2,
                            text: receiptVoucher.total,
                            fontSize: 8,
                        },
                    ],
                    alignment: "left",
                },
                { text: "المبلغ", alignment: "right", fontSize: 8 },
            ],
        },
        PdfMakeHelper.longText(
            `${receiptVoucher.customerName ? 'قبض من عميل ' : 'إضافة إلى العهدة'} ${receiptVoucher.note ? "-  " : ""}` +
            receiptVoucher.note
                ?.split("")
                .map((char) => {
                    if (char === "(") return ")";
                    if (char === ")") return "(";
                    return char;
                })
                .join(""),
            45,
            {
                alignment: "right",
                fontSize: 8,
            }
        ),
        PdfMakeHelper.printDivider(),
    ];
};
