import { IAdminSettingsInputs } from "./AdminSettingsInterface";
import { AdminSettingsValidation } from "./AdminSettingsValidation";
import { IActions } from "../../../../common/redux/data/useActions";
import { OrganizationRepo } from "../../repos/OrganizationRepo";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { debug } from "../../../../common/utils/CommonUtils";

export class AdminSettingsService {
    static async updateOrganization(
        inputs: IAdminSettingsInputs,
        actions: IActions,
        setState: (state: IAdminSettingsInputs) => void,
        refetch: () => void
    ) {
        try {
            actions.setLoading();

            const isValid = AdminSettingsValidation.validate(inputs);
            if (!isValid) return;

            await OrganizationRepo.update(inputs);
            ToastHelper.success(TranslateConstants.SETTINGS_UPDATED_SUCCESSFULLY);
            setState({ password: "" });
            refetch();
        } catch (error: any) {
            debug(`SettingsService [updateOrganization] Error: ${error}`);
            throw new Error(error);
        } finally {
            actions.setLoading(false);
        }
    }
}
