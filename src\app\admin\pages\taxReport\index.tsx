import { useMemo, useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import { AdminTaxReportService } from "./TaxReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import ListComponent from "../../../../common/components/ListComponent";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { PurchaseInvoiceApiRepo } from "../../../../common/repos/api/PurchaseInvoiceApiRepo";
import AdminTaxReportDataFeature from "./feature/AdminTaxReportDataFeature";

const AdminTaxReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-tax-" + EndPointsEnums.ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrdersApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-tax-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: purchaseInvoiceSum,
        isLoading: purchaseInvoiceSumLoading,
        isError: purchaseInvoiceSumError,
        refetch: refetchPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-tax-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedPurchaseInvoicePaymentsSum,
        isLoading: returnedPurchaseInvoicePaymentsSumLoading,
        isError: returnedPurchaseInvoicePaymentsSumError,
        refetch: refetchReturnedPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-tax-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedPurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchOrderSum(start, end);
        refetchReturnedOrderSum(start, end);
        refetchPurchaseInvoicePaymentsSum(start, end);
        refetchReturnedPurchaseInvoicePaymentsSum(start, end);
    };

    const totals = useMemo(() =>
        AdminTaxReportService.handleTotalInfo(
            orderSum,
            returnedOrderSum,
            purchaseInvoiceSum,
            returnedPurchaseInvoicePaymentsSum,
        ), [
        orderSum,
        returnedOrderSum,
        purchaseInvoiceSum,
        returnedPurchaseInvoicePaymentsSum,
    ]);

    const onDownload = () =>
        AdminTaxReportService.handleDownload(dateRange, totals);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={
                    !orderSum ||
                    !returnedOrderSum ||
                    !purchaseInvoiceSum ||
                    !returnedPurchaseInvoicePaymentsSum
                }
                disableSearchButtonByDefault={false}
            />
            <StatusComponent
                isEmpty={
                    !orderSum ||
                    !returnedOrderSum ||
                    !purchaseInvoiceSum ||
                    !returnedPurchaseInvoicePaymentsSum
                }
                isLoading={
                    orderSumLoading ||
                    returnedOrderSumLoading ||
                    purchaseInvoiceSumLoading ||
                    returnedPurchaseInvoicePaymentsSumLoading
                }
                isError={
                    orderSumError ||
                    returnedOrderSumError ||
                    purchaseInvoiceSumError ||
                    returnedPurchaseInvoicePaymentsSumError
                }
                height={isXs ? 10.6 : isSm ? 8 : 8.1}
            >
                <ListComponent padding="0">
                    <AdminTaxReportDataFeature 
                        data={totals}
                    />
                </ListComponent>
            </StatusComponent>
        </div>
    );
};

export default AdminTaxReportPage;
