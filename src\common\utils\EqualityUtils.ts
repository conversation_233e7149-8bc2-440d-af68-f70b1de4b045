import { IPosOrder } from "../../app/pos/interface";

export class EqualityUtils {
    static isOrderEqual(order: IPosOrder, updatedOrder: IPosOrder) {
        let isTableEqual = true;
        let isProductsEqual = true;

        if (order.table?.id !== updatedOrder.table?.id) {
            isTableEqual = false;
        }

        if (order.orderProducts.length !== updatedOrder.orderProducts.length) {
            isProductsEqual = false;
        } else {
            for (let i = 0; i < order.orderProducts.length; i++) {
                if (
                    order.orderProducts[i].product.id !==
                    updatedOrder.orderProducts[i].product.id ||
                    order.orderProducts[i].quantity !== updatedOrder.orderProducts[i].quantity
                ) {
                    isProductsEqual = false;
                    break;
                }
            }
        }

        return { isProductsEqual, isTableEqual };
    }
}
