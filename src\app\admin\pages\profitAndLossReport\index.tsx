import { useMemo, useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import { AdminProfitAndLossReportReportService } from "./ProfitAndLossReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import ListComponent from "../../../../common/components/ListComponent";
import AdminProfitAndLossReportDataFeature from "./feature/AdminProfitAndLossReportDataFeature";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { PurchaseInvoiceApiRepo } from "../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { IPaymentVoucherDestinationSumModel } from "../../../../common/models/PaymentVoucherSumModel";

const AdminProfitAndLossReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-profitAndLoss-" + EndPointsEnums.ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrdersApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-profitAndLoss-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: purchaseInvoiceSum,
        isLoading: purchaseInvoiceSumLoading,
        isError: purchaseInvoiceSumError,
        refetch: refetchPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-profitAndLoss-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: returnedPurchaseInvoicePaymentsSum,
        isLoading: returnedPurchaseInvoicePaymentsSumLoading,
        isError: returnedPurchaseInvoicePaymentsSumError,
        refetch: refetchReturnedPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-profitAndLoss-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedPurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const {
        data: paymentVouchersSum,
        isLoading: paymentVouchersSumLoading,
        isError: paymentVouchersSumError,
        refetch: refetchPaymentVouchersSum,
    } = useFetch(
        "report-profitAndLoss-" + EndPointsEnums.PAYMENT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PaymentVouchersApiRepo.getSum<IPaymentVoucherDestinationSumModel[]>(startTime, endTime, undefined, undefined, undefined, true),
        { resetOnUnmount: true, autoFetchIfEmpty: false }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchOrderSum(start, end);
        refetchReturnedOrderSum(start, end);
        refetchPurchaseInvoicePaymentsSum(start, end);
        refetchReturnedPurchaseInvoicePaymentsSum(start, end);
        refetchPaymentVouchersSum(start, end);
    };

    const totals = useMemo(() =>
        AdminProfitAndLossReportReportService.handleTotalInfo(
            orderSum,
            returnedOrderSum,
            purchaseInvoiceSum,
            returnedPurchaseInvoicePaymentsSum,
            paymentVouchersSum,
        ), [
        orderSum,
        returnedOrderSum,
        purchaseInvoiceSum,
        returnedPurchaseInvoicePaymentsSum,
        paymentVouchersSum,
    ]);

    const onDownload = () =>
        AdminProfitAndLossReportReportService.handleDownload(dateRange, totals);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={
                    !orderSum ||
                    !returnedOrderSum ||
                    !purchaseInvoiceSum ||
                    !returnedPurchaseInvoicePaymentsSum ||
                    !paymentVouchersSum
                }
                disableSearchButtonByDefault={false}
            />
            <StatusComponent
                className="!p-0"
                isEmpty={
                    !orderSum ||
                    !returnedOrderSum ||
                    !purchaseInvoiceSum ||
                    !returnedPurchaseInvoicePaymentsSum ||
                    !paymentVouchersSum
                }
                isLoading={
                    orderSumLoading ||
                    returnedOrderSumLoading ||
                    purchaseInvoiceSumLoading ||
                    returnedPurchaseInvoicePaymentsSumLoading ||
                    paymentVouchersSumLoading
                }
                isError={
                    orderSumError ||
                    returnedOrderSumError ||
                    purchaseInvoiceSumError ||
                    returnedPurchaseInvoicePaymentsSumError ||
                    paymentVouchersSumError
                }
                height={isXs ? 10.6 : isSm ? 8 : 8.1}
            >
                <ListComponent padding="0">
                    <AdminProfitAndLossReportDataFeature
                        data={totals}
                    />
                </ListComponent>
            </StatusComponent>
        </div>
    );
};

export default AdminProfitAndLossReportPage;
