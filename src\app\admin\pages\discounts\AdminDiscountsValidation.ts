import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminDiscountInputs } from "./AdminDiscountsInterface";

export class AdminDiscountsValidation {
    static inputsValidation = (values: IAdminDiscountInputs) => {
        let isValid = true;

        if (!values.amount) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DISCOUNT_FIELD_IS_REQUIRED);
        } else if (isNaN(Number(values.amount)) || Number(values.amount) <= 0 || Number(values.amount) > 100) {
            isValid = false;
            ToastHelper.error(TranslateConstants.DISCOUNT_FIELD_IS_NOT_VALID);
        } else if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        }

        return isValid;
    };
}
