import { BaseModel } from ".";
import { PaymentsEnum } from "../enums/DataEnums";
import { ISupplierModel } from "./SuppliersModel";

export interface IPurchaseOrderProductModel {
    rawMaterialId: number;
    isTaxable: boolean;
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IPurchaseOrderModel extends BaseModel {
    number: number;
    date: number;
    dueDate: number;
    paymentMethod: PaymentsEnum;
    supplier: ISupplierModel;
    isPriceIncludingTax: boolean;
    purchaseOrderProducts: IPurchaseOrderProductModel[];
    note: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
    isConverted: boolean;
}