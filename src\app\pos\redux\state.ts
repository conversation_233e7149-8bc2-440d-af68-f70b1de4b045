import { OrderStatusEnum, OrderTypeEnum } from "../../../common/enums/DataEnums";
import IState, { IPosHomeState } from "./interface";

export const PosHomeGeneralInitialState: IPosHomeState = {
    view: "categories",
    selectedCategory: undefined,
};

const InitialState: IState = {
    products: [],
    additions: [],
    categories: [],
    tables: [],
    discounts: [],
    order: {
        id: 0,
        orderId: 0,
        shiftId: 0,
        deleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        type: OrderTypeEnum.TAKE_AWAY,
        orderProducts: [],
        status: OrderStatusEnum.PENDING,
        table: undefined,
        deliveryApp: undefined,
        customer: undefined,
        selectedDiscount: undefined,
        invoiceNumber: undefined,
        orderNumber: undefined,
        subTotal: 0,
        discount: 0,
        vat: 0,
        tobaccoTax: 0,
        total: 0,
        cash: 0,
        network: 0,
        deferred: 0,
        startTime: undefined,
        endTime: undefined,
        deliveryAppFee: 0,
        totalDue: 0,
        isPaused: false,
    },
    home: {
        ...PosHomeGeneralInitialState,
        isShiftOpen: false,
    },
};

export default InitialState;
