import { AppHelper } from "../helpers/AppHelper";
import { IsArabic } from "../hooks/useTranslate";

export class DateUtils {
    static toIsoString(now: Date | number | string = new Date()): string {
        if (typeof now === "number") now = new Date(now);
        else if (typeof now === "string" && !isNaN(Number(now))) now = new Date(Number(now));
        else if (typeof now === "string") now = new Date(now);
        const offset = now.getTimezoneOffset() * 60000;
        const localISOTime = new Date((now as any) - offset)
            .toISOString()
            .slice(0, 16);
        return localISOTime;
    }

    static getTime(date: Date | number | string = new Date()): number {
        if (typeof date === "number") return date;
        else if (typeof date === "string" && !isNaN(Number(date))) return Number(date);
        else if (typeof date === "string") return new Date(date).getTime();
        return date.getTime();
    }

    static format(
        date: Date | number | string,
        format: string = "yyyy-MM-dd hh:mm A",
        strictTimeSuffix = false
    ): string {
        if (typeof date === "number") date = new Date(date);
        else if (typeof date === "string" && !isNaN(Number(date)))
            date = new Date(Number(date));
        else if (typeof date === "string") date = new Date(date);

        const map: any = {
            M: date.getMonth() + 1,
            d: date.getDate(),
            H: date.getHours(),
            m: date.getMinutes(),
            s: date.getSeconds(),
            y: date.getFullYear(),
            A: date.getHours() < 12 ?
                (!strictTimeSuffix && IsArabic()) ? "ص" : "AM" :
                (!strictTimeSuffix && IsArabic()) ? "م" : "PM",
            h: (date.getHours() % 12) || 12,
        };

        return format.replace(/y+|M+|d+|h+|m+|s+|A+|H+/g, (str: string) => {
            const len = str.length;
            const key = str[0];
            return (map[key] + "").padStart(len, "0");
        });
    }

    static getStartOfDay(date: Date = new Date()): Date {
        return new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            0,
            0,
            0,
            0
        );
    }

    static getStartOfDayNumber(date: Date = new Date()): number {
        return DateUtils.getStartOfDay(date).getTime();
    }

    static getEndOfDay(date: Date = new Date()): Date {
        return new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            59,
            999
        );
    }

    static getEndOfDayNumber(date: Date = new Date()): number {
        return DateUtils.getEndOfDay(date).getTime();
    }

    static timeZone(
        date: Date = new Date(),
        timeZone: "Europe/Berlin" | "Asia/Riyadh" = "Europe/Berlin"
    ): Date {
        if (AppHelper.isDevEnv()) return date;
        const frankfurtTime = date.toLocaleString("en-US", { timeZone });
        return new Date(frankfurtTime);
    }

    static isValidDate(date: any): boolean {
        return !isNaN(new Date(date).getTime());
    }
}