import { Content } from 'pdfmake/interfaces';
import { IInvoicePdfPrinterBodyModel } from './InvoicePdfPrinterModel';
import { PdfMakeHelper } from '../../PdfMakeHelper';
import { isPositive } from '../../../utils/CommonUtils';
import { DefaultPdfMakePrinterContentOptions, RsSvgPlaceholder } from '../../PdfMakeConstants';
import { RsSvg } from '../../../assets/svgs/RsSvg';
import { IPdfMakePrinterContentOptionsModel } from '../../PdfMakeInterfaces';

const getTotalAmounts = (invoice: IInvoicePdfPrinterBodyModel, isSvgPlaceholder: boolean = true) => {
    const totalAmounts = [];

    (isPositive(invoice.nativeTotal) && isPositive(invoice.discount)) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.nativeTotal,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'إجمالي الفاتورة', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.discount) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.discount,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'الخصم', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.subTotal) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.subTotal,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'الإجمالي غير شامل الضريبة', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.tobaccoTax || "") &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.tobaccoTax,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'رسوم التبغ %100', alignment: 'right', fontSize: 8, },
        ]);

    isPositive(invoice.vat) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.vat,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'ضريبة القيمة المضافة %15', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.total) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.total,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'الإجمالي شامل الضريبة', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.cash) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.cash,
                        fontSize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'المدفوع نقداً', alignment: 'right', fontSize: 8 },
        ]);

    isPositive(invoice.network) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.network,
                        fontsize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'المدفوع شبكة', alignment: 'right', fontsize: 8 },
        ]);

    isPositive(invoice.deferred) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.deferred,
                        fontsize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'اجل', alignment: 'right', fontsize: 8 },
        ]);

    isPositive(invoice.totalDeliverApp) &&
        totalAmounts.push([
            {
                columns: [
                    {
                        svg: isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                        marginLeft: 1,
                        width: 8,
                    },
                    {
                        marginTop: 1,
                        marginLeft: 2,
                        text: invoice.totalDeliverApp,
                        fontsize: 8,
                    },
                ],
                alignment: 'left',
            },
            { text: 'المدفوع ' + invoice.orderType, alignment: 'right', fontsize: 8 },
        ]);

    return totalAmounts;
};

export const InvoicePdfPrinterContent = (
    invoice: IInvoicePdfPrinterBodyModel,
    options: IPdfMakePrinterContentOptionsModel = DefaultPdfMakePrinterContentOptions,
): Content[] => {
    const defaultOptions: IPdfMakePrinterContentOptionsModel = { ...DefaultPdfMakePrinterContentOptions, ...options };
    return [
        ...PdfMakeHelper.optionalDocItem(!!defaultOptions?.showLogo && !!defaultOptions.logo, [
            {
                image: defaultOptions.logo!,
                width: 150,
                height: 100,
            },
        ]),
        {
            text: invoice.organizationName,
            bold: true,
            fontSize: 15,
            marginTop: 5,
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.organizationSubName, [
            {
                text: invoice.organizationSubName,
                bold: true,
                marginTop: 5,
            },
        ]),
        PdfMakeHelper.printDivider(),
        ...PdfMakeHelper.optionalDocItem(!!invoice.address, [
            PdfMakeHelper.longText(invoice.address!, 45, {
                margin: [0, 5, 0, 5],
                reverseText: defaultOptions?.reverseLongText,
            }),
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.licenseNo, [
            {
                text: `L.No: ${invoice.licenseNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.crNo, [
            {
                text: `Cr No: ${invoice.crNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.vatNo, [
            {
                text: `Vat No: ${invoice.vatNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.phone, [
            {
                text: `Phone: ${invoice.phone}`,
                marginBottom: 5,
            },
        ]),
        {
            text: invoice.invoiceTitle,
        },
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.textWithBorder(`Order # ${invoice.orderNumber}`),
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderType, [
            {
                text: invoice.orderType,
                marginTop: 10,
                fontSize: 12,
            },
        ]),
        {
            text: `تاريخ الفاتورة: ${invoice.invoiceDate}`,
            marginTop: 10,
        },
        {
            text: `فاتورة: ${invoice.invoiceNumber}`,
            marginTop: 10,
        },
        ...PdfMakeHelper.optionalDocItem(
            !!invoice.customerName ||
            !!invoice.customerMobile ||
            !!invoice.customerTaxNumber ||
            !!invoice.customerAddress,
            [PdfMakeHelper.printDivider(false, true)],
        ),
        ...PdfMakeHelper.optionalDocItem(!!invoice.customerName, [
            {
                columns: [
                    {
                        text: invoice.customerName!,
                        alignment: 'left',
                        marginBottom: 5,
                        fontSize: 7,
                    },
                    {
                        text: 'اسم العميل',
                        alignment: 'right',
                        fontSize: 8,
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.customerMobile, [
            {
                columns: [
                    {
                        text: invoice.customerMobile!,
                        alignment: 'left',
                        marginBottom: 5,
                        fontSize: 7,
                    },
                    {
                        text: 'رقم الجوال',
                        alignment: 'right',
                        fontSize: 8,
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.customerTaxNumber, [
            {
                columns: [
                    {
                        text: invoice.customerTaxNumber!,
                        alignment: 'left',
                        marginBottom: 5,
                        fontSize: 7,
                    },
                    {
                        text: 'الرقم الضريبي',
                        alignment: 'right',
                        fontSize: 8,
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.customerAddress, [
            {
                columns: [
                    {
                        text: invoice.customerAddress!,
                        alignment: 'left',
                        marginBottom: 5,
                        fontSize: 7,
                    },
                    {
                        text: 'العنوان',
                        alignment: 'right',
                        fontSize: 8,
                    },
                ],
            },
        ]),
        PdfMakeHelper.printDivider(false, true),
        {
            layout: 'lightHorizontalLines',
            table: {
                widths: ['auto', 'auto', '*'],
                body: [
                    [
                        { text: 'الإجمالي', alignment: 'left', bold: true, fontSize: 7 },
                        { text: 'الكمية', bold: true, fontSize: 7 },
                        { text: 'المنتج', alignment: 'right', bold: true, fontSize: 7 },
                    ],
                    ...invoice.items.map((product) => {
                        return [
                            [
                                {
                                    columns: [
                                        {
                                            marginLeft: 1,
                                            svg: defaultOptions?.isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                                            width: 8,
                                        },
                                        {
                                            marginLeft: 2,
                                            marginTop: 2,
                                            text: product.price,
                                            width: "auto",
                                        }
                                    ],
                                    alignment: 'left',
                                    fontSize: 7,
                                    margin: [0, 2, 0, 4],
                                },
                                product.additions?.map((el) => ({
                                    columns: [
                                        {
                                            marginLeft: 1,
                                            svg: defaultOptions?.isSvgPlaceholder ? RsSvgPlaceholder : RsSvg(),
                                            width: 8,
                                        },
                                        {
                                            marginLeft: 2,
                                            marginTop: 2,
                                            text: el.price,
                                            width: "*",
                                        }
                                    ],
                                    alignment: 'left',
                                    fontSize: 7,
                                    margin: [0, 2, 0, 2],
                                    decoration: Number(el.price) > 0 ? undefined : 'lineThrough',
                                })),
                            ],
                            [
                                {
                                    text: product.quantity,
                                    alignment: 'center',
                                    fontSize: 7,
                                    margin: [0, 4, 0, 4],
                                },
                                product.additions?.map((el) => ({
                                    text: el.quantity,
                                    alignment: 'center',
                                    fontSize: 7,
                                    margin: [0, 4, 0, 2],
                                })),
                            ],
                            [
                                {
                                    text: product.name,
                                    alignment: 'right',
                                    fontSize: 7,
                                    margin: [0, 3],
                                },
                                ...PdfMakeHelper.optionalDocItem(!!product.secondName, [
                                    {
                                        text: product.secondName!,
                                        alignment: 'right',
                                        fontSize: 7,
                                        marginBottom: 3,
                                    },
                                ]),
                                product.additions?.map((el) => {
                                    const refactoredName = PdfMakeHelper.reverseTextIfArabic(el.name);
                                    return ({
                                        text: (el.secondName ? el.secondName + " -" : "") + refactoredName + " *",
                                        alignment: 'right',
                                        fontSize: 7,
                                        margin: [0, 4, 0, 2],
                                    })
                                }),
                            ],
                        ];
                    }),
                ],
            },
        },
        PdfMakeHelper.printDivider(false, true),
        {
            layout: 'noBorders',
            table: {
                widths: ['auto', '*'],
                body: getTotalAmounts(invoice, defaultOptions?.isSvgPlaceholder),
            },
        },
        PdfMakeHelper.printDivider(),
        {
            qr: invoice.qrCode,
            alignment: 'center',
            fit: 100,
        },
        {
            text: invoice.footer || '',
            alignment: 'center',
            marginTop: 10,
        },
    ];
};
