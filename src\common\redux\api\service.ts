import { appApi } from "./slice";
import { ToastHelper } from "../../helpers/ToastHelper";
import { debug } from "../../utils/CommonUtils";
import { AppSlice } from "../data/slice";

export const handleMutate = async (
    query: string | any,
    id: number | undefined,
    queryFulfilled: Promise<any>,
    dispatch: any
) => {
    try {
        dispatch(AppSlice.actions.setLoadingAction(true));
        const { data } = await queryFulfilled;

        const updateDraft = (draft: any) => {
            if (id) return draft.map((e: any) => (e.id === id ? data : e));
            return [data, ...draft];
        };

        const updateAction = appApi.util.updateQueryData(
            query,
            undefined,
            updateDraft
        );

        dispatch(updateAction);
        dispatch(AppSlice.actions.closeModalAction());
        ToastHelper.mutated(!!id);
    } catch (error: any) {
        debug(`WithGlobalOnQueryStarted [handleMutate] Error: `, error);
        ToastHelper.error();
    } finally {
        dispatch(AppSlice.actions.setLoadingAction(false));
    }
};
