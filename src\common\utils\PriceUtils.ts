import { IPosOrder } from "../../app/pos/interface";
import { OrderTypeEnum } from "../enums/DataEnums";
import { DeliveryAppsHelper } from "../helpers/DeliveryAppsHelper";
import { IAdditionModel } from "../models/AdditionsModel";
import { IProductModel, IProductSizeModel } from "../models/ProductModel";

export class PriceUtils {
    static getProductPrice = (
        product: IProductModel | IProductSizeModel | IAdditionModel,
        order: IPosOrder
    ) => {
        if (order.type !== OrderTypeEnum.DELIVERY_APP || !order.deliveryApp)
            return product.price;

        const { key } = DeliveryAppsHelper.getDeliverAppKeys(
            order.deliveryApp
        );

        const price = (product.deliveryApps as any)?.[key];
        return price || product.price;
    };
}
