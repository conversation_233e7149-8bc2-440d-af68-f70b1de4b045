import { FC } from "react";
import { useTranslate } from "../hooks/useTranslate";

export interface IMultiColumnsDataViewerComponentItem {
    title: string;
    values: (string | number)[];
    header: string[];
    isFullWidth?: boolean;
    className?: string;
}

interface IProps {
    cols?: number;
    items?: IMultiColumnsDataViewerComponentItem[];
}

const MultiColumnsDataViewerComponent: FC<IProps> = ({
    cols = 2,
    items = [],
}) => {
    const { isArabic } = useTranslate();

    return (
        <div
            className={
                "grid gap-2 md:font-tajawal-bold md:text-sm" +
                " " +
                `grid-cols-1 sm:grid-cols-${cols}`
            }
        >
            {items.map((item, index) => (
                <div
                    key={index}
                    className={
                        "flex flex-col gap-2" +
                        " " +
                        (item.isFullWidth ? "sm:col-span-2" : "") +
                        " " +
                        (item.className ?? "")
                    }
                >
                    <div className="text-center font-tajawal-bold text-lg">
                        {item.title}
                    </div>
                    <div className="flex flex-col gap-2">
                        {item.values.map((value, index) => (
                            <div key={index} className="flex">
                                <div
                                    className={
                                        "flex-1 bg-[#226bb2] p-2 text-white" +
                                        " " +
                                        (isArabic ? "rounded-r" : "rounded-l")
                                    }
                                >
                                    {item.header[index]}
                                </div>
                                <div className={
                                    "w-1/3 p-2 border border-gray-400 rounded text-center bg-gray-50 text-black" +
                                    " " +
                                    (isArabic ? "rounded-r-none border-r-0" : "rounded-l-none border-l-0")
                                }>
                                    {value}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
};

export default MultiColumnsDataViewerComponent;
