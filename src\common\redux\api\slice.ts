import { createApi } from "@reduxjs/toolkit/query/react";
import { customBaseQuery } from "./utils";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { IOrderSumModel } from "../../models/OrderSumModel";
import { IOrderModel } from "../../models/OrderModel";
import { IShiftSumModel } from "../../models/ShiftsSumModel";
import { IShiftModel } from "../../models/ShiftModel";

export const appApi = createApi({
    reducerPath: "appApi",
    baseQuery: customBaseQuery,
    refetchOnReconnect: true,
    endpoints: (builder) => ({
        getOrdersSum: builder.query<IOrderSumModel, { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsEnums.ORDERS_SUM + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getOrders: builder.query<IOrderModel[], { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsEnums.ORDERS + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getShiftsSum: builder.query<IShiftSumModel, { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsEnums.SHIFTS_SUM + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getShifts: builder.query<IShiftModel[], { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsEnums.SHIFTS + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
    }),
});

export const {
    useGetOrdersSumQuery,
    useGetOrdersQuery,
    useGetShiftsSumQuery,
    useGetShiftsQuery,
} = appApi;
