interface IReturnedPurchaseInvoicePdfPrinterItemModel {
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IReturnedPurchaseInvoicePdfPrinterModel {
    items?: IReturnedPurchaseInvoicePdfPrinterItemModel[];
    note?: string;
    totals: {
        nativeTotal: number;
        productsDiscount: number;
        subTotal: number;
        vat: number;
        total: number;
    };
}