import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import ButtonComponent from "./ButtonComponent";
import { TbReload } from "react-icons/tb";
import { IoArrowBack, IoArrowForward } from "react-icons/io5";
import { useTranslate } from "../hooks/useTranslate";
import useScreenSize from "../hooks/useScreenSize";

interface IProps {
    onAdd?: () => void;
    onSaveAndPrint?: () => void;
    onReload?: () => void;
    onSave?: () => void;
    onBack?: () => void;
    isSaveDisabled?: boolean;
    isSaveAndPrintDisabled?: boolean;
    isEdit?: boolean;
    buttonsClassName?: string;
}

const AddAndFilterComponent: FC<IProps> = ({
    onAdd,
    onSaveAndPrint,
    onReload,
    onSave,
    onBack,
    isSaveDisabled,
    isSaveAndPrintDisabled,
    isEdit,
    buttonsClassName,
}) => {
    const { isArabic } = useTranslate();
    const { isXs } = useScreenSize();

    return (
        <div className="flex justify-between gap-1 w-full">
            <div className="w-full flex gap-1 sm:gap-2">
                {onSaveAndPrint && (
                    <ButtonComponent
                        text={
                            isEdit
                                ? TranslateConstants.EDIT_AND_PRINT
                                : TranslateConstants.SAVE_AND_PRINT
                        }
                        onClick={onSaveAndPrint}
                        className={
                            "sm:!w-1/4 md:!w-1/5 lg:!w-1/6 sm:min-w-44" +
                            " " +
                            buttonsClassName
                        }
                        isDisabled={isSaveAndPrintDisabled}
                    />
                )}
                {onSave && (
                    <ButtonComponent
                        text={isEdit ? TranslateConstants.EDIT : TranslateConstants.SAVE}
                        onClick={onSave}
                        className={
                            "sm:!w-1/4 md:!w-1/5 lg:!w-1/6 sm:min-w-44" +
                            " " +
                            buttonsClassName
                        }
                        isDisabled={isSaveDisabled}
                    />
                )}
                {onAdd && (
                    <ButtonComponent
                        text={TranslateConstants.ADD}
                        onClick={onAdd}
                        className={
                            "sm:!w-1/4 md:!w-1/5 lg:!w-1/6 sm:min-w-44" +
                            " " +
                            buttonsClassName
                        }
                    />
                )}
                {onReload && (
                    <ButtonComponent
                        text={isXs ? undefined : TranslateConstants.RELOAD}
                        onClick={onReload}
                        className={"!w-24 mr-auto sm:mr-0" + " " + buttonsClassName}
                        iconComponent={<TbReload />}
                    />
                )}
            </div>
            {onBack && (
                <ButtonComponent
                    text={isXs ? undefined : TranslateConstants.BACK}
                    bgColor="slate"
                    onClick={onBack}
                    className={
                        "!w-24 sm:!w-1/4 md:!w-1/5 lg:!w-1/6 sm:min-w-44 dark:border dark:border-gray-400" +
                        " " +
                        (isArabic ? "mr-auto" : "ml-auto") +
                        " " +
                        buttonsClassName
                    }
                    component={isArabic ? <IoArrowBack /> : <IoArrowForward />}
                />
            )}
        </div>
    );
};

export default AddAndFilterComponent;
