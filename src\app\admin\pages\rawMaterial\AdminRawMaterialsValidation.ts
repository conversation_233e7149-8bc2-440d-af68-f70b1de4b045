import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminRawMaterialInputs } from "./AdminRawMaterialsInterface";

export class AdminRawMaterialsValidation {
    static inputsValidation = (values: IAdminRawMaterialInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        } else if (values.price === undefined || values.price < 0 || values.price > 100000) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRICE_FIELD_MUST_BE_BETWEEN_0_AND_100000);
        }

        return isValid;
    };
}
