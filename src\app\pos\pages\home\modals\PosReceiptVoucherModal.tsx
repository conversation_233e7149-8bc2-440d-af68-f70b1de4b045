import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { ReceiptVoucherDestinationEnum } from "../../../../../common/enums/DataEnums";
import { IAdminReceiptVoucherInputs } from "../../../../admin/pages/receiptVoucher/AdminReceiptVoucherInterface";
import { AdminReceiptVoucherInputs } from "../../../../admin/pages/receiptVoucher/AdminReceiptVoucherConstants";
import { AdminReceiptVoucherValidation } from "../../../../admin/pages/receiptVoucher/AdminReceiptVoucherValidation";
import { PosVoucherViewEnum } from "../PosHomeConstants";
import { PosReceiptVoucherRepo } from "../../../repo/PosReceiptVoucherRepo";
import { FC, useState } from "react";
import NumpadComponent from "../../../../../common/components/NumpadComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import useActions from "../../../../../common/redux/data/useActions";
import { PosInvoicesService } from "../../invoices/PosInvoicesService";
import { ICustomerModel } from "../../../../../common/models/CustomerModel";
import PosOrderPaymentModal from "../../../containers/order/modals/PosOrderPaymentModal";
import { PosCustomersRepo } from "../../../repo/PosCustomersRepo";
import { UpdateFetch } from "../../../../../common/asyncController/updateFetch";

interface IProps {
    customer?: ICustomerModel;
}

const PosReceiptVoucherModal: FC<IProps> = ({ customer }) => {
    const actions = useActions();
    const [view, setView] = useState(PosVoucherViewEnum.NUMPAD);
    const [state, setState] = useState<IAdminReceiptVoucherInputs>({
        ...AdminReceiptVoucherInputs(),
        destination: customer ?
            ReceiptVoucherDestinationEnum.CUSTOMER :
            ReceiptVoucherDestinationEnum.ADD_TO_SHIFT,
        customer,
        note: customer ? TranslateHelper.t(TranslateConstants.DEFERRED_PAY) : "",
    });
    const addReceiptVoucher = useFlatMutate(
        PosReceiptVoucherRepo.addReceiptVoucher,
        {
            showDefaultSuccessToast: true,
            closeModalOnSuccess: true,
            onSuccess: ({ args }) => {
                if (customer && customer.credit) {
                    PosCustomersRepo.updateCustomer(customer.id, {
                        ...customer,
                        credit: customer.credit - (args[0].cash + args[0].network),
                    });
                    UpdateFetch.dynamicUpdate("pos-customers", async (data: ICustomerModel[]) => {
                        return data?.map((item: ICustomerModel) => {
                            if (item.id === customer.id) return {
                                ...item,
                                credit: customer.credit - (args[0].cash + args[0].network)
                            };
                            return item;
                        });
                    }, { safeUpdate: true });
                };
                PosInvoicesService.handleOnPrintReceiptVoucher(actions, args[0])
            },
        }
    );

    const handleCustomerPay = () => {
        actions.openModal({
            component: <PosOrderPaymentModal getNumbersOnly={{
                amount: state.cash,
                onSubmit: (cash, network) => addReceiptVoucher({ ...state, cash, network }),
            }} />,
            showButtons: false,
            size: "md",
            title: TranslateHelper.t(TranslateConstants.CUSTOMER) + " (" + (customer?.name || "") + ")",
        });
    }

    const onClick = () => {
        if (view === PosVoucherViewEnum.NUMPAD) {
            if (!AdminReceiptVoucherValidation.inputsValidation(state)) return;
            if (!customer) setView(PosVoucherViewEnum.STATEMENT_OPTIONS);
            else handleCustomerPay();
            return;
        }

        addReceiptVoucher(state);
    };

    return (
        <>
            <form className="px-2">
                {view === PosVoucherViewEnum.NUMPAD && (
                    <NumpadComponent
                        type="fixed"
                        onChange={(cash) => setState({ ...state, cash: Number(cash) })}
                        placeHolder={customer?.credit?.toString() || "0"}
                    />
                )}
                {view === PosVoucherViewEnum.STATEMENT_OPTIONS && (
                    <>
                        <div className="grid grid-cols-2 gap-2 text-center font-tajawal-medium mb-2">
                            {
                                [
                                    TranslateHelper.t(TranslateConstants.ADD_AMOUNT_TO_SHIFT),
                                    TranslateHelper.t(TranslateConstants.TIPS_DESCRIBED),
                                    TranslateHelper.t(TranslateConstants.ADD_CHANGE_DESCRIBED),
                                ].map((el, index) => (
                                    <div
                                        key={index}
                                        className={
                                            "border p-2 rounded-lg cursor-pointer hover:scale-95 active:bg-base-200 py-8" +
                                            " " +
                                            (state.note === el ? "!border-[#226bb2] text-[#226bb2]" : "border-gray-400")
                                        }
                                        onClick={() => setState({ ...state, note: el })}
                                    >
                                        {el}
                                    </div>
                                ))
                            }
                        </div>
                        <ButtonComponent
                            text={TranslateConstants.OTHER}
                            onClick={() => {
                                setState({ ...state, note: "" });
                                setView(PosVoucherViewEnum.STATEMENT);
                            }}
                            textColor="slate-600"
                            borderColor="gray-400"
                            bgColor="transparent"
                            className="py-4"
                        />
                    </>
                )}
                {view === PosVoucherViewEnum.STATEMENT && (
                    <InputComponent
                        label={TranslateConstants.STATEMENT}
                        type="textarea"
                        value={state.note || ""}
                        onChange={(note) => setState({ ...state, note })}
                        textAreaRows={2}
                    />
                )}
            </form>
            <ModalButtonsComponent
                text={
                    view === PosVoucherViewEnum.NUMPAD
                        ? TranslateConstants.NEXT
                        : TranslateConstants.SAVE
                }
                onClick={onClick}
            />
        </>
    );
};

export default PosReceiptVoucherModal;
