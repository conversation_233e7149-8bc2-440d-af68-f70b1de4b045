import { Content } from 'pdfmake/interfaces';
import { PdfMakeHelper } from '../../PdfMakeHelper';
import { DefaultPdfMakePrinterContentOptions, RsSvgPlaceholder } from '../../PdfMakeConstants';
import { IPosPaymentVoucherPdfPrinterBodyModel } from './PosPaymentVoucherPdfPrinterModel';
import { IPdfMakePrinterContentOptionsModel } from '../../PdfMakeInterfaces';

export const PosPaymentVoucherPdfPrinterContent = (
    paymentVoucher: IPosPaymentVoucherPdfPrinterBodyModel,
    options: IPdfMakePrinterContentOptionsModel = DefaultPdfMakePrinterContentOptions,
): Content[] => {
    const defaultOptions: IPdfMakePrinterContentOptionsModel = { ...DefaultPdfMakePrinterContentOptions, ...options };

    return [
        ...PdfMakeHelper.optionalDocItem(!!defaultOptions?.showLogo && !!defaultOptions.logo, [
            {
                image: defaultOptions.logo!,
                width: 150,
                height: 100,
            },
        ]),
        {
            text: paymentVoucher.organizationName,
            bold: true,
            fontSize: 15,
            marginTop: 5,
        },
        ...PdfMakeHelper.optionalDocItem(!!paymentVoucher.organizationSubName, [
            {
                text: paymentVoucher.organizationSubName,
                bold: true,
                marginTop: 5,
            },
        ]),
        PdfMakeHelper.printDivider(),
        ...PdfMakeHelper.optionalDocItem(!!paymentVoucher.address, [
            PdfMakeHelper.longText(paymentVoucher.address!, 45, {
                margin: [0, 5, 0, 5],
            }),
        ]),
        ...PdfMakeHelper.optionalDocItem(!!paymentVoucher.crNo, [
            {
                text: `Cr No: ${paymentVoucher.crNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!paymentVoucher.vatNo, [
            {
                text: `Vat No: ${paymentVoucher.vatNo}`,
                marginBottom: 5,
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!paymentVoucher.phone, [
            {
                text: `Phone: ${paymentVoucher.phone}`,
                marginBottom: 5,
            },
        ]),
        {
            text: paymentVoucher.invoiceTitle,
        },
        PdfMakeHelper.printDivider(),
        {
            columns: [
                { text: paymentVoucher.date, alignment: 'left', fontSize: 8 },
                { text: 'تاريخ السند', alignment: 'right', fontSize: 8 },
            ],
        },
        {
            marginTop: 10,
            marginBottom: 10,
            columns: [
                {
                    columns: [
                        {
                            svg: RsSvgPlaceholder,
                            marginLeft: 1,
                            width: 8,
                        },
                        {
                            marginTop: 1,
                            marginLeft: 2,
                            text: paymentVoucher.total,
                            fontSize: 8,
                        },
                    ],
                    alignment: 'left',
                },
                { text: 'المبلغ', alignment: 'right', fontSize: 8 },
            ],
        },
        PdfMakeHelper.longText(`صرف من العهدة ${paymentVoucher.note ? '-  ' : ''}` + paymentVoucher.note, 45, {
            alignment: 'right',
            fontSize: 8,
        }),
        PdfMakeHelper.printDivider(),
    ];
};
