import { IMultiInputsItem } from "../../../../common/components/MultiInputsComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { PaymentsEnum } from "../../../../common/enums/DataEnums";
import { IRawMaterialModel } from "../../../../common/models/RawMaterialModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { PurchaseUtils } from "../../../../common/utils/PurchaseUtils";
import {
    IAdminPurchaseOrderInputs,
    IAdminPurchaseOrderState,
} from "./AdminPurchaseOrderInterfaces";

export const AdminPurchaseOrderHeaders = [
    TranslateConstants.PRODUCT,
    TranslateConstants.QUANTITY,
    TranslateConstants.PRICE,
    TranslateConstants.DISCOUNT,
    TranslateConstants.TOTAL,
    "",
];

export const AdminPurchaseOrderInputs = (
    isPriceIncludingTax: boolean,
    rawMaterials?: IRawMaterialModel[],
): IMultiInputsItem<IAdminPurchaseOrderInputs>[] => {
    return [
        {
            name: "rawMaterial",
            width: "!w-5/12",
            type: "dropDown",
            items: rawMaterials || [],
            titleSelector: (item: IRawMaterialModel) => item.name,
        },
        { name: "quantity", placeholder: "0", width: "!w-2/12", type: "number" },
        {
            name: "price",
            placeholder: "0",
            width: "!w-2/12",
            type: "number",
            computedValue: (item) => fixedNumber(item.rawMaterial?.price ?? 0).toString(),
            computedDependencies: ["rawMaterial"],
        },
        { name: "discount", placeholder: "0", width: "!w-2/12", type: "number" },
        {
            name: "total",
            placeholder: "0",
            width: "!w-2/12",
            isHideInMd: true,
            type: "fixed",
            computedValue: (item) =>
                fixedNumber(PurchaseUtils.getPurchaseProductData(item, isPriceIncludingTax).total, 3).toString(),
        },
        {
            type: "checkbox",
            name: "isTaxable",
            placeholder: "0",
            width: "!w-2/12",
            label: TranslateConstants.VAT_15,
            defaultValue: true,
        },
    ];
};

export const AdminPurchaseOrderInitialState: IAdminPurchaseOrderState = {
    date: new Date().getTime(),
    dueDate: new Date().getTime(),
    paymentMethod: PaymentsEnum.CASH,
    supplier: undefined,
    isPriceIncludingTax: true,
    purchaseOrderProducts: [],
    note: "",
    nativeTotal: 0,
    productsDiscount: 0,
    subTotal: 0,
    vat: 0,
    total: 0,
};

export const AdminPurchaseOrderDataTableHeaders = [
    TranslateConstants.INVOICE_NUMBER,
    TranslateConstants.SUPPLIER,
    TranslateConstants.TOTAL,
    TranslateConstants.DATE,
];
