import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminPaymentReportVoucherDestinationsConstant, AdminPaymentVouchersReportTableHeaders } from "./AdminPaymentVouchersConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminPaymentVouchersReportTotalInfoFeature from "./features/AdminPaymentVouchersReportTotalInfoFeature";
import { AdminPaymentVouchersReportService } from "./PaymentVouchersReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";

const AdminPaymentVouchersReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: paymentVouchersSum,
        isLoading: paymentVouchersSumLoading,
        isError: paymentVouchersSumError,
        refetch: refetchPaymentVouchersSum,
    } = useFetch(
        "report-" + EndPointsEnums.PAYMENT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
            destination?: string
        ) => PaymentVouchersApiRepo.getSum<IPaymentVoucherSumModel>(startTime, endTime, destination),
        { autoFetchOnMount: true }
    );

    const {
        data: paymentVouchers,
        isLoading: paymentVouchersLoading,
        isError: paymentVouchersError,
        refetch: refetchPaymentVouchers,
    } = useFetch(
        "report-" + EndPointsEnums.PAYMENT_VOUCHERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber(),
            destination?: string
        ) => PaymentVouchersApiRepo.getPaymentVouchers({ startTime, endTime, destination }),
        { autoFetchOnMount: true }
    );

    const onDate = (
        startDate: Date,
        endDate: Date,
        destination?: { name: string; value: string }
    ) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setDateRange({ startTime: start, endTime: end });
        refetchPaymentVouchersSum(start, end, destination?.value);
        refetchPaymentVouchers(start, end, destination?.value);
    };

    const onDownload = () =>
        AdminPaymentVouchersReportService.handleDownload(
            dateRange,
            paymentVouchers,
            paymentVouchersSum
        );

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!paymentVouchers?.length || !paymentVouchersSum}
                showSearch={true}
                items={AdminPaymentReportVoucherDestinationsConstant}
                isSearchable={false}
                titleSelector={(item) => TranslateHelper.t(item.name)}
                defaultValue={TranslateHelper.t(AdminPaymentReportVoucherDestinationsConstant[0].name)}
            />
            <AdminPaymentVouchersReportTotalInfoFeature
                paymentVoucherSum={paymentVouchersSum}
            />
            <StatusComponent
                isEmpty={!paymentVouchers?.length}
                isLoading={paymentVouchersSumLoading || paymentVouchersLoading}
                isError={paymentVouchersSumError || paymentVouchersError}
                height={isXs ? 17.5 : isSm ? 15 : 15.6}
            >
                <TableComponent
                    headers={AdminPaymentVouchersReportTableHeaders}
                    items={paymentVouchers || []}
                    selectors={(item: IPaymentVoucherModel) => [
                        DateUtils.format(item.date),
                        item.number,
                        TranslateHelper.t(item.destination),
                        item.cash,
                        item.network,
                        item.note,
                    ]}
                    showPrintButton={true}
                    onPrint={AdminPaymentVouchersReportService.handleOnPrint}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminPaymentVouchersReportPage;
