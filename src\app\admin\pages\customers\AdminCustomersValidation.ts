import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminCustomerInputs } from "./AdminCustomersInterface";

export class AdminCustomersValidation {
    static inputsValidation = (values: IAdminCustomerInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        } else if (!values.mobile) {
            isValid = false;
            ToastHelper.error(TranslateConstants.MOBILE_FIELD_IS_REQUIRED);
        } else if (values.mobile.length > 20) {
            ToastHelper.error(TranslateConstants.MOBILE_SIZE_ERROR)
            isValid = false;
        } else if (!!values.taxNumber && values.taxNumber.length > 15) {
            ToastHelper.error(TranslateConstants.TAX_NUMBER_SIZE_ERROR)
            isValid = false;
        } else if (!!values.address && values.address.length > 100) {
            ToastHelper.error(TranslateConstants.ADDRESS_SIZE_ERROR)
            isValid = false;
        }

        return isValid;
    };
}
