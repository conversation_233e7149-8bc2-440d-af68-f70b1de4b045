interface IReturnedPurchasesReportPdfPrinterItemModel {
    invoiceNumber: string;
    supplierName: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    date: string;
}

export interface IReturnedPurchasesReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IReturnedPurchasesReportPdfPrinterItemModel[];
    totals: {
        count: number;
        subTotal: number;
        vat: number;
        total: number;
    };
}