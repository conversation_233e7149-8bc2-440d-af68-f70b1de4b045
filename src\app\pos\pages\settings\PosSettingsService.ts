import { Dispatch, SetStateAction } from "react";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IPrinterModel } from "../../../../common/models/PrinterModel";
import { PrintersHelper } from "../../../../common/helpers/PrintersHelper";
import { IPrinterData } from "../../../../common/interfaces";
import { ICategoryModel } from "../../../../common/models/CategoryModel";
import { PosReceiptPrinterConstant } from "./PosSettingsConstants";

export class PosSettingsService {
    static onSave(
        data: IPrinterData[],
        setDisabled: Dispatch<SetStateAction<boolean>>
    ) {
        PrintersHelper.setPrinters(data);
        ToastHelper.success();
        setDisabled(true);
    }

    static handleOnPrinterSelect(
        setData: Dispatch<SetStateAction<IPrinterData[]>>,
        index: number,
        value: IPrinterModel
    ) {
        setData((prev) => {
            return prev.map((item, i) => {
                if (i === index) return { ...item, printer: value.deviceId };
                return item;
            });
        });
    }

    static async initPrintersData(
        setData: Dispatch<SetStateAction<IPrinterData[]>>,
        categories: ICategoryModel[]
    ) {
        const savedData = PrintersHelper.getPrinters();
        const receiptPrinterData = this.handleReceiptPrinterInit(savedData);
        const printerData = this.handlePrintersCategoryInit(savedData, categories);

        setData([receiptPrinterData, ...printerData]);
    }

    private static handleReceiptPrinterInit(
        savedData: IPrinterData[]
    ): IPrinterData {
        const receiptId = PosReceiptPrinterConstant.categoryId;
        const receiptPrinter = savedData.find((el) => el.categoryId === receiptId);

        return {
            ...PosReceiptPrinterConstant,
            printer: receiptPrinter?.printer,
        };
    }

    private static handlePrintersCategoryInit(
        savedData: IPrinterData[],
        categories: ICategoryModel[]
    ): IPrinterData[] {
        return categories.map((item) => {
            const savedItem = savedData.find((el) => el.categoryId === `${item.id}`);

            return {
                categoryId: item.id.toString(),
                name: item.name,
                secondName: item.secondName,
                printer: savedItem ? savedItem.printer : undefined,
            };
        });
    }
}
