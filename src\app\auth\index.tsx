import logo from "/logo.svg";
import { useState } from "react";
import AuthLoginFormFeature from "./features/AuthLoginFormFeature";
import AuthButtonsFeature from "./features/AuthButtonsFeature";
import { IAuthState } from "./AuthInterface";
import { AuthInitialState } from "./AuthConstants";
import AuthRegisterFormFeature from "./features/AuthRegisterFormFeature";
import { useTranslate } from "../../common/hooks/useTranslate";
import GuardedRouteComponent from "../../common/components/GuardedRouteComponent";
import { TranslateConstants } from "../../common/constants/TranslateConstants";
import RandomBackGroundImageComponent from "../../common/components/RandomBackGroundImageComponent";
import OnlineStatusGuardComponent from "../../common/components/OnlineStatusGuardComponent";

const AuthPage = () => {
    const { translate } = useTranslate();
    const [state, setState] = useState<IAuthState>(AuthInitialState);

    return (
        <OnlineStatusGuardComponent>
            <GuardedRouteComponent notAuthGuard={true}>
                <RandomBackGroundImageComponent>
                    <div className="flex justify-center items-center h-full w-full">
                        <AuthButtonsFeature
                            view={state.view}
                            changeView={(view) => setState({ ...state, view })}
                        />
                        <div className="flex flex-col gap-5 items-center justify-center p-5 w-full sm:w-3/5 md:w-2/5 lg:w-1/4 relative">
                            <div className="flex flex-col gap-5 items-center">
                                <div className=" rounded-full p-3 animate-h_spin">
                                    <img src={logo} alt="logo" className="w-20" />
                                </div>
                                <h1 className="text-lg font-tajawal-bold text-white">
                                    {translate(TranslateConstants.APP_NAME)}
                                </h1>
                            </div>
                            {state.view === "register" ? (
                                <AuthRegisterFormFeature />
                            ) : (
                                <AuthLoginFormFeature />
                            )}
                        </div>
                    </div>
                </RandomBackGroundImageComponent>
            </GuardedRouteComponent>
        </OnlineStatusGuardComponent>
    );
};

export default AuthPage;
