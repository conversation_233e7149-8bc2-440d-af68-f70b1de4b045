import { useMemo, useRef, useState } from "react";
import ButtonComponent from "../../../common/components/ButtonComponent";
import ReportDatesPickerComponent from "../../../common/components/ReportDatesPickerComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import DropDownSearchComponent from "../../../common/components/DropDownSearchComponent";
import { useTranslate } from "../../../common/hooks/useTranslate";

interface IProps<T> {
    onDate?: (startDate: Date, endDate: Date, selectedItem?: T) => void;
    onDownload?: () => void;
    isDownloadDisabled?: boolean;
    // onView?: () => void;
    onSearch?: (value: string) => void;
    showSearch?: boolean;
    isSearchLoading?: boolean;
    isSearchError?: boolean;
    items?: T[];
    placeHolder?: string;
    titleSelector?: (item: T) => any;
    subtitleSelector?: (item: T) => any;
    defaultValue?: string;
    defaultItem?: T;
    isSearchable?: boolean;
    disableSearchButtonByDefault?: boolean;
    buttonText?: string;
    buttonIcon?: React.ReactNode;
}

function AdminReportControllerComponent<T>({
    onDate,
    onDownload,
    isDownloadDisabled = true,
    // onView,
    onSearch,
    showSearch = false,
    isSearchLoading = false,
    isSearchError = false,
    items = [],
    placeHolder,
    titleSelector,
    subtitleSelector,
    defaultValue,
    defaultItem,
    isSearchable = true,
    disableSearchButtonByDefault = true,
    buttonText = TranslateConstants.DOWNLOAD_PDF,
    buttonIcon,
}: IProps<T>) {
    const { isRtl } = useTranslate();
    const [selectedItem, setSelectedItem] = useState(undefined);
    const selectedItemRef = useRef(selectedItem);
    const isSelectedItemChanged = selectedItem !== selectedItemRef.current;

    const hasButtons = useMemo(() => {
        return !!onDownload;
    }, [onDownload]);

    const handleOnDate = (startDate: Date, endDate: Date) => {
        selectedItemRef.current = selectedItem;
        onDate?.(startDate, endDate, selectedItem || defaultItem);
    };

    return (
        <div
            className={
                "flex gap-2" +
                " " +
                (hasButtons ? "justify-between" : "justify-end") +
                " " +
                (showSearch ? "flex-col-reverse" : "flex-col-reverse sm:flex-row")
            }
        >
            {
                hasButtons && (
                    <div className="w-full sm:w-1/5 flex gap-2 sm:gap-0">
                        {/* <div className="w-full sm:w-2/5 flex gap-2 sm:gap-0"> */}
                        {
                            onDownload && (
                                <ButtonComponent
                                    text={buttonText}
                                    onClick={onDownload}
                                    isDisabled={isDownloadDisabled}
                                    className={isRtl ? "sm:ml-2" : "sm:mr-2"}
                                    iconComponent={buttonIcon}
                                />
                            )
                        }
                        {/* <ButtonComponent
                    text={TranslateConstants.VIEW}
                    onClick={onView}
                    isDisabled={true}
                    className={isRtl ? "sm:ml-1" : "sm:mr-1"}
                /> */}
                    </div>
                )
            }
            <div
                className={
                    "flex justify-between" +
                    " " +
                    (showSearch ? "w-full gap-2 flex-col sm:flex-row" : "")
                }
            >
                {showSearch && (
                    <DropDownSearchComponent
                        containerClassName="w-full sm:!w-[39.7%]"
                        onSelect={(item) => setSelectedItem(item)}
                        isSearchable={isSearchable}
                        isOutFilter={true}
                        showSearchIcon={true}
                        showViewButton={false}
                        placeholder={placeHolder || TranslateConstants.SEARCH}
                        onChange={onSearch}
                        items={items}
                        titleSelector={titleSelector}
                        subtitleSelector={subtitleSelector}
                        isLoading={isSearchLoading}
                        isError={isSearchError}
                        defaultValue={defaultValue}
                    />
                )}
                <ReportDatesPickerComponent
                    onClick={handleOnDate}
                    disableButton={!isSelectedItemChanged}
                    disableSearchButtonByDefault={disableSearchButtonByDefault}
                />
            </div>
        </div>
    );
}

export default AdminReportControllerComponent;
