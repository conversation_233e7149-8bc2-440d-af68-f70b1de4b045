import { Content } from "pdfmake/interfaces";
import { ITaxReportPdfPrinterModel } from "./TaxReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { TranslateHelper } from "../../../helpers/TranslateHelper";
import { TranslateConstants } from "../../../constants/TranslateConstants";

export const TaxReportPdfPrinterContent = (model: ITaxReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "تقرير الضرائب", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        { text: "المبيعات", style: { fontSize: 14, bold: true }, marginTop: 20 },
        {
            style: {
                fontSize: 14,
                bold: true,
                alignment: "center",
            },
            table: {
                headerRows: 1,
                widths: "*",
                body: [
                    [
                        { text: model.data.ordersSubTotal },
                        { text: TranslateHelper.t(TranslateConstants.SALES_SUB_TOTAL) },
                    ],
                    [
                        { text: model.data.returnedOrdersSubTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_RETURNED_SALES) },
                    ],
                    [
                        { text: model.data.ordersNet },
                        { text: TranslateHelper.t(TranslateConstants.SALES_NET) },
                    ],
                    [
                        { text: model.data.orderTax },
                        { text: TranslateHelper.t(TranslateConstants.SALES_TAX) },
                    ],
                ],
            },
        },
        { text: "المشتريات", style: { fontSize: 14, bold: true }, marginTop: 20 },
        {
            style: {
                fontSize: 14,
                bold: true,
                alignment: "center",
            },
            table: {
                headerRows: 1,
                widths: "*",
                body: [
                    [
                        { text: model.data.purchaseInvoicesSubTotal },
                        { text: TranslateHelper.t(TranslateConstants.PURCHASES_SUB_TOTAL) },
                    ],
                    [
                        { text: model.data.returnedPurchaseInvoicesSubTotal },
                        { text: TranslateHelper.t(TranslateConstants.TOTAL_RETURNED_PURCHASE_INVOICES) },
                    ],
                    [
                        { text: model.data.purchaseInvoicesNet },
                        { text: TranslateHelper.t(TranslateConstants.PURCHASES_NET) },
                    ],
                    [
                        { text: model.data.purchaseInvoicesTax },
                        { text: TranslateHelper.t(TranslateConstants.PURCHASES_TAX) },
                    ],
                ],
            },
        },
        { text: "مستحق الضريبة", style: { fontSize: 14, bold: true }, marginTop: 20 },
        {
            style: {
                fontSize: 14,
                bold: true,
                alignment: "center",
            },
            table: {
                headerRows: 1,
                widths: "*",
                body: [
                    [
                        { text: model.data.orderTax - model.data.purchaseInvoicesTax },
                        { text: TranslateHelper.t(TranslateConstants.TAX_NET) },
                    ],
                ],
            },
        },
    ];
}
