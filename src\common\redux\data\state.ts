import { IModalProps } from "../../components/ModalComponent";
import IState from "./interface";

export const initialModalState: IModalProps = {
    show: false,
    component: null,
    title: undefined,
    size: 'md',
    submitButton: undefined,
    showButtons: true,
    onClose: undefined,
    closeOnBlur: false,
    closButtonClassName: ''
}

const InitialState: IState = {
    modal: initialModalState,
    loading: false,
}

export default InitialState;