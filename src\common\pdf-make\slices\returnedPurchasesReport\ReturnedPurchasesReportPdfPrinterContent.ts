
import { Content } from "pdfmake/interfaces";
import { IReturnedPurchasesReportPdfPrinterModel } from "./ReturnedPurchasesReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";

export const ReturnedPurchasesReportPdfPrinterContent = (model: IReturnedPurchasesReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "تقرير مرتجع المشتريات", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: [
                    "*",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "*",
                    "auto",
                ],
                body: [
                    [
                        { text: "التاريخ", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "الخصم", style: "tableHeader" },
                        { text: "الإجمالي الفرعي", style: "tableHeader" },
                        { text: "المورد", style: "tableHeader" },
                        { text: "الفاتورة", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.date,
                        el.vat.toFixed(2),
                        el.total.toFixed(2),
                        el.discount.toFixed(2),
                        el.subTotal.toFixed(2),
                        el.supplierName ?? "",
                        el.invoiceNumber,
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            stack: [
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.count.toString(), margin: [0, 3, 0, 0] },
                                        { text: "عدد المرتجعات:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.subTotal.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "الإجمالي الفرعي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.vat.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "الضريبة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.total.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "الإجمالي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
            ],
        }
    ];
}