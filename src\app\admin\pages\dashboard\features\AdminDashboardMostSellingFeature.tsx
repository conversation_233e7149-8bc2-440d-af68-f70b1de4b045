import { FC } from "react";
import TableComponent from "../../../../../common/components/TableComponent";
import TitledCardComponent from "../../../../../common/components/TitledCardComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import EmptyComponent from "../../../../../common/components/EmptyComponent";
import { IOrderProductSumModel } from "../../../../../common/models/OrderProductSumModel";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    mostSellingProducts?: IOrderProductSumModel[];
}

const AdminDashboardMostSellingFeature: FC<IProps> = ({
    mostSellingProducts,
}) => {
    return (
        <TitledCardComponent title={TranslateConstants.MOST_SELLING}>
            <div className="flex justify-center items-start h-full w-full min-h-52">
                {!mostSellingProducts?.length && <EmptyComponent />}
                {!!mostSellingProducts?.length && (
                    <TableComponent
                        isCentered={true}
                        tableClassName="!w-full"
                        headers={[
                            TranslateConstants.NAME,
                            TranslateConstants.PRICE,
                            TranslateConstants.QUANTITY,
                            TranslateConstants.TOTAL,
                        ]}
                        items={mostSellingProducts || []}
                        selectors={(e: IOrderProductSumModel) => [
                            e.name,
                            e.price,
                            e.quantity,
                            fixedNumber(e.total),
                        ]}
                    />
                )}
            </div>
        </TitledCardComponent>
    );
};

export default AdminDashboardMostSellingFeature;
