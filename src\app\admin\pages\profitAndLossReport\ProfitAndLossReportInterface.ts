import { IPaymentVoucherDestinationSumModel } from "../../../../common/models/PaymentVoucherSumModel";

export interface IProfitAndLossReportTotalInfoInterface {
    ordersTotal: number;
    returnedOrdersTotal: number;
    purchaseInvoicesTotal: number;
    returnedPurchaseInvoicesTotal: number;
    paymentVouchersTotal: number;
    paymentVouchersSum?: IPaymentVoucherDestinationSumModel[];
    salesNet: number;
    purchasesNet: number;
    totalNet: number;
}