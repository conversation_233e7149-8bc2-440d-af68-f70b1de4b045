import ButtonComponent from "../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../common/hooks/useTranslate";
import { IAutView } from "../AuthInterface";
import { FC } from "react";
import { LanguageButtonComponent } from "../../../common/components/LanguageComponent";

interface IProps {
    view: IAutView;
    changeView: (view: IAutView) => void;
}

const AuthButtonsFeature: FC<IProps> = ({ view, changeView }) => {
    const { translate } = useTranslate();

    const handleOnRegisterClick = () => {
        changeView(view === "login" ? "register" : "login");
    };

    return (
        <div className="absolute top-5 sm:top-10 w-full flex justify-between">
            <div className="flex gap-2 mx-5 w-full">
                <LanguageButtonComponent />
                <ButtonComponent
                    text={
                        view === "login"
                            ? translate(TranslateConstants.NEW_REGISTER)
                            : translate(TranslateConstants.LOGIN)
                    }
                    onClick={handleOnRegisterClick}
                    className="!w-36"
                />
            </div>
        </div>
    );
};

export default AuthButtonsFeature;
