import { <PERSON><PERSON><PERSON>, FC, SetStateAction } from "react";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import useFetch from "../../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import { AdminReturnedPurchaseInvoiceDataTableHeaders } from "../AdminReturnedPurchaseInvoiceConstants";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { AdminReturnedPurchaseInvoiceService } from "../AdminReturnedPurchaseInvoiceService";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { IReturnedPurchaseInvoiceModel } from "../../../../../common/models/ReturnedPurchaseInvoiceModel";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
}

const AdminReturnedPurchaseInvoiceViewFeature: FC<IProps> = ({ setIsView }) => {
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.RETURNED_PURCHASE_INVOICES,
        () => {
            return ReturnedPurchaseInvoiceApiRepo.getReturnedPurchaseInvoices({
                getPurchaseInvoice: false,
                getSuppliers: true,
            });
        }
    );

    const handleOnPrint = (item: IReturnedPurchaseInvoiceModel) =>
        AdminReturnedPurchaseInvoiceService.handlePreview(item);

    return (
        <>
            <AddAndFilterComponent
                onAdd={() => setIsView(false)}
                onReload={refetch}
            />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminReturnedPurchaseInvoiceDataTableHeaders}
                    items={data || []}
                    selectors={(item: IReturnedPurchaseInvoiceModel) => [
                        item.number,
                        item.purchaseInvoiceId,
                        item.supplier?.name,
                        item.total,
                        DateUtils.format(item.date),
                    ]}
                    showEditButton={false}
                    showPrintButton={true}
                    onPrint={handleOnPrint}
                />
            </StatusComponent>
        </>
    );
};

export default AdminReturnedPurchaseInvoiceViewFeature;
