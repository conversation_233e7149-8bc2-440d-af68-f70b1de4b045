import { useNavigate } from "react-router-dom";
import useFetch from "../../../../../common/asyncController/useFetch";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { CachedKeysEnums } from "../../../../../common/enums/CachedKeysEnums";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import usePosActions from "../../../redux/usePosActions";
import { PosPausedOrdersRepo } from "../../../repo/PosPausedOrdersRepo";
import { PosPausedOrdersTableHeaderConstants } from "../PosInvoicesConstants";
import { PosTablesService } from "../../tables/PosTablesService";
import { IPosPausedOrderModel } from "../../../../../common/models/PosOrderModel";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { PosTablesRepo } from "../../../repo/PosTablesRepo";
import { OrderTypeEnum, TableStatusEnum } from "../../../../../common/enums/DataEnums";
import { ToastHelper } from "../../../../../common/helpers/ToastHelper";

const PosPausedOrdersFeature = () => {
    const posActions = usePosActions();
    const navigate = useNavigate();
    const {
        data,
        isLoading,
        isError,
        refetch,
    } = useFetch(
        CachedKeysEnums.POS_PAUSED_ORDERS,
        PosPausedOrdersRepo.getPausedOrders,
        { autoFetchOnMount: true }
    );

    const handleOnView = async (order: IPosPausedOrderModel) => {
        if (order.type === OrderTypeEnum.DINE_IN && order.table) {
            const table = await PosTablesRepo.getOneTable(order.table.id);
            if (table?.order || table?.status === TableStatusEnum.OCCUPIED) {
                ToastHelper.error(TranslateConstants.ERROR_TABLE_IS_OCCUPIED);
                return;
            }
        }
        PosTablesService.handleViewTable(undefined, order, posActions, navigate);
    }

    const handleOnDelete = (order: IPosPausedOrderModel) => {
        PosPausedOrdersRepo.deletePausedOrder(order);
        refetch();
    }

    return (
        <StatusComponent
            isLoading={isLoading}
            isError={isError}
            isEmpty={!data || data.length === 0}
            height={13}
        >
            <TableComponent
                headers={PosPausedOrdersTableHeaderConstants}
                items={data || []}
                showNumbering={true}
                reverseNumbering={true}
                selectors={(item: IPosPausedOrderModel) => {
                    return [
                        item.customer?.name,
                        item.customer?.mobile,
                        item.total.toFixed(2),
                        item.startTime ? DateUtils.format(item.startTime) : "-",
                        <span className={item.isConverted ? "text-green-600" : ""}>
                            {TranslateHelper.t(item.isConverted ? TranslateConstants.CONVERTED : TranslateConstants.PENDING)}
                        </span>,
                    ]
                }}
                showDeleteButton={(item: IPosPausedOrderModel) => !item.isConverted}
                onDelete={handleOnDelete}
                customButtons={[
                    {
                        text: TranslateConstants.VIEW,
                        onClick: handleOnView,
                        showButton: (item: IPosPausedOrderModel) => !item.isConverted,
                    },
                ]}
            />
        </StatusComponent>
    );
}

export default PosPausedOrdersFeature;
