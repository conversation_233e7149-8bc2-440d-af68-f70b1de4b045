export const LocalDbDeleteByIdAction = (
    db: IDBDatabase | undefined,
    collection: string,
    id: string | number
): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readwrite");
        const store = transaction?.objectStore(collection);
        const request = store?.delete(id);

        if (request) {
            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

export const LocalDbDeleteAllAction = (
    db: IDBDatabase | undefined,
    collection: string
): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readwrite");
        const store = transaction?.objectStore(collection);
        const request = store?.clear();

        if (request) {
            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

export const LocalDbDeleteDbAction = (dbName: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.deleteDatabase(dbName);

        request.onsuccess = () => {
            resolve(true);
        };

        request.onerror = (e: any) => {
            reject(e.target.error);
        };
    });
};

export const LocalDbClearAction = (
    db: IDBDatabase | undefined
): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        const transaction = db.transaction(db.objectStoreNames, "readwrite");
        transaction.oncomplete = () => {
            resolve(true);
        };

        transaction.onerror = (e: any) => {
            reject(e.target.error);
        };

        for (const storeName of db.objectStoreNames) {
            const store = transaction.objectStore(storeName);
            store.clear();
        }
    });
};
