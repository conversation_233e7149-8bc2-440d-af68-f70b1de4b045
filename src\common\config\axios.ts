import axios from "axios";
import { accessTokenKeyConstant } from "../constants/ConfigConstants";
import { debug } from "../utils/CommonUtils";
import { CookiesHelper } from "../helpers/CookiesHelper";
import { AuthService } from "../../app/auth/AuthService";
import { IResponse } from "../interfaces";

export const getDomain = () => {
    if (process.env.NODE_ENV === "production") {
        return "https://restaurant-app-server-8rcw.onrender.com/api/v1/";
    } else {
        const origin = window.location.origin;
        const path = origin.split(":")[0] + ":" + origin.split(":")[1];
        return path + ":3000/api/v1/";
        // return "https://3b87-156-201-73-137.ngrok-free.app/api/v1/";
        // return "https://restaurant-app-server-8rcw.onrender.com/api/v1/";
    }
}

const api = axios.create({
    baseURL: getDomain(),
    timeout: 10000,
    timeoutErrorMessage: "Request Timeout",
});

api.interceptors.request.use(
    (config) => {
        const token = CookiesHelper.get(accessTokenKeyConstant);
        if (token) {
            config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        if (error.response.data.statusCode === 401) {
            AuthService.logout();
        } else if (
            error.response.data.statusCode === 400 &&
            error.response.data.error === "Bad Request"
        ) {
            let message: string;

            if (typeof error.response.data.message === "string") {
                message = error.response.data.message;
            } else {
                message = error.response.data.message[0];
            }

            const refactorError: IResponse<never> = {
                statusCode: 400,
                message: message.split(" ").join("_").toUpperCase().replace(".", ""),
                success: false,
            };

            debug(refactorError);
            return Promise.resolve({ data: refactorError });
        }

        if (error?.response?.data) {
            debug(error.response.data);
            return Promise.resolve(error.response);
        }

        return Promise.reject(error);
    }
);

export default api;
