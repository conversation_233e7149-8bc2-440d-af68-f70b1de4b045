export interface IShiftBody {
    shiftId: number;
    startTime: number;
    endTime: number;
    startAmount: number;
    endAmount: number;
    ordersCount: number;
    returnedOrdersCount: number;
    totalAmount: number;
    totalNetAmount: number;
    totalReturnedAmount: number;
    discountAmount: number;
    tobaccoTaxAmount: number;
    vatAmount: number;
    additionAmount: number;
    shortageAmount: number;
    cashAmount: number;
    networkAmount: number;
    deferredAmount: number;
    addedToShiftAmount: number;
    customerReceiptsAmount: number;
    pullsAmount: number;
    cashNetAmount: number;
    networkNetAmount: number;
    deliveryAppsAmount: number;
}