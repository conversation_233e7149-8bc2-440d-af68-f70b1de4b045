import { DeliveryAppEnum } from "../enums/DataEnums";
import { HttpStatus } from "../enums/HttpStatusEnums";
import { IKitchenPdfPrinterItemModel } from "../pdf-make/slices/kitchen/KitchenPdfPrinterModel";

export type IDataOperations = "add" | "remove" | "update" | "set";

export interface IResponse<T> {
    statusCode?: HttpStatus;
    message?: string;
    success?: boolean;
    access_token?: string;
    data?: T;
    error?: string;
    total?: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}

export interface IPrinterData {
    name: string;
    secondName?: string;
    categoryId: string;
    printer?: string;
}

export interface IKitchenPrinter {
    printerId: string;
    products: IKitchenPdfPrinterItemModel[];
}

export interface IDeliveryApp {
    name: DeliveryAppEnum;
    image: string;
}

export interface IPosSmallScreenSettings {
    port: number;
    comPort: string;
    isActive: boolean;
}

export interface IPrintingSettings {
    isInvoicePrintingActive: boolean;
    isKitchenPrintingActive: boolean;
    isLogoPrintingActive: boolean;
    isShiftPrintingActive: boolean;
    isVouchersPrintingActive: boolean;
}