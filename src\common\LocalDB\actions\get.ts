import { ILocalDBGetOptionsProperties, IOperator } from "../LocalDBInterface";
import { LocalDbCheckConditionUtil } from "../LocalDBUtils";
import {
    LocalDbGetAllService,
    LocalDbGetAllWithJoinService,
    LocalDbGetAllWithOptionsService,
} from "../services/get";

export const LocalDbGetOneByIdAction = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    id: string | number
): Promise<T | undefined> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readonly");
        const store = transaction?.objectStore(collection);
        const request = store?.get(id);

        if (request) {
            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

export const LocalDbGetOneAction = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    [key, operator, value]: [string, IOperator, any]
): Promise<T | undefined> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readonly");
        const store = transaction?.objectStore(collection);
        const request = store?.openCursor();

        if (request) {
            request.onsuccess = (e: any) => {
                const cursor: IDBCursorWithValue | null = e.target.result;
                if (cursor) {
                    const result = cursor.value;

                    if (LocalDbCheckConditionUtil(result[key], operator, value))
                        resolve(result);
                    else cursor.continue();
                } else {
                    resolve(undefined);
                }
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

export const LocalDbGetAction = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    options?: ILocalDBGetOptionsProperties<T>
): Promise<T[]> => {
    return new Promise(async (resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        if (!options || Object.keys(options).length === 0) {
            resolve(await LocalDbGetAllService(db, collection));
            return;
        }

        const {
            where,
            orderBy,
            skip,
            skipUntil,
            limit,
            join,
            batchSize,
            index,
            whereKey: key,
        } = options;

        if (!join) {
            resolve(
                await LocalDbGetAllWithOptionsService(
                    db,
                    collection,
                    limit,
                    skip,
                    skipUntil,
                    orderBy,
                    where,
                    batchSize,
                    index,
                    key
                )
            );
            return;
        }

        resolve(
            await LocalDbGetAllWithJoinService(
                db,
                collection,
                join,
                limit,
                skip,
                skipUntil,
                orderBy,
                where,
                batchSize,
                index,
                key
            )
        );
    });
};
