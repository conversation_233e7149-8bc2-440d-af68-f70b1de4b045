import { IAdminPaymentVoucherInputs } from "../../../app/admin/pages/paymentVoucher/AdminPaymentVoucherInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IPaymentVoucherBody } from "../../interfaces/body/PaymentVoucherBody";
import { IPaymentVoucherModel } from "../../models/PaymentVoucherModel";
import { IPaymentVoucherDestinationSumModel, IPaymentVoucherSumModel } from "../../models/PaymentVoucherSumModel";
import { debug } from "../../utils/CommonUtils";

export class PaymentVouchersApiRepo {
    static async getPaymentVouchers(options?: {
        startTime?: number;
        endTime?: number;
        destination?: string;
        isCash?: boolean;
        isBank?: boolean;
    }) {
        try {
            const res = await AxiosHelper.get<IPaymentVoucherModel[]>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                {
                    params: {
                        startTime: options?.startTime,
                        endTime: options?.endTime,
                        destination: options?.destination,
                        isCash: options?.isCash,
                        isBank: options?.isBank,
                    },
                }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [getPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    static async addPaymentVoucher(body: IAdminPaymentVoucherInputs) {
        try {
            const formattedBody: IPaymentVoucherBody = body;
            const res = await AxiosHelper.post<IPaymentVoucherModel>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [addPaymentVoucher] Error: `, error);
            throw error;
        }
    }

    static async updatePaymentVoucher(
        id: number,
        body: IAdminPaymentVoucherInputs
    ) {
        try {
            const formattedBody: IPaymentVoucherBody = body;
            const res = await AxiosHelper.patch<IPaymentVoucherModel>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                id,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [updatePaymentVoucher] Error: `, error);
            throw error;
        }
    }

    static async getSum<T extends IPaymentVoucherSumModel | IPaymentVoucherDestinationSumModel[]>(
        startTime: number,
        endTime: number,
        destination?: string,
        isCash?: boolean,
        isBank?: boolean,
        isGetDestinations?: boolean,
    ): Promise<T> {
        try {
            const res = await AxiosHelper.get(
                EndPointsEnums.PAYMENT_VOUCHERS_SUM,
                { params: { startTime, endTime, destination, isCash, isBank, isGetDestinations } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data as any;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}
