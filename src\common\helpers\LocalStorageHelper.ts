export class LocalStorageHelper {
    static set = (key: string, value: any) => {
        localStorage.setItem(key, JSON.stringify(value));
    }

    static get = (key: string) => {
        const item = localStorage.getItem(key);
        if (item) return JSON.parse(item);
        return null;
    }

    static remove = (key: string) => {
        localStorage.removeItem(key);
    }

    static removeMultiple = (keys: string[]) => {
        keys.forEach(key => {
            localStorage.removeItem(key);
        });
    }

    static clear = () => {
        localStorage.clear();
    }
}