import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";

export interface IAdminDeliveryAppInputs {
    name: string;
    percentage: number;
}

export type TAdminDeliveryAppsState = "view" | "products";

export interface IAdminDeliveryAppsProductItemProps {
    image: string;
    name: string;
    price: number;
    priceInDeliveryApp?: number;
    productSizeType: ProductSizeTypeEnum;
    productId?: number;
    sizeId?: number;
    active: boolean;
}

export interface IAdminDeliveryAppsAdditionItemProps { 
    name: string;
    price: number;
    priceInDeliveryApp?: number;
    additionId?: number;
    active: boolean;
}