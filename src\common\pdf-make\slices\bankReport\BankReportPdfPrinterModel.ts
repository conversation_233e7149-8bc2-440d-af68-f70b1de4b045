export interface IBankReportPdfPrinterItemModel {
    date: string;
    number: string | number;
    type: string;
    bank: number;
}

export interface IBankReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IBankReportPdfPrinterItemModel[];
    totals: {
        ordersCount: number;
        returnedCount: number;
        paymentVoucherCount: number;
        receiptVoucherCount: number;
        total: number;
    };
}