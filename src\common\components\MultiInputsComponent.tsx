import { useEffect, useMemo, useState } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";
import InputComponent, { InputType } from "./InputComponent";
import DividerComponent from "./DividerComponent";
import ButtonComponent from "./ButtonComponent";
import { TranslateConstants } from "../constants/TranslateConstants";
import { LuTrash2 } from "react-icons/lu";
import IconButtonComponent from "./IconButtonComponent";
import { useTranslate } from "../hooks/useTranslate";
import useScreenSize from "../hooks/useScreenSize";
import DropDownSearchComponent from "./DropDownSearchComponent";
import CheckBoxComponent from "./CheckBoxComponent";

interface IBaseInput<T = any> {
    name: keyof T;
    placeholder?: string;
    className?: string;
    width?: string;
    isHideInSm?: boolean;
    isHideInMd?: boolean;
    isDisabled?: boolean;
    computedValue?: (value: T) => string | boolean;
    computedDependencies?: Array<keyof T>;
    defaultValue?: string | boolean;
    validation?: (value: string, item: T, originalItem: T) => boolean | undefined;
}

interface IDropDownInput<T = any> extends IBaseInput<T> {
    type?: "dropDown";
    titleSelector?: (item: any) => string;
    subtitleSelector?: (item: any) => string;
    isLoading?: boolean;
    isError?: boolean;
    items?: any[];
    selector?: (item: any) => any;
    resultSelector?: (item: any) => any;
    label?: never;
}

interface ICheckBoxInput<T = any> extends IBaseInput<T> {
    type?: "checkbox";
    titleSelector?: never;
    subtitleSelector?: never;
    isLoading?: never;
    isError?: never;
    items?: never;
    selector?: never;
    resultSelector?: never;
    placeholder?: never;
    label?: string;
}

interface INonDropDownInput<T = any> extends IBaseInput<T> {
    type?: InputType;
    titleSelector?: never;
    subtitleSelector?: never;
    items?: never;
    isLoading?: never;
    isError?: never;
    selector?: never;
    resultSelector?: never;
    label?: string;
}

export type IMultiInputsItem<T = any> =
    | IDropDownInput<T>
    | INonDropDownInput<T>
    | ICheckBoxInput<T>;

export type IDefaultItem<T> = IMultiInputsItem<T> & {
    value: string | number | boolean;
    item?: T;
};

interface IProps<T> {
    headers?: string[];
    inputs?: IMultiInputsItem<T>[];
    maxItems?: number;
    buttonText?: string;
    onResult?: (value: T[]) => void;
    defaultValues?: T[];
    values?: T[];
    showMobileView?: boolean;
    showMobileViewOnSm?: boolean;
    suffixComponent?: React.ReactNode;
    productButtonClassName?: string;
    resetInputs?: boolean;
    showDeleteButton?: boolean;
    showAddButton?: boolean;
}

function MultiInputsComponent<T>({
    headers,
    inputs = [],
    maxItems = 10,
    buttonText,
    onResult,
    defaultValues = [],
    values = [],
    showMobileView = false,
    showMobileViewOnSm = false,
    suffixComponent,
    productButtonClassName = "",
    resetInputs = undefined,
    showDeleteButton = true,
    showAddButton = true,
}: IProps<T>) {
    const defaultItem: IDefaultItem<T>[] = inputs.map((item) => ({
        ...item,
        value: item.defaultValue ?? "",
    }));
    const [state, setState] = useState<IDefaultItem<T>[][]>([defaultItem]);
    const { isArabic } = useTranslate();
    const { isXs } = useScreenSize();

    const mobileView = useMemo(
        () => showMobileView || (showMobileViewOnSm && isXs),
        [showMobileViewOnSm, isXs, showMobileView]
    );
    const hasComputedValue = useMemo(
        () => inputs.some((item) => item.computedValue),
        [inputs]
    );

    const formateItem = (item: IDefaultItem<T>[]): T => {
        const obj: any = {};
        item.forEach((input) => {
            obj[input.name] = input.value;
            if (input.item) obj.item = input.item;
        });
        return obj as T;
    };

    const handleOnResult = (res: IDefaultItem<T>[][]) => {
        let result = res.map((item) => formateItem(item));
        const hasResultSelector = inputs.some((item) => item.resultSelector);
        if (hasResultSelector) {
            result = result.map((item: any) => {
                const keys = Object.keys(item as any);
                keys.forEach((key) => {
                    const input = (item as any)[key];
                    const resultSelector = inputs.find(
                        (input) => input.name === key
                    )?.resultSelector;
                    if (resultSelector) {
                        (item as any)[key] = resultSelector(input);
                    }
                });
                return item;
            });
        }
        onResult?.(result);
    };

    const handleValues = (values: T[]) => {
        const newState = values.map((item) => {
            const obj: IDefaultItem<T>[] = inputs.map((input) => {
                const value = item[input.name as keyof T] ?? "";
                return { ...input, value: value as string, item };
            });
            return obj;
        });
        setState(newState);
        handleOnResult(newState);
    };

    useEffect(() => {
        if (defaultValues.length > 0) handleValues(defaultValues);
    }, []);

    const checkDeeplyEqual = (values: T[]) => {
        if (values.length !== state.length) return false;
        return values.every((item, index) => {
            return inputs.every((input, inputIndex) => {
                return item[input.name as keyof T] === state[index][inputIndex].value;
            });
        });
    };

    useEffect(() => {
        if (values?.length > 0 && !checkDeeplyEqual(values)) handleValues(values);
    }, [values]);

    useEffect(() => {
        if (resetInputs) {
            setState([defaultItem]);
            handleOnResult([defaultItem]);
        }
    }, [inputs, resetInputs]);

    const handleComputedValue = (
        state: IDefaultItem<T>[][],
        containerIndex: number,
        hasComputedValue: boolean,
        inputIndex: number
    ) => {
        if (hasComputedValue) {
            state[containerIndex].forEach((item, index) => {
                const updatedObjName = state[containerIndex][inputIndex]
                    .name as keyof T;

                if (
                    item.computedValue &&
                    (!item.computedDependencies ||
                        item.computedDependencies.includes(updatedObjName))
                ) {
                    state[containerIndex][index].value = item.computedValue(
                        formateItem(state[containerIndex])
                    );
                }
            });
        }
        return state;
    };

    const handleOninputChange = (
        value: string | boolean,
        containerIndex: number,
        inputIndex: number,
        validation?:
            | ((value: string, item: T, originalItem: T) => boolean | undefined)
            | undefined
    ) => {
        if (
            validation &&
            !validation(
                value as string,
                formateItem(state[containerIndex]),
                state[containerIndex][inputIndex].item as T
            )
        ) return;

        let newState = [...state];
        const selector = newState[containerIndex][inputIndex].selector;
        newState[containerIndex][inputIndex].value = selector
            ? selector(value)
            : value;
        newState = handleComputedValue(
            newState,
            containerIndex,
            hasComputedValue,
            inputIndex
        );
        setState(newState);
        handleOnResult(newState);
    };

    const handleOnRemove = (index: number) => {
        if (state.length <= 1) return;
        const newState = [...state];
        newState.splice(index, 1);
        setState(newState);
        handleOnResult(newState);
    };

    const handleOnAdd = () => {
        if (state.length >= maxItems) return;
        setState((prev) => [...prev, defaultItem]);
    };

    return (
        <>
            {!!headers && (
                <div
                    className={
                        "w-full gap-2 p-1 bg-base-300 rounded-sm text-black dark:text-white font-bold" +
                        " " +
                        (mobileView ? "hidden" : "flex")
                    }
                >
                    {headers.map((header, index) => (
                        <span
                            className={
                                (inputs[index]?.width ?? "flex-1") +
                                " " +
                                (inputs[index]?.isHideInMd ? "hidden md:flex" : "") +
                                " " +
                                (inputs[index]?.isHideInSm ? "hidden sm:flex" : "")
                            }
                            key={index}
                        >
                            {TranslateHelper.t(header)}
                        </span>
                    ))}
                    {showDeleteButton && (
                        <span className={"w-1/6" + " " + productButtonClassName}></span>
                    )}
                </div>
            )}
            {state?.map((input, containerIndex) => {
                return (
                    <div key={containerIndex}>
                        <div
                            className={
                                "gap-2" +
                                " " +
                                (mobileView
                                    ? "grid grid-cols-2 bg-base-200 border rounded p-2 border-gray-400"
                                    : "w-full flex")
                            }
                        >
                            {input.map((item, inputIndex) => {
                                if (item.type === "checkbox") {
                                    return (
                                        <CheckBoxComponent
                                            key={inputIndex}
                                            containerClassName={
                                                "flex items-center !h-10  gap-2" +
                                                " " +
                                                (!!item.width && !mobileView ? item.width : "flex-1") +
                                                " " +
                                                (item.isHideInMd ? "hidden md:flex" : "") +
                                                " " +
                                                (item.isHideInSm ? "hidden sm:flex" : "") +
                                                " " +
                                                (item.className ?? "")
                                            }
                                            label={item.label}
                                            onChange={(value) => {
                                                handleOninputChange(value, containerIndex, inputIndex);
                                            }}
                                            value={item.value as boolean}
                                            isDisabled={item.isDisabled}
                                        />
                                    );
                                }

                                if (item.type === "dropDown") {
                                    return (
                                        <DropDownSearchComponent
                                            key={`${containerIndex}-${inputIndex}`}
                                            containerClassName={
                                                (!!item.width && !mobileView ? item.width : "flex-1") +
                                                " " +
                                                (item.isHideInMd ? "hidden md:flex" : "") +
                                                " " +
                                                (item.isHideInSm ? "hidden sm:flex" : "") +
                                                " " +
                                                (item.className ?? "")
                                            }
                                            onSelect={(value) => {
                                                handleOninputChange(value, containerIndex, inputIndex);
                                            }}
                                            placeholder={item.placeholder}
                                            label={mobileView ? headers?.[inputIndex] : ""}
                                            titleSelector={item.titleSelector}
                                            subtitleSelector={item.subtitleSelector}
                                            items={item.items}
                                            isLoading={item.isLoading}
                                            isError={item.isError}
                                            defaultValue={item.titleSelector?.(item.value) ?? ""}
                                        />
                                    );
                                }

                                return (
                                    <InputComponent
                                        key={inputIndex}
                                        containerClassName={
                                            (!!item.width && !mobileView ? item.width : "flex-1") +
                                            " " +
                                            (item.isHideInMd ? "hidden md:flex" : "") +
                                            " " +
                                            (item.isHideInSm ? "hidden sm:flex" : "") +
                                            " " +
                                            (item.className ?? "")
                                        }
                                        className={item.className}
                                        onChange={(value) =>
                                            handleOninputChange(
                                                value,
                                                containerIndex,
                                                inputIndex,
                                                item.validation
                                            )
                                        }
                                        placeholder={item.placeholder}
                                        value={`${item.value || ""}`}
                                        label={mobileView ? headers?.[inputIndex] : ""}
                                        isDisabled={item.isDisabled}
                                        type={item.type}
                                    />
                                );
                            })}
                            {showDeleteButton && (
                                <div
                                    className={
                                        (mobileView ? "" : "w-1/6") + " " + productButtonClassName
                                    }
                                >
                                    <IconButtonComponent
                                        icon={<LuTrash2 className="h-8" />}
                                        className={
                                            (mobileView ? "" : "max-w-20") +
                                            " " +
                                            (mobileView
                                                ? "!w-full mx-auto"
                                                : isArabic
                                                    ? "mr-auto"
                                                    : "ml-auto")
                                        }
                                        bgColor="red"
                                        isDisabled={state.length <= 1}
                                        onClick={() => handleOnRemove(containerIndex)}
                                    />
                                </div>
                            )}
                        </div>
                        {!mobileView && <DividerComponent className="mt-2" />}
                    </div>
                );
            })}
            <div
                className={
                    "flex flex-col sm:flex-row gap-2" +
                    " " +
                    (showAddButton ? "justify-between" : "justify-end")
                }
            >
                {showAddButton && (
                    <ButtonComponent
                        text={buttonText || TranslateConstants.ADD}
                        className="!w-full sm:!w-1/4 min-w-28 !h-10"
                        isDisabled={!!state && state.length >= maxItems}
                        onClick={handleOnAdd}
                    />
                )}
                {suffixComponent}
            </div>
        </>
    );
}

export default MultiInputsComponent;
