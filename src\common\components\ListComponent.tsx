import { forwardRef } from "react";

interface IProps {
    children: React.ReactNode;
    cols?: number;
    height?: string;
    space?: string;
    padding?: string;
    isBorder?: boolean;
    allowScrollBar?: boolean;
    showBottomBorder?: boolean;
    calcHeight?: number;
    className?: string;
}

const ListComponent = forwardRef(
    (
        {
            children,
            cols = 1,
            height = "h-full",
            space = ".5",
            padding = "2",
            isBorder = false,
            allowScrollBar = true,
            showBottomBorder = true,
            calcHeight,
            className,
        }: IProps,
        ref: any
    ) => {
        return (
            <div
                className={
                    `overflow-auto  ${calcHeight ? "" : height}` +
                    " " +
                    (isBorder
                        ? `border-gray-400 border rounded-md ${showBottomBorder ? "!border-b " : "border-b-0"
                        }`
                        : "") +
                    " " +
                    (!allowScrollBar ? "no-scrollbar" : "") +
                    " " +
                    className
                }
                ref={ref}
                style={{ height: calcHeight ? `calc(100svh - ${calcHeight}rem)` : undefined }}
            >
                <ul
                    className={`grid p-${padding}`}
                    style={{
                        gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
                        gap: `${space}rem`,
                    }}
                >
                    {children}
                </ul>
            </div>
        );
    }
);

export default ListComponent;
