import { APP_LOCAL_DB_COLLECTIONS, AppLocalDB } from "../../../common/config/localDB";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IPaymentVoucherBody } from "../../../common/interfaces/body/PaymentVoucherBody";
import { ILocalDBGetOptionsProperties, IOperator } from "../../../common/LocalDB/LocalDBInterface";
import { IPaymentVoucherModel } from "../../../common/models/PaymentVoucherModel";
import { debug } from "../../../common/utils/CommonUtils";
import { IAdminPaymentVoucherInputs } from "../../admin/pages/paymentVoucher/AdminPaymentVoucherInterface";

export class PosPaymentVoucherRepo {
    static getAndCountPaymentVouchers = async () => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [], count: 0 };
            }

            const paymentVoucherWere: [keyof IPaymentVoucherModel, IOperator, any][] = [
                [`shiftId`, "==", shift.shiftId],
            ];

            const res = await AppLocalDB.getAndCount<IPaymentVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS,
                {
                    where: [paymentVoucherWere],
                }
            );
            return res;
        } catch (error) {
            debug(`PosPaymentVoucherRepo [getAndCountPaymentVouchers] Error: ${error}`);
            throw error;
        }
    };

    static async getShiftPaymentVouchers() {
        try {
            const shift = ShiftHelper.get();
            if (!shift) return [];

            const res = await AppLocalDB.get<IPaymentVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS,
                {
                    orderBy: ["id", "desc"],
                    where: [[[`shiftId`, "===", shift.shiftId]]],
                }
            );
            return res;
        } catch (error) {
            debug(`PosPaymentVoucherRepo [getShiftPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    static async getPaymentVouchers(
        options?: ILocalDBGetOptionsProperties<IPaymentVoucherModel>
    ) {
        try {
            return await AppLocalDB.get<IPaymentVoucherModel>(
                APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS,
                options
            );
        } catch (error) {
            debug(`PosPaymentVoucherRepo [getPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    static async addPaymentVoucher(body: IAdminPaymentVoucherInputs) {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");

            const formattedBody = {
                ...body,
                id: AppLocalDB.generateId(),
                shiftId: shift.shiftId,
            };

            const res = await AppLocalDB.add(
                APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS,
                formattedBody,
                true
            );
            return res;
        } catch (error) {
            debug(`PosPaymentVoucherRepo [addPaymentVoucher] Error: `, error);
            throw error;
        }
    }

    static async paymentVouchersCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS);
        } catch (error) {
            debug(`PosPaymentVoucherRepo [paymentVouchersCount] Error: `, error);
            throw error;
        }
    }

    static async uploadPaymentVouchers(body: IPaymentVoucherBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsEnums.PAYMENT_VOUCHERS_CREATE_MANY, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosPaymentVoucherRepo [uploadPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    static async deleteAllPaymentVouchers() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.PAYMENT_VOUCHERS);
        } catch (error) {
            debug(`PosPaymentVoucherRepo [deleteAllPaymentVouchers] Error: `, error);
            throw error;
        }
    }
}