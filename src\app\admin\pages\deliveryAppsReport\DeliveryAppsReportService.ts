import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { DeliveryAppsReportPdfPrinterContent } from "../../../../common/pdf-make/slices/deliveryAppsReport/DeliveryAppsReportPdfPrinterContent";
import { IDeliveryAppsReportPdfPrinterModel } from "../../../../common/pdf-make/slices/deliveryAppsReport/DeliveryAppsReportPdfPrinterModel";
import { IDeliveryApp } from "../../../../common/interfaces";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";

export class AdminDeliveryAppsReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        orders: IOrderModel[] | undefined,
        orderSum: IOrderSumModel | undefined,
        deliveryApp: IDeliveryApp
    ) {
        const model: IDeliveryAppsReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            deliveryAppName: deliveryApp.name,
            items: orders?.map((el) => ({
                invoiceNumber: el.invoiceNumber,
                subTotal: el.subTotal,
                discount: el.discount,
                vat: el.vat,
                total: el.total,
                deliveryAppFee: el.deliveryAppFee,
                totalDue: el.totalDue,
            })),
            totals: {
                count: orderSum?.count ?? 0,
                vat: orderSum?.vat ?? 0,
                total: orderSum?.total ?? 0,
                deliveryAppFee: orderSum?.deliveryAppFee ?? 0,
                totalDue: orderSum?.totalDue ?? 0,
            },
        };

        const fileName = `Delivery Apps Report - ${deliveryApp.name}`;
        PdfMakeUtils.download(
            DeliveryAppsReportPdfPrinterContent(model),
            fileName,
            {
                isLandScape: true,
                header: await PdfMakeHeaders.normal(true),
            }
        );
    }

    static handleData(
        orders: IOrderModel[] = [],
        returnedOrders: IReturnedOrderModel[] = [],
        receiptVouchers: IReceiptVoucherModel[] = []
    ): (IOrderModel & { invoiceType: string; customNumber: string })[] {
        return [
            ...orders.map((el) => ({
                ...el,
                customNumber: el.invoiceNumber,
                invoiceType: TranslateConstants.SELL,
                date: el.startTime,
            })),
            ...(returnedOrders.map((el) => ({
                ...el,
                customNumber: el.returnedInvoiceNumber,
                invoiceType: TranslateConstants.RETURNED,
                date: el.startTime,
            })) as any[]),
            ...receiptVouchers.map((el) => ({
                ...el,
                customNumber: el.number,
                invoiceType: TranslateConstants.RECEIPT_VOUCHER,
                total: el.cash + el.network,
                totalDue: -(el.cash + el.network),
            })) as any[],
        ].sort((a, b) => b.date - a.date);
    }
}
