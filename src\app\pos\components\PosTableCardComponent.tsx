import { FC, useState, useEffect } from "react";
import { GoClockFill } from "react-icons/go";
import { TableStatusEnum } from "../../../common/enums/DataEnums";

interface IProps {
    name: string;
    onClick?: () => void;
    status?: TableStatusEnum;
    startTime?: Date;
}

const PosTableCardComponent: FC<IProps> = ({
    name,
    onClick,
    status = TableStatusEnum.AVAILABLE,
    startTime,
}) => {
    const [time, setTime] = useState<string | undefined>(undefined);

    useEffect(() => {
        if (status === TableStatusEnum.OCCUPIED && startTime) {
            const interval = setInterval(() => {
                const diff = Math.abs(new Date().getTime() - startTime.getTime());
                const hours = Math.floor(diff / 1000 / 60 / 60);
                const minutes = Math.floor((diff / 1000 / 60) % 60);
                const seconds = Math.floor((diff / 1000) % 60);
                setTime(`${hours}:${minutes}:${seconds}`);
            }, 1000);

            return () => clearInterval(interval);
        } else {
            if (time) setTime(undefined);
        }
    }, [status, startTime]);

    return (
        <div
            className={
                "shadow rounded-2xl text-lg flex flex-col font-tajawal-bold h-44 btn btn-ghost" +
                " " +
                (status === TableStatusEnum.AVAILABLE
                    ? "bg-white"
                    : "bg-red-900 hover:bg-red-800 text-white")
            }
            onClick={onClick}
        >
            <span>{name}</span>
            {status === TableStatusEnum.OCCUPIED && (
                <span className="mt-4 flex items-center gap-1">
                    <span>{time ?? "00:00:00"}</span>
                    <GoClockFill />
                </span>
            )}
        </div>
    );
};

export default PosTableCardComponent;
