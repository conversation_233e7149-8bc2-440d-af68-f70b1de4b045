import { IAdminCustomerInputs } from "../../../app/admin/pages/customers/AdminCustomersInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { ICustomerModel } from "../../models/CustomerModel";
import { debug } from "../../utils/CommonUtils";

export class CustomersApiRepo {
    static async getCustomers(search?: string, limit?: number) {
        try {
            const res = await AxiosHelper.get<ICustomerModel[]>(
                EndPointsEnums.CUSTOMERS,
                { params: { search, limit } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CustomersApiRepo [getCustomers] Error: `, error);
            throw error;
        }
    }

    static async addCustomer(body: IAdminCustomerInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.post<ICustomerModel>(
                EndPointsEnums.CUSTOMERS,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CustomersApiRepo [addCustomer] Error: `, error);
            throw error;
        }
    }

    static async updateCustomer(id: number, body: IAdminCustomerInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.patch<ICustomerModel>(
                EndPointsEnums.CUSTOMERS,
                id,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CustomersApiRepo [updateCustomer] Error: `, error);
            throw error;
        }
    }
}
