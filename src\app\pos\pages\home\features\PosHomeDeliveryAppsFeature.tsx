import { FC } from "react";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";
import PosItemCardComponent from "../../../components/PosItemCardComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { PosHomeService } from "../PosHomeService";
import usePosActions from "../../../redux/usePosActions";
import { posOrderSelector } from "../../../redux/selector";
import { useAppSelector } from "../../../../../common/redux/store";
import { DeliveryAppEnum } from "../../../../../common/enums/DataEnums";

const PosHomeDeliveryAppsFeature: FC = () => {
    const posActions = usePosActions();
    const order = useAppSelector(posOrderSelector);
    const activeDeliverApps = DeliveryAppsHelper.getActiveDeliveryApps();

    const handleOnClick = (item: DeliveryAppEnum) =>
        PosHomeService.handleSelectDeliveryApp(item, posActions, order);

    return (
        <>
            {activeDeliverApps.map((item, index) => (
                <PosItemCardComponent
                    key={index}
                    name={TranslateHelper.t(item.name)}
                    type="delivery_app"
                    image={item.image}
                    onClick={() => handleOnClick(item.name)}
                />
            ))}
        </>
    );
}

export default PosHomeDeliveryAppsFeature;
