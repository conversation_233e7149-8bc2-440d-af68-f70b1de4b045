import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { IShiftPdfPrinterBodyModel } from "../../../../common/pdf-make/slices/shift/ShiftPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export const PosReceiptVoucherDestinationsConstant = [
    {
        name: TranslateConstants.ADD_TO_SHIFT,
    },
    {
        name: TranslateConstants.CUSTOMER,
    },
]

export enum PosVoucherViewEnum {
    NUMPAD = "NUMPAD",
    STATEMENT = "STATEMENT",
    STATEMENT_OPTIONS = "STATEMENT_OPTIONS",
};

export const PosHomeShiftInfoConstant = (
    shift: IShiftPdfPrinterBodyModel | undefined
): { text: string; value: number | string; keepIfZero?: boolean }[] | undefined => {
    if (!shift) return undefined;
    return [
        {
            text: TranslateConstants.START_TIME,
            value: DateUtils.format(
                new Date(shift.startTime),
                "dd-MM-yyyy hh:mm A"
            ),
        },
        {
            text: TranslateConstants.START_BALANCE,
            value: shift.startAmount,
        },
        {
            text: TranslateConstants.ORDERS_COUNT,
            value: shift.ordersCount,
        },
        {
            text: TranslateConstants.RETURNED_ORDERS_COUNT,
            value: shift.returnedOrdersCount,
        },
        {
            text: TranslateConstants.TOTAL_DISCOUNT,
            value: shift.discountAmount,
        },
        {
            text: TranslateConstants.TOTAL_SALES,
            value: shift.totalAmount,
        },
        {
            text: TranslateConstants.TOTAL_RETURNED,
            value: shift.totalReturnedAmount,
        },
        {
            text: TranslateConstants.NET,
            value: shift.totalNetAmount,
        },
        {
            text: TranslateConstants.CASH_SALES,
            value: shift.cashAmount,
        },
        {
            text: TranslateConstants.NETWORK_SALES,
            value: shift.networkAmount,
        },
        {
            text: TranslateConstants.CREDIT,
            value: shift.deferredAmount,
        },
        ...(shift.paymentAmounts || []).map((el) => ({
            text: el.name,
            value: Number(el.amount),
        })),
        {
            text: TranslateConstants.ADD_TO_SHIFT,
            value: shift.addedToShiftAmount,
        },
        {
            text: TranslateConstants.CUSTOMER_RECEIPT,
            value: shift.customerReceiptsAmount,
        },
        {
            text: TranslateConstants.PULLS,
            value: shift.pullsAmount,
        },
        {
            text: TranslateConstants.CASH_NET,
            value: shift.cashNetAmount,
        },
        {
            text: TranslateConstants.NETWORK_NET,
            value: shift.networkNetAmount,
        },
    ];
}