import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminCategoryInputs } from "./AdminCategoriesInterface";

export class AdminCategoriesValidation {
    static inputsValidation = (values: IAdminCategoryInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        } else if (values.name.length > 30) {
            ToastHelper.error(TranslateConstants.NAME_SIZE_ERROR);
            isValid = false;
        } else if (!!values.secondName && values.secondName.length > 30) {
            ToastHelper.error(TranslateConstants.SECOND_NAME_SIZE_ERROR);
            isValid = false;
        }

        return isValid;
    };
}
