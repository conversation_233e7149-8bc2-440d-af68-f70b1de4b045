import { ILocalDBWhere } from "../LocalDBInterface";
import {
    LocalDbHandleGetKeyRangeUtil,
    LocalDbHandleOrderByUtil,
    LocalDbHandleWhereUtil,
} from "../LocalDBUtils";

export const LocalDbGetAllService = <T>(
    db: IDBDatabase | undefined,
    collection: string
): Promise<T[]> => {
    return new Promise((resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        const transaction = db.transaction(collection, "readonly");
        const store = transaction.objectStore(collection);
        const request = store?.getAll();

        if (request) {
            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

const getAllWithoutWhere = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    limit: number = 0,
    skip: number = 0,
    skipUntil: number = Infinity,
    orderBy?: [string, "desc" | "asc"],
    indexName?: string,
    key?: string | number
): Promise<T[]> => {
    return new Promise((resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        const transaction = db.transaction(collection, "readonly");
        const store = transaction.objectStore(collection);
        let request: IDBRequest<T[]> | undefined;

        if (indexName && store.indexNames.contains(indexName)) {
            const index = store.index(indexName);
            request = index.getAll(
                LocalDbHandleGetKeyRangeUtil(skip, skipUntil, key),
                limit
            );
        } else {
            request = store.getAll(
                LocalDbHandleGetKeyRangeUtil(skip, skipUntil, key),
                limit
            );
        }

        if (request) {
            request.onsuccess = () => {
                const data = request.result;
                if (orderBy) {
                    resolve(LocalDbHandleOrderByUtil(data, orderBy));
                } else {
                    resolve(data);
                }
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
};

export const LocalDbGetAllWithOptionsService = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    limit: number = 0,
    skip: number = 0,
    skipUntil: number = Infinity,
    orderBy?: [string, "desc" | "asc"],
    where?: ILocalDBWhere<T>,
    batchSize: number = 1000,
    indexName?: string,
    key?: string | number
): Promise<T[]> => {
    if (!where) {
        return getAllWithoutWhere(
            db,
            collection,
            limit,
            skip,
            skipUntil,
            orderBy,
            indexName,
            key
        );
    }

    return new Promise(async (resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        let skipped = skip;
        const results: T[] = [];

        const handleResult = () => {
            if (orderBy) {
                resolve(LocalDbHandleOrderByUtil(results, orderBy));
            } else {
                resolve(results);
            }
        };

        const getBatch = async () => {
            const batch = await getAllWithoutWhere<T>(
                db,
                collection,
                (limit && limit < batchSize) ? limit : batchSize,
                skipped,
                skipUntil,
                undefined,
                indexName,
                key
            );

            if (batch.length === 0) {
                handleResult();
                return;
            }

            for (const record of batch) {
                if (limit !== 0 && results.length >= limit) break;
                if (
                    (limit === 0 || results.length < limit) &&
                    LocalDbHandleWhereUtil(record, where)
                ) {
                    results.push(record);
                }
            }

            if (
                batch.length < batchSize ||
                (limit !== 0 && results.length >= limit)
            ) {
                handleResult();
                return;
            }

            skipped = (batch[batch.length - 1] as any).id;
            getBatch();
        };

        getBatch();
    });
};

export const LocalDbGetAllWithJoinService = <T>(
    db: IDBDatabase | undefined,
    collection: string,
    join: { collection: string, fk: string, alias?: string, type?: "one" | "many" }[],
    limit: number = 0,
    skip: number = 0,
    skipUntil: number = Infinity,
    orderBy?: [string, "desc" | "asc"],
    where?: ILocalDBWhere<T>,
    batchSize: number = 1000,
    indexName?: string,
    key?: string | number
): Promise<T[]> => {
    return new Promise((resolve, reject) => {
        if (!db) {
            return reject(new Error("Database is not defined"));
        }

        for (const { collection: joinCollection, fk } of join) {
            const joinStore = db
                .transaction(joinCollection)
                .objectStore(joinCollection);
            if (!joinStore.indexNames.contains(fk)) {
                return reject(new Error(`Index ${fk} not found in ${joinCollection}`));
            }
        }

        const store = db.transaction(collection).objectStore(collection);
        const pk = store.keyPath as string;

        let skipped = skip;
        const results: T[] = [];

        const handleResult = () => {
            if (orderBy) {
                resolve(LocalDbHandleOrderByUtil(results, orderBy));
            } else {
                resolve(results);
            }
        };

        const getData = async () => {
            const data = await LocalDbGetAllWithOptionsService<T>(
                db,
                collection,
                limit < batchSize ? limit : batchSize,
                skipped,
                skipUntil,
                undefined,
                where,
                batchSize,
                indexName,
                key
            );

            if (data.length === 0) {
                handleResult();
                return;
            }

            for (const { collection: joinCollection, fk, alias, type } of join) {
                const joinName = alias || joinCollection;
                for (const record of data) {
                    if (limit !== 0 && results.length >= limit) break;
                    if (limit === 0 || results.length < limit) {
                        const batchJoin = await getAllWithoutWhere(
                            db,
                            joinCollection,
                            type === "many" ? undefined : 1,
                            undefined,
                            undefined,
                            undefined,
                            fk,
                            (record as any)[pk]
                        );

                        if (type === "many") {
                            (record as any)[joinName] = batchJoin;
                        } else {
                            (record as any)[joinName] = batchJoin[0];
                        }

                        results.push(record);
                    }
                }
            }

            if (data.length < batchSize || (limit !== 0 && results.length >= limit)) {
                handleResult();
                return;
            }

            skipped += batchSize;
            getData();
        };

        getData();
    });
};
