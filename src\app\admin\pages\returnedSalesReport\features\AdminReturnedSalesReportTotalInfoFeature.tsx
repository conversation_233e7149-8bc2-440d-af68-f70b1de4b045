import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IReturnedOrderSumModel } from "../../../../../common/models/ReturnedOrderSumModel";

interface IProps {
    returnedOrderSum?: IReturnedOrderSumModel;
}

const AdminReturnedSalesReportTotalInfoFeature: FC<IProps> = ({ returnedOrderSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.RETURNED_ORDERS_COUNT, value: returnedOrderSum?.count, fixedVal: 0, isHiddenInSm: true },
                { text: TranslateConstants.TAX, value: returnedOrderSum?.vat },
                { text: TranslateConstants.TOTAL, value: returnedOrderSum?.total },
                {
                    text: TranslateConstants.CASH,
                    value: returnedOrderSum?.cash,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.NETWORK,
                    value: returnedOrderSum?.network,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.CREDIT,
                    value: (returnedOrderSum?.deferred ?? 0) + (returnedOrderSum?.deliveryApps ?? 0),
                },
            ]}
        />
    );
};

export default AdminReturnedSalesReportTotalInfoFeature;
