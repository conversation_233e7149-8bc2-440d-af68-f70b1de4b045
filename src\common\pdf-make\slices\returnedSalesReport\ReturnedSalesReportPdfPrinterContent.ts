
import { Content } from "pdfmake/interfaces";
import { IReturnedSalesReportPdfPrinterModel } from "./ReturnedSalesReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { TranslateHelper } from "../../../helpers/TranslateHelper";
import { OrganizationHelper } from "../../../helpers/OrganizationHelper";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const ReturnedSalesReportPdfPrinterContent = (model: IReturnedSalesReportPdfPrinterModel): Content[] => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    return [
        {
            text: "تقرير المرتجعات", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: [
                    "*",
                    "*",
                    "*",
                    "auto",
                    "auto",
                    ...(hasTobaccoTax ? ["auto"] : []),
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                ],
                body: [
                    [
                        { text: "اجل", style: "tableHeader" },
                        { text: " شبكة", style: "tableHeader" },
                        { text: "نقداً", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        { text: "الإجمالي", style: "tableHeader" },
                        ...PdfMakeHelper.optionalDocItem(hasTobaccoTax,
                            [{ text: "ضريبة التبغ", style: "tableHeader" }]
                        ),
                        { text: "الخصم", style: "tableHeader" },
                        { text: "الإجمالي الفرعي", style: "tableHeader" },
                        { text: "نوع الطلب", style: "tableHeader" },
                        { text: "الفاتورة", style: "tableHeader" },
                        { text: " المرتجع", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.credit.toFixed(2),
                        el.network.toFixed(2),
                        el.cash.toFixed(2),
                        el.vat.toFixed(2),
                        el.total.toFixed(2),
                        ...PdfMakeHelper.optionalDocItem(hasTobaccoTax,
                            [el.tobaccoTax?.toFixed(2) || 0]
                        ),
                        el.discount.toFixed(2),
                        el.subTotal.toFixed(2),
                        TranslateHelper.t(el.orderType),
                        el.invoiceNumber,
                        el.returnedInvoiceNumber,
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            stack: [
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.count.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "عدد المرتجعات:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.vat.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "الضريبة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.total.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "الإجمالي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.cash.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "نقداً: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.network.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "شبكة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.credit.toFixed(2), margin: [0, 3, 0, 0] },
                                        { text: "اجل: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
            ],
        }
    ];
}