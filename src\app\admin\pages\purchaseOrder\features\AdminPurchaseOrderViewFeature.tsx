import { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction } from "react";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import useFetch from "../../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { PurchaseOrderApiRepo } from "../../../../../common/repos/api/PurchaseOrderApiRepo";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import { IPurchaseOrderModel } from "../../../../../common/models/PurchaseOrderModel";
import { AdminPurchaseOrderDataTableHeaders } from "../AdminPurchaseOrderConstants";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { AdminPurchaseOrderService } from "../AdminPurchaseOrderService";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useNavigate } from "react-router-dom";
import { RoutsConstants } from "../../../../../common/constants/RoutesConstants";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
    setSelectedItem: Dispatch<SetStateAction<IPurchaseOrderModel | undefined>>;
}

const AdminPurchaseOrderViewFeature: FC<IProps> = ({
    setIsView,
    setSelectedItem,
}) => {
    const { isSm } = useScreenSize();
    const navigate = useNavigate();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.PURCHASE_ORDERS,
        PurchaseOrderApiRepo.getPurchaseOrders
    );

    const handleOnPrint = (item: IPurchaseOrderModel) =>
        AdminPurchaseOrderService.handlePreview(item);

    const handleOnEdit = (item: IPurchaseOrderModel) => {
        setSelectedItem(item);
        setIsView(false);
    };

    const handleOnConvert = (item: IPurchaseOrderModel) =>
        navigate(RoutsConstants.admin.purchasesInvoices.fullPath, { state: item });

    return (
        <>
            <AddAndFilterComponent
                onAdd={() => setIsView(false)}
                onReload={refetch}
            />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminPurchaseOrderDataTableHeaders}
                    items={data || []}
                    selectors={(item: IPurchaseOrderModel) => [
                        item.number,
                        item.supplier?.name,
                        item.total,
                        DateUtils.format(item.date),
                    ]}
                    showEditButton={(item: IPurchaseOrderModel) => !item.isConverted}
                    showPrintButton={true}
                    onEdit={handleOnEdit}
                    onPrint={handleOnPrint}
                    customButtons={[
                        {
                            text: (item: IPurchaseOrderModel) => item.isConverted ? TranslateConstants.CONVERTED : TranslateConstants.CONVERT,
                            onClick: (item: IPurchaseOrderModel) => handleOnConvert(item),
                            isDisabled: (item: IPurchaseOrderModel) => item.isConverted,
                        },
                    ]}
                />
            </StatusComponent>
        </>
    );
};

export default AdminPurchaseOrderViewFeature;
