import { NavigateFunction } from "react-router-dom";
import { accessTokenKeyConstant } from "../../common/constants/ConfigConstants";
import { RoutsConstants } from "../../common/constants/RoutesConstants";
import { TranslateConstants } from "../../common/constants/TranslateConstants";
import { CookiesHelper } from "../../common/helpers/CookiesHelper";
import { IAuthLoginState, IAuthRegisterInputs, IAuthRegisterState } from "./AuthInterface";
import { AuthValidation } from "./AuthValidation";
import { ToastHelper } from "../../common/helpers/ToastHelper";
import { AuthRegisterInitialState } from "./AuthConstants";
import { OrganizationRepo } from "../admin/repos/OrganizationRepo";
import store from "../../common/redux/store";
import { OrganizationHelper } from "../../common/helpers/OrganizationHelper";
import { AuthRepo } from "./AuthRepo";
import { appApi } from "../../common/redux/api/slice";

export class AuthService {
    static async login(
        email: string,
        password: string,
        setState: React.Dispatch<React.SetStateAction<IAuthLoginState>>,
        navigate: NavigateFunction,
    ) {
        try {
            const isValid = AuthValidation.loginValidation(email, password, setState);
            if (!isValid) return;

            setState((prev) => ({ ...prev, error: "", loading: true }));

            const auth = await AuthRepo.login(email, password);

            if (!auth.success || !auth.access_token) {
                setState((prev) => ({
                    ...prev,
                    error: auth.message || TranslateConstants.ERROR_PLEASE_TRY_AGAIN,
                    loading: false,
                }));
                ToastHelper.error(TranslateConstants.ERROR_PLEASE_TRY_AGAIN);
                return;
            }

            CookiesHelper.set(accessTokenKeyConstant, auth.access_token, 30);
            await OrganizationRepo.getOwn();
            navigate(RoutsConstants.role.path);
        } catch (error) {
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.ERROR_PLEASE_TRY_AGAIN,
                loading: false,
            }));
        }
    }

    static async register(
        values: IAuthRegisterInputs,
        setState: React.Dispatch<React.SetStateAction<IAuthRegisterState>>,
    ) {
        try {
            const isValid = AuthValidation.registerValidation(values, setState);
            if (!isValid) return;

            setState((prev) => ({ ...prev, error: "", loading: true }));

            const res = await AuthRepo.register(values);

            if (!res.success) {
                setState((prev) => ({
                    ...prev,
                    error: res.message || TranslateConstants.ERROR_PLEASE_TRY_AGAIN,
                    loading: false,
                }));
                ToastHelper.error(TranslateConstants.ERROR_PLEASE_TRY_AGAIN);
                return;
            }

            ToastHelper.success(TranslateConstants.REGISTER_SUCCESSFULLY);
            setState(AuthRegisterInitialState);
        } catch (error) {
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.ERROR_PLEASE_TRY_AGAIN,
                loading: false,
            }));
        }
    }

    static async logout(navigate?: NavigateFunction) {
        store.dispatch(appApi.util.resetApiState())
        CookiesHelper.delete(accessTokenKeyConstant);
        OrganizationHelper.removeOrganization();
        if (navigate) {
            navigate(RoutsConstants.auth.path);
            return;
        }
        window.location.href = "/";
    }
}
