import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { ICategoryModel } from "../../../../common/models/CategoryModel";
import { IProductModel } from "../../../../common/models/ProductModel";
import useActions from "../../../../common/redux/data/useActions";
import { CategoriesApiRepo } from "../../../../common/repos/api/CategoriesApiRepo";
import { ProductsApiRepo } from "../../../../common/repos/api/ProductsApiRepo";
import { AdminProductDataTableHeaders } from "./AdminProductsConstants";
import AdminProductsModal from "./modals/AdminProductsModal";

const AdminProductsPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.PRODUCTS,
        ProductsApiRepo.getProducts
    );
    const {
        data: categories,
        isLoading: categoriesIsLoading,
        isError: categoriesIsError,
        refetch: categoriesRefetch,
    } = useFetch(EndPointsEnums.CATEGORIES, CategoriesApiRepo.getCategories);

    const updateProduct = useFlatMutate(ProductsApiRepo.updateProduct, {
        updateCached: {
            key: EndPointsEnums.PRODUCTS,
            operation: "update",
            selector: (data: IProductModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: IProductModel) =>
        updateProduct(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: IProductModel) => {
        actions.openModal({
            component: <AdminProductsModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent
                onAdd={handleOnClick}
                onReload={() => {
                    refetch();
                    categoriesRefetch();
                }}
            />
            <StatusComponent
                isLoading={isLoading || categoriesIsLoading}
                isError={isError || categoriesIsError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminProductDataTableHeaders}
                    items={data || []}
                    selectors={(item: IProductModel) => {
                        const isFixed = item.productSizeType === ProductSizeTypeEnum.FIXED;
                        return [
                            item.number,
                            item.name,
                            item.secondName,
                            categories?.find((e: ICategoryModel) => e.id === item.categoryId)
                                ?.name,
                            TranslateHelper.t(
                                isFixed
                                    ? TranslateConstants.FIXED_SIZE
                                    : TranslateConstants.MULTIPLE_SIZE
                            ),
                            (isFixed && item.price) ? item.price : "-",
                        ];
                    }}
                    imageSelector={(item: IProductModel) => item.image}
                    showEditButton={true}
                    showDeleteButton={false}
                    onEdit={(item: IProductModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: IProductModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminProductsPage;
