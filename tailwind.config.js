/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/react-tailwindcss-datepicker/dist/index.esm.{js,ts}",
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      animation: {
        h_spin: 'h_spin 2s ease-in-out infinite',
      },
      keyframes: {
        h_spin: {
          '0%': { transform: 'rotateY(0deg)' },
          '20%': { transform: 'rotateY(0deg)' },
          '100%': { transform: 'rotateY(360deg)' }
        }
      },
      fontFamily: {
        'tajawal-extra-light': ['tajawal-extra-light'],
        'tajawal-light': ['tajawal-light'],
        'tajawal-medium': ['tajawal-medium'],
        'tajawal-regular': ['tajawal-regular'],
        'tajawal-bold': ['tajawal-bold'],
        'tajawal-extra-bold': ['tajawal-extra-bold'],
        'tajawal-black': ['tajawal-black'],
      },
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      'winter',
      'dark',
    ]
  }
}