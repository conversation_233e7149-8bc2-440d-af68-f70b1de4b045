import useFetch from "../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import useActions from "../../../../common/redux/data/useActions";
import { AdminReceiptVoucherDataTableHeaders } from "./AdminReceiptVoucherConstants";
import AdminReceiptVoucherModal from "./modals/AdminReceiptVoucherModal";
import TableComponent from "../../../../common/components/TableComponent";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { maxStringLength } from "../../../../common/utils/CommonUtils";
import { ReceiptVouchersApiRepo } from "../../../../common/repos/api/ReceiptVouchersApiRepo";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { AdminReceiptVouchersReportService } from "../receiptVoucherReport/ReceiptVouchersReportService";

const AdminReceiptVoucherPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.RECEIPT_VOUCHERS,
        () => ReceiptVouchersApiRepo.getReceiptVouchers({ getCustomers: true })
    );

    const handleOnClick = (isEdit?: boolean, item?: IReceiptVoucherModel) => {
        actions.openModal({
            component: <AdminReceiptVoucherModal isEdit={isEdit} item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminReceiptVoucherDataTableHeaders}
                    items={data || []}
                    selectors={(item: IReceiptVoucherModel) => [
                        item.number,
                        item.destination ? TranslateHelper.t(item.destination) : "-",
                        item.customer?.name,
                        item.cash,
                        item.network,
                        DateUtils.format(item.date),
                        maxStringLength(item.note, 10),
                    ]}
                    showEditButton={(item: IReceiptVoucherModel) => !item.shiftId}
                    showPrintButton={true}
                    onEdit={(item: IReceiptVoucherModel) => handleOnClick(true, item)}
                    onPrint={AdminReceiptVouchersReportService.handleOnPrint}
                />
            </StatusComponent>
        </>
    );
};

export default AdminReceiptVoucherPage;
