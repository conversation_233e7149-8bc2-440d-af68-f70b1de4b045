import { useEffect, useRef } from "react";

type Keys = "Shift" | "F6" | "Control";

const useKeyboard = (keys: Keys | Keys[], onKeys?: () => void) => {
    const pressedKeys = useRef<Set<string>>(new Set());

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            pressedKeys.current.add(e.key);
            let allKeysPressed = false;

            if (typeof keys === "string")
                allKeysPressed = pressedKeys.current.has(keys);
            else allKeysPressed = keys.every((key) => pressedKeys.current.has(key));

            if (allKeysPressed) onKeys?.();
        };

        const handleKeyUp = (e: KeyboardEvent) => {
            pressedKeys.current.delete(e.key);
        };

        window.addEventListener("keydown", handleKeyDown);
        window.addEventListener("keyup", handleKeyUp);

        return () => {
            window.removeEventListener("keydown", handleKeyDown);
            window.removeEventListener("keyup", handleKeyUp);
        };
    }, [keys, onKeys]);
};

export default useKeyboard;
