import { FC, useMemo } from "react";
import { IProductModel } from "../../../../../common/models/ProductModel";
import PosItemCardComponent from "../../../components/PosItemCardComponent";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";
import { ICategoryModel } from "../../../../../common/models/CategoryModel";
import { useAppSelector } from "../../../../../common/redux/store";
import { posProductsSelector } from "../../../redux/selector";
import { OrderTypeEnum, ProductSizeTypeEnum } from "../../../../../common/enums/DataEnums";
import useActions from "../../../../../common/redux/data/useActions";
import PosHomeProductSizesModal from "../modals/PosHomeProductSizesModal";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import PosOrderAdditionsModal from "../../../containers/order/modals/PosOrderAdditionsModal";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";
import { IPosOrder } from "../../../interface";
import { PriceUtils } from "../../../../../common/utils/PriceUtils";

interface IProps {
    selectedCategory?: ICategoryModel;
    order: IPosOrder;
}

const PosHomeProductsFeature: FC<IProps> = ({
    selectedCategory,
    order,
}) => {
    const actions = useActions();
    const posActions = usePosActions();
    const products = useAppSelector(posProductsSelector);

    const filteredProducts = useMemo(() => {
        if (!selectedCategory) return;

        return products.filter((product) => {
            const sameCategory = product.categoryId === selectedCategory?.id
            if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
                const { isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(order.deliveryApp);
                if (product.productSizeType === ProductSizeTypeEnum.FIXED) {
                    return sameCategory && product.deliveryApps && (product.deliveryApps as any)[isActiveKey];
                }
                return sameCategory && product.sizes.some((size) => (size.deliveryApps as any)?.[isActiveKey]);
            }

            return sameCategory
        });
    }, [products, selectedCategory]);

    const handleOnClick = (product: IProductModel) => {
        if (product.productSizeType === ProductSizeTypeEnum.MULTIPLE) {
            actions.openModal({
                title: TranslateHelper.t(TranslateConstants.SIZES) + " (" + product.name + ")",
                component: <PosHomeProductSizesModal product={product} order={order} />,
                size: "lg"
            });
            return;
        }
        if (product.isIncludingAdditions) {
            actions.openModal({
                title: TranslateHelper.t(TranslateConstants.ADDITIONS) + " (" + product.name + ")",
                component: <PosOrderAdditionsModal />,
                size: "lg",
            });
        }
        PosHomeService.handleAddProductToOrder(product, posActions, order);
    };

    return (
        <>
            {filteredProducts?.map((product) => {
                return (
                    <PosItemCardComponent
                        key={product.id}
                        name={product.name}
                        secondName={product.secondName}
                        type="product"
                        price={PriceUtils.getProductPrice(product, order)}
                        image={product.image}
                        onClick={() => handleOnClick(product)}
                        productSizeType={product.productSizeType}
                    />
                )
            })}
        </>
    );
};

export default PosHomeProductsFeature;
