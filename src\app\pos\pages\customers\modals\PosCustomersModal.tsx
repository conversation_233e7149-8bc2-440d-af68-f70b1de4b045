import { FC } from "react";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useOnlineStatus from "../../../../../common/hooks/useOnlineStatus";
import useCustomState from "../../../../../common/hooks/useCustomState";
import { AdminCustomerInputs } from "../../../../admin/pages/customers/AdminCustomersConstants";
import { IAdminCustomerInputs } from "../../../../admin/pages/customers/AdminCustomersInterface";
import { ICustomerModel } from "../../../../../common/models/CustomerModel";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { CustomersApiRepo } from "../../../../../common/repos/api/CustomerApiRepo";
import { PosCustomersRepo } from "../../../repo/PosCustomersRepo";

interface IProps {
    item: ICustomerModel;
}

const PosCustomersModal: FC<IProps> = ({ item }) => {
    const isOnline = useOnlineStatus();
    const [inputs, setInputs] = useCustomState<IAdminCustomerInputs>(
        AdminCustomerInputs,
        { updateState: item }
    );

    const mutate = useFlatMutate(CustomersApiRepo.updateCustomer, {
        closeModalOnSuccess: true,
        onSuccess: ({ data }) => PosCustomersRepo.updateCustomer(item.id, data),
        updateCached: {
            key: "pos-customers",
            operation: "update",
            selector: (data: ICustomerModel) => data.id,
        },
    });

    return (
        <>
            <div className="flex flex-col gap-2 px-2">
                {!isOnline && (
                    <p className="text-red-400 text-center">
                        {TranslateHelper.t(TranslateConstants.NO_INTERNET_CONNECTION)}
                    </p>
                )}
                <InputComponent
                    label={TranslateConstants.NAME}
                    className="!h-12"
                    value={inputs.name}
                    onChange={(name) => setInputs({ ...inputs, name })}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.MOBILE}
                    className="!h-12"
                    value={inputs.mobile}
                    onChange={(mobile) => setInputs({ ...inputs, mobile })}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.TAX_NUMBER}
                    className="!h-12"
                    value={inputs.taxNumber}
                    onChange={(taxNumber) => setInputs({ ...inputs, taxNumber })}
                />
                <InputComponent
                    label={TranslateConstants.ADDRESS}
                    className="!h-12"
                    value={inputs.address}
                    onChange={(address) => setInputs({ ...inputs, address })}
                />
            </div>
            <ModalButtonsComponent
                text={TranslateConstants.ADD}
                onClick={() => mutate(item.id, inputs, false)}
                isDisabled={!inputs.name || !inputs.mobile || !isOnline}
            />
        </>
    );
};

export default PosCustomersModal;
