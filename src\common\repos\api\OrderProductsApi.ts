import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IOrderProductSumModel } from "../../models/OrderProductSumModel";
import { IOrderSumModel } from "../../models/OrderSumModel";
import { debug } from "../../utils/CommonUtils";

export class OrderProductsApiRepo {
    static async sum(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IOrderSumModel>(
                EndPointsEnums.ORDER_PRODUCTS_SUM,
                { params: { startTime, endTime, isTobaccoTax } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrderProductsApiRepo [sum] Error: `, error);
            throw error;
        }
    }

    static async getOrderProducts(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IOrderProductSumModel[]>(
                EndPointsEnums.ORDER_PRODUCTS_GET,
                { params: { startTime, endTime, isTobaccoTax } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrderProductsApiRepo [getOrders] Error: `, error);
            throw error;
        }
    }

    static async getSumMany(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean,
        limit?: number,
        getPrice?: boolean,
    ) {
        try {
            const res = await AxiosHelper.get<IOrderProductSumModel[]>(
                EndPointsEnums.ORDER_PRODUCTS_SUM_MANY,
                { params: { startTime, endTime, isTobaccoTax, limit, getPrice } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrderProductsApiRepo [getSumMany] Error: `, error);
            throw error;
        }
    }

    static async getRushHours(
        startTime?: number,
        endTime?: number,
    ) {
        try {
            const res = await AxiosHelper.get<{ hour: number; count: number; }[]>(
                EndPointsEnums.ORDERS_RUSH_HOURS,
                { params: { startTime, endTime } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        }
        catch (error) {
            debug(`OrderProductsApiRepo [getRushHours] Error: `, error);
            throw error;
        }
    }
}