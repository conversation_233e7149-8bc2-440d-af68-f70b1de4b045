import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";

interface IProps {
    image?: string | File;
    title: string;
    subTitle?: string;
    onEdit?: () => void;
    onDelete?: () => void;
    onClick?: () => void;
    onSubTitleClick?: () => void;
    customButton?: {
        text: string;
        onClick?: () => void;
        bgColor?: string;
    };
    startText?: string;
    endText?: string;
    rounded?: boolean;
    bottomBorderOnly?: boolean;
    subTitleEllipsis?: boolean;
    padding?: string;
    titleClassName?: string;
    className?: string;
}

const ListTileItemComponent: FC<IProps> = ({
    image,
    title,
    subTitle,
    onEdit,
    onDelete,
    onClick,
    onSubTitleClick,
    customButton,
    startText,
    endText,
    rounded = true,
    bottomBorderOnly = false,
    subTitleEllipsis = false,
    padding,
    titleClassName,
    className,
}) => {
    const isClickable = !!onClick;

    return (
        <li
            className={
                "flex bg-base-100 shadow-sm items-center justify-between border-gray-400" +
                " " +
                (!!padding ? padding : subTitle || image ? "p-2" : "p-3") +
                " " +
                (rounded ? "rounded-md" : "rounded-none") +
                " " +
                (bottomBorderOnly ? "border-b" : "border") +
                " " +
                (isClickable ? "cursor-pointer" : "") +
                " " +
                (isClickable
                    ? "hover:!bg-base-300 text-black dark:text-white bg-base-200"
                    : "") +
                " " +
                className
            }
            onClick={onClick}
        >
            <div className="flex items-center gap-2">
                {startText && (
                    <div className="font-bold text-xl min-w-10 text-center">
                        {startText}
                    </div>
                )}
                {image && (
                    <div className="rounded-full border p-1">
                        <img
                            src={
                                typeof image === "string" ? image : URL.createObjectURL(image)
                            }
                            className="w-10 h-10 overflow-hidden rounded-full"
                        />
                    </div>
                )}
                <>
                    <h5
                        className={
                            "font-bold" +
                            " " +
                            (!isClickable ? "text-black dark:text-white" : "text-inherit") +
                            " " +
                            titleClassName
                        }
                    >
                        {title}
                    </h5>
                    {subTitle && (
                        <div
                            className={
                                "text-sm text-gray-500" +
                                " " +
                                (subTitleEllipsis
                                    ? "overflow-hidden w-52 whitespace-nowrap overflow-ellipsis"
                                    : "")
                            }
                            onClick={() => { onSubTitleClick }}
                        >
                            {subTitle}
                        </div>
                    )}
                </>
            </div>
            <div className="flex gap-2">
                {!!endText && (
                    <div className="font-bold text-xl min-w-10 text-center">
                        {endText}
                    </div>
                )}
                {!!customButton && (
                    <button
                        className={
                            "text-white rounded-md p-1 w-16" +
                            " " +
                            (customButton.bgColor || "bg-cyan-500")
                        }
                        onClick={(e) => {
                            e.stopPropagation();
                            customButton.onClick && customButton.onClick();
                        }}
                    >
                        {customButton.text}
                    </button>
                )}
                {!!onEdit && (
                    <button
                        className="bg-cyan-500 text-white rounded-md p-1 w-16"
                        onClick={(e) => {
                            e.stopPropagation();
                            onEdit();
                        }}
                    >
                        {TranslateConstants.EDIT}
                    </button>
                )}
                {!!onDelete && (
                    <button
                        className="bg-red-700 text-white rounded-md p-1 w-16"
                        onClick={(e) => {
                            e.stopPropagation();
                            onDelete();
                        }}
                    >
                        {TranslateConstants.DELETE}
                    </button>
                )}
            </div>
        </li>
    );
};

export default ListTileItemComponent;
