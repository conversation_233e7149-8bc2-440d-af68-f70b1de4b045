import TitledCardComponent from "../../../../../common/components/TitledCardComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { Doughnut } from "react-chartjs-2";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { FC, useMemo } from "react";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    orderTotal: number;
    purchaseInvoicesTotal: number;
}

const AdminDashboardOperationsSummaryFeature: FC<IProps> = ({
    orderTotal = 0,
    purchaseInvoicesTotal = 0,
}) => {
    const isEmpty = useMemo(() => {
        return !orderTotal && !purchaseInvoicesTotal;
    }, [orderTotal, purchaseInvoicesTotal]);

    const percentage = useMemo(() => {
        return orderTotal / (orderTotal + purchaseInvoicesTotal) * 100;
    }, [orderTotal, purchaseInvoicesTotal]);

    const PieData = {
        datasets: [
            {
                data: isEmpty ? [100] : [
                    purchaseInvoicesTotal,
                    orderTotal,
                ],
                backgroundColor: isEmpty ? ['#94a3b8'] : [
                    '#418dd9',
                    '#226bb2'
                ],
                borderColor: isEmpty ? ['#94a3b8'] : [
                    '#418dd9',
                    '#226bb2'
                ],
            }
        ]
    }

    return (
        <TitledCardComponent title={TranslateConstants.OPERATIONS_SUMMARY}>
            <div className="flex justify-center items-center flex-col gap-2 h-full">
                <div className="flex-1 flex items-center justify-center min-h-0">
                    <Doughnut
                        options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false,
                                },
                            }
                        }}
                        data={PieData}
                    />
                </div>
                <div className="w-full flex justify-center items-center gap-2 text-sm font-tajawal-bold dark:text-white">
                    <div className="flex items-center gap-1">
                        <div className="h-3 w-5 bg-[#226bb2]"></div>
                        <span>{TranslateHelper.t(TranslateConstants.SALES)} {percentage ? "(" + fixedNumber(percentage) + "%)" : ""}</span>
                    </div>
                    <span>-</span>
                    <div className="flex items-center gap-1">
                        <div className="h-3 w-5 bg-[#418dd9]"></div>
                        <span>{TranslateHelper.t(TranslateConstants.PURCHASES)} {percentage ? "(" + fixedNumber(100 - percentage) + "%)" : ""}</span>
                    </div>
                </div>
            </div>
        </TitledCardComponent>
    );
}

export default AdminDashboardOperationsSummaryFeature;
