import { FC, useEffect, useMemo, useState } from "react";
import { IDeliveryApp } from "../../../../../common/interfaces";
import { AdminDeliveryAppProductsViewEnum } from "../AdminDeliveryAppsConstants";
import StatusComponent from "../../../../../common/components/StatusComponent";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import AdminDeliveryAppsProductsSlice from "../slices/AdminDeliveryAppsProductsSlice";
import AdminDeliveryAppsAdditionsSlice from "../slices/AdminDeliveryAppsAdditionsSlice";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { ProductsApiRepo } from "../../../../../common/repos/api/ProductsApiRepo";
import useFetch from "../../../../../common/asyncController/useFetch";
import { AdditionsApiRepo } from "../../../../../common/repos/api/AdditionsApiRepo";
import AdminDeliveryAppsTopButtonsSlice from "../slices/AdminDeliveryAppsTopButtonsSlice";
import {
    IAdminDeliveryAppsAdditionItemProps,
    IAdminDeliveryAppsProductItemProps,
} from "../AdminDeliveryAppsInterface";
import {
    isValidAdditions,
    isValidProducts,
} from "../AdminDeliveryAppsValidation";
import { adminHandleDeliveryAppProducts } from "../AdminDeliveryAppsUtils";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { DeliveryAppsProductsApiRepo } from "../../../../../common/repos/api/DeliveryAppsProductsApiRepo";

interface IProps {
    onBack: () => void;
    item: IDeliveryApp;
}

const AdminDeliveryAppsProductsFeature: FC<IProps> = ({ onBack, item }) => {
    const { isSm, isXs } = useScreenSize();
    const [view, setView] = useState<AdminDeliveryAppProductsViewEnum>(
        AdminDeliveryAppProductsViewEnum.PRODUCTS
    );
    const [formatProducts, setFormatProducts] = useState<
        IAdminDeliveryAppsProductItemProps[]
    >([]);
    const [formatAdditions, setFormatAdditions] = useState<
        IAdminDeliveryAppsAdditionItemProps[]
    >([]);
    const [isSaveDisabled, setIsSaveDisabled] = useState(true);
    const isProductsView = view === AdminDeliveryAppProductsViewEnum.PRODUCTS;

    const {
        data: products,
        isLoading: productsIsLoading,
        isError: productsIsError,
        refetch: productsRefetch,
    } = useFetch(
        EndPointsEnums.PRODUCTS + "-deliveryApps",
        (active = true, deliveryApps = true, excludeTobaccoTax = true) =>
            ProductsApiRepo.getProducts(active, deliveryApps, excludeTobaccoTax)
    );

    const {
        data: additions,
        isLoading: additionsIsLoading,
        isError: additionsIsError,
        refetch: additionsRefetch,
    } = useFetch(
        EndPointsEnums.ADDITIONS + "-deliveryApps",
        (active = true, deliveryApps = true) => AdditionsApiRepo.getAdditions(active, deliveryApps),
        { autoFetchIfEmpty: false }
    );

    useEffect(() => {
        setIsSaveDisabled(
            !isValidProducts(formatProducts, products, item.name) &&
            !isValidAdditions(formatAdditions, additions, item.name)
        );
    }, [formatProducts, formatAdditions]);

    const setDeliveryAppProducts = useFlatMutate(
        DeliveryAppsProductsApiRepo.setDeliveryAppProducts,
        { showDefaultSuccessToast: true }
    );
    const setDeliveryAppSizes = useFlatMutate(
        DeliveryAppsProductsApiRepo.setDeliveryAppSizes,
        { showDefaultSuccessToast: true }
    );
    const setDeliveryAppAdditions = useFlatMutate(
        DeliveryAppsProductsApiRepo.setDeliveryAppAdditions,
        { showDefaultSuccessToast: true }
    );

    const handleOnSave = async () => {
        const { deliveryProducts, deliverySizes, deliveryAdditions } =
            adminHandleDeliveryAppProducts(
                formatProducts,
                products,
                formatAdditions,
                additions,
                item.name
            );

        if (deliveryProducts.length) await setDeliveryAppProducts(deliveryProducts);
        if (deliverySizes.length) await setDeliveryAppSizes(deliverySizes);
        if (deliveryAdditions.length)
            await setDeliveryAppAdditions(deliveryAdditions);
        setIsSaveDisabled(true);
    };

    const handleRefetch = () => {
        if (isProductsView) {
            setFormatProducts([]);
            productsRefetch();
            return;
        }

        setFormatAdditions([]);
        additionsRefetch();
    };

    const handleOnSelect = (item: AdminDeliveryAppProductsViewEnum) => {
        const isProductsView = item === AdminDeliveryAppProductsViewEnum.PRODUCTS;
        setView(item);
        if (isProductsView && !products) productsRefetch();
        if (!isProductsView && !additions) additionsRefetch();
    };

    const isEmpty = useMemo(() => {
        return isProductsView
            ? !products || products.length === 0
            : !additions || additions.length === 0;
    }, [view, products, additions]);

    return (
        <>
            <AdminDeliveryAppsTopButtonsSlice
                onBack={onBack}
                item={item}
                handleRefetch={handleRefetch}
                handleOnSelect={handleOnSelect}
                handleOnSave={handleOnSave}
                isSaveDisabled={isSaveDisabled}
            />
            <StatusComponent
                isLoading={productsIsLoading || additionsIsLoading}
                isError={productsIsError || additionsIsError}
                isEmpty={isEmpty}
                height={isXs ? 13.7 : isSm ? 10.6 : 11.3}
            >
                {view === AdminDeliveryAppProductsViewEnum.PRODUCTS && (
                    <AdminDeliveryAppsProductsSlice
                        products={products}
                        formatProducts={formatProducts}
                        setFormatProducts={setFormatProducts}
                        deliveryApp={item.name}
                    />
                )}
                {view === AdminDeliveryAppProductsViewEnum.ADDITIONS && (
                    <AdminDeliveryAppsAdditionsSlice
                        additions={additions}
                        formatAdditions={formatAdditions}
                        setFormatAdditions={setFormatAdditions}
                        deliveryApp={item.name}
                    />
                )}
            </StatusComponent>
        </>
    );
};

export default AdminDeliveryAppsProductsFeature;
