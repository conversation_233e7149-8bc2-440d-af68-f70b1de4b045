import { useEffect, useState } from "react";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { IPosSmallScreenSettings } from "../../../../../common/interfaces";
import { SmallScreenHelper } from "../../../../../common/helpers/SmallScreenHelper";
import { ToastHelper } from "../../../../../common/helpers/ToastHelper";
import { PosDisplayRepo } from "../../../repo/PosDisplayRepo";
import CheckBoxComponent from "../../../../../common/components/CheckBoxComponent";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import useFetch from "../../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";

const PosSmallScreenSettingsModal = () => {
    const [isDisabled, setIsDisabled] = useState(true);
    const [state, setState] = useState<IPosSmallScreenSettings>(
        SmallScreenHelper.get()
    );

    const {
        data: ports,
        isLoading: portsIsLoading,
        isError: portsIsError,
    } = useFetch(
        EndPointsEnums.DISPLAY_PORTS,
        PosDisplayRepo.getPorts,
        { autoFetchOnMount: true }
    );

    useEffect(() => {
        const { comPort, port, isActive } = SmallScreenHelper.get();
        setIsDisabled(
            comPort === state.comPort &&
            port === state.port &&
            isActive === state.isActive
        );
    }, [state]);

    const handleOnSave = async () => {
        try {
            if (!state) return;
            if (state.isActive) await PosDisplayRepo.connect(state.comPort, state.port, state.isActive);
            SmallScreenHelper.set(state);
            setIsDisabled(true);
            ToastHelper.success();
        } catch (error) {
            ToastHelper.error();
        }
    };

    return (
        <>
            <div className="grid grid-cols-1 gap-2 px-2">
                <DropDownSearchComponent
                    label={TranslateConstants.COM_PORT}
                    items={ports?.map((item) => item.path) || []}
                    onSelect={(comPort) => setState({ ...state, comPort })}
                    titleSelector={(item) => item}
                    isSearchable={false}
                    isNullable={false}
                    defaultValue={state.comPort}
                    isError={portsIsError}
                    isLoading={portsIsLoading}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.PORT}
                    value={state?.port.toString()}
                    onChange={(port) => setState({ ...state, port: +port })}
                />
                <CheckBoxComponent
                    onChange={(isActive) => setState({ ...state, isActive })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.isActive}
                    containerClassName="my-2"
                />
            </div>
            <ModalButtonsComponent
                text={TranslateConstants.SAVE}
                onClick={handleOnSave}
                isDisabled={isDisabled}
            />
        </>
    );
};

export default PosSmallScreenSettingsModal;
