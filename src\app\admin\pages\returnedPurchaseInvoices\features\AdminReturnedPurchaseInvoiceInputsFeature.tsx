import { <PERSON><PERSON><PERSON>, FC, SetStateAction, useEffect, useMemo } from "react";
import useFetch from "../../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import InputComponent from "../../../../../common/components/InputComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import {
    AdminReturnedPurchaseInvoiceHeaders,
    AdminReturnedPurchaseInvoiceInitialState,
    AdminReturnedPurchaseInvoiceInputs,
} from "../AdminReturnedPurchaseInvoiceConstants";
import {
    IAdminReturnedPurchaseInvoiceInputs,
    IAdminReturnedPurchaseInvoiceState,
} from "../AdminReturnedPurchaseInvoiceInterfaces";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import useCustomState from "../../../../../common/hooks/useCustomState";
import StatusComponent from "../../../../../common/components/StatusComponent";
import { PurchaseUtils } from "../../../../../common/utils/PurchaseUtils";
import AdminMultiplePurchasesProductInputsComponent from "../../../components/AdminMultiplePurchasesProductInputsComponent";
import { AdminReturnedPurchaseInvoiceService } from "../AdminReturnedPurchaseInvoiceService";
import { IPurchaseInvoiceModel } from "../../../../../common/models/PurchaseInvoiceModel";
import { AdminReturnedPurchaseInvoiceUtil } from "../AdminReturnedPurchaseInvoiceUtil";
import { PurchaseInvoiceApiRepo } from "../../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { maxStringLength } from "../../../../../common/utils/CommonUtils";
import { IReturnedPurchaseInvoiceModel } from "../../../../../common/models/ReturnedPurchaseInvoiceModel";
import { PaymentMethodsConstant } from "../../../../../common/constants/CommonConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { AdminReturnedPurchaseInvoiceValidation } from "../AdminReturnedPurchaseInvoiceValidation";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
}

const AdminReturnedPurchaseInvoiceInputsFeature: FC<IProps> = ({
    setIsView,
}) => {
    const { isSm } = useScreenSize();
    const [state, setState, resetState] = useCustomState<
        IAdminReturnedPurchaseInvoiceState,
        IReturnedPurchaseInvoiceModel
    >(AdminReturnedPurchaseInvoiceInitialState);

    const {
        data: purchaseInvoices,
        isLoading: purchaseInvoicesIsLoading,
        isError: purchaseInvoicesIsError,
        refetch: refetchPurchaseInvoices,
    } = useFetch("returned-items" + EndPointsEnums.PURCHASE_INVOICES, (search, limit = 5) =>
        PurchaseInvoiceApiRepo.getPurchaseInvoice({ search, limit, getIsReturned: false }),
        { autoFetchOnMount: true }
    );

    const addReturnedPurchaseInvoice = useFlatMutate(
        (body) => ReturnedPurchaseInvoiceApiRepo.addReturnedPurchaseInvoice(body),
        {
            showDefaultSuccessToast: true,
            updateCached: { key: EndPointsEnums.RETURNED_PURCHASE_INVOICES, operation: "add" },
            closeModalOnSuccess: true,
            beforeStartMiddleware: () => AdminReturnedPurchaseInvoiceValidation.inputsValidation(state),
            onSuccess: async ({ args, data }) => {
                if (args[1]) await AdminReturnedPurchaseInvoiceService.handlePreview(data);
                AdminReturnedPurchaseInvoiceService.updatePurchaseInvoiceReturn(data.purchaseInvoiceId);
                refetchPurchaseInvoices();
            },
            afterEnd: () => resetState(),
        }
    );

    useEffect(() => {
        setState((prev) => ({
            ...prev,
            returnedPurchaseInvoiceProducts:
                AdminReturnedPurchaseInvoiceUtil.convertPurchaseInvoiceToReturnedPurchaseInvoice(
                    state.purchaseInvoice
                ).returnedPurchaseInvoiceProducts,
        }));
    }, [state.purchaseInvoice]);

    useEffect(() => {
        setState((prev) => ({
            ...prev,
            ...PurchaseUtils.getPurchaseData(
                prev.returnedPurchaseInvoiceProducts,
                prev.purchaseInvoice?.isPriceIncludingTax
            ),
        }));
    }, [state.returnedPurchaseInvoiceProducts]);

    const multiInputs = useMemo(() => {
        return AdminReturnedPurchaseInvoiceInputs(state.purchaseInvoice?.isPriceIncludingTax);
    }, [state.purchaseInvoice?.isPriceIncludingTax]);

    const handleOnPurchaseInvoiceProductsResult = (
        result: IAdminReturnedPurchaseInvoiceInputs[]
    ) => {
        setState((prev) => ({
            ...prev,
            returnedPurchaseInvoiceProducts:
                AdminReturnedPurchaseInvoiceUtil.handlePurchaseInvoiceProductsData(
                    result,
                    prev.purchaseInvoice?.isPriceIncludingTax
                ),
        }));
    };

    const handleViewedData = (
        item?: IPurchaseInvoiceModel
    ) => {
        return {
            invoiceTitle: item ? item.number + " - " + item.supplier?.name : undefined,
            date: item ? DateUtils.format(item.date, "dd/MM/yyyy hh.mm A") : undefined,
            dueDate: item ? DateUtils.format(item.dueDate, "dd/MM/yyyy hh.mm A") : undefined,
            isPriceIncludingTax: !item ? undefined : item.isPriceIncludingTax
                ? TranslateConstants.PRICE_INCLUDING_VAT
                : TranslateConstants.PRICE_EXCLUDING_VAT,
        }
    }

    return (
        <>
            <AddAndFilterComponent
                onSave={() => addReturnedPurchaseInvoice(state, false)}
                onSaveAndPrint={() => addReturnedPurchaseInvoice(state, true)}
                isSaveDisabled={!state.purchaseInvoice}
                isSaveAndPrintDisabled={!state.purchaseInvoice}
                onBack={() => setIsView(true)}
            />
            <StatusComponent
                isLoading={false}
                isError={false}
                height={isSm ? 7.5 : 8.1}
                className="!p-0"
            >
                <ListComponent
                    padding="0 px-2"
                    isBorder={true}
                    className="bg-base-100 !border-none"
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                        <DropDownSearchComponent
                            key={state.purchaseInvoice?.id}
                            label={TranslateConstants.INVOICE_NUMBER}
                            items={purchaseInvoices || []}
                            titleSelector={(item: IPurchaseInvoiceModel) => handleViewedData(item).invoiceTitle}
                            subtitleSelector={(item: IPurchaseInvoiceModel) => maxStringLength(item.note)}
                            isOutFilter={true}
                            onChange={(val: string) => {
                                if (isNaN(Number(val))) return;
                                refetchPurchaseInvoices(val, 5);
                            }}
                            defaultValue={handleViewedData(state.purchaseInvoice).invoiceTitle}
                            isLoading={purchaseInvoicesIsLoading}
                            isError={purchaseInvoicesIsError}
                            onSelect={(purchaseInvoice) => setState((prev) => ({ ...prev, purchaseInvoice }))}
                        />
                        <InputComponent
                            label={TranslateConstants.DATE}
                            type="fixed"
                            className="text-gray-400"
                            value={handleViewedData(state.purchaseInvoice).date}
                        />
                        <InputComponent
                            label={TranslateConstants.DUE_DATE}
                            type="fixed"
                            className="text-gray-400"
                            value={handleViewedData(state.purchaseInvoice).dueDate}
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.PAYMENT_METHOD}
                            items={PaymentMethodsConstant}
                            titleSelector={(item) => TranslateHelper.t(item.name)}
                            defaultValue={TranslateHelper.t(state.paymentMethod)}
                            isSearchable={false}
                            onSelect={(item) => setState({ ...state, paymentMethod: item.value })}
                        />
                        <InputComponent
                            label={TranslateConstants.SUPPLIER}
                            type="fixed"
                            className="text-gray-400"
                            value={state.purchaseInvoice?.supplier?.name}
                        />
                        <InputComponent
                            label={TranslateConstants.VAT_TYPE}
                            type="fixed"
                            className="text-gray-400"
                            value={handleViewedData(state.purchaseInvoice).isPriceIncludingTax}
                        />
                    </div>
                    <AdminMultiplePurchasesProductInputsComponent<IAdminReturnedPurchaseInvoiceInputs>
                        headers={AdminReturnedPurchaseInvoiceHeaders}
                        showDeleteButton={false}
                        showAddButton={false}
                        inputs={multiInputs}
                        onResult={handleOnPurchaseInvoiceProductsResult}
                        values={state.returnedPurchaseInvoiceProducts}
                        state={state}
                        setState={setState}
                        resetInputs={!state.returnedPurchaseInvoiceProducts.length}
                    />
                </ListComponent>
            </StatusComponent>
        </>
    );
};

export default AdminReturnedPurchaseInvoiceInputsFeature;
