import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { WrittenNumberHelper } from "../../../../common/helpers/WrittenNumberHelper";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { PaymentVoucherPdfPrinterContent } from "../../../../common/pdf-make/slices/paymentVoucher/PaymentVoucherPdfPrinterContent";
import { IPaymentVoucherPdfPrinterModel } from "../../../../common/pdf-make/slices/paymentVoucher/PaymentVoucherPdfPrinterModel";
import { PaymentVoucherReportPdfPrinterContent } from "../../../../common/pdf-make/slices/paymentVoucherReport/PaymentVoucherReportPdfPrinterContent";
import { IPaymentVoucherReportPdfPrinterModel } from "../../../../common/pdf-make/slices/paymentVoucherReport/PaymentVoucherReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminPaymentVouchersReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        paymentVouchers: IPaymentVoucherModel[] | undefined,
        paymentVouchersSum: IPaymentVoucherSumModel | undefined
    ) {
        const model: IPaymentVoucherReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: paymentVouchers?.map((el) => ({
                number: el.number.toString(),
                destination: TranslateHelper.t(el.destination),
                date: DateUtils.format(el.date),
                cash: el.cash,
                network: el.network,
                note: el.note || "",
            })),
            totals: {
                cash: paymentVouchersSum?.cash ?? 0,
                network: paymentVouchersSum?.network ?? 0,
                count: paymentVouchersSum?.count ?? 0,
            },
        };

        PdfMakeUtils.download(PaymentVoucherReportPdfPrinterContent(model), "Payment Vouchers Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static async handleOnPrint(paymentVouchers: IPaymentVoucherModel) {
        const model: IPaymentVoucherPdfPrinterModel = {
            date: DateUtils.format(paymentVouchers.date),
            number: paymentVouchers.number.toString(),
            destination: TranslateHelper.t(paymentVouchers.destination),
            amount: (paymentVouchers.cash + paymentVouchers.network).toString(),
            writtenAmount: WrittenNumberHelper.convert(paymentVouchers.cash + paymentVouchers.network),
            note: paymentVouchers.note || "",
        };

        PdfMakeUtils.preview(PaymentVoucherPdfPrinterContent(model), {
            headerType: "normal",
            header: await PdfMakeHeaders.normal(false, "normal"),
        });
    }
}
