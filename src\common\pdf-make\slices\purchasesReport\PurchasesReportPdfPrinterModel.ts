interface IPurchasesReportPdfPrinterItemModel {
    invoiceNumber: string;
    supplierName: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    cash: number;
    network: number;
    credit: number;
    date: string;
    dueDate: string;
}

export interface IPurchasesReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IPurchasesReportPdfPrinterItemModel[];
    totals: {
        subTotal: number;
        vat: number;
        total: number;
        cash: number;
        network: number;
        credit: number;
    };
}