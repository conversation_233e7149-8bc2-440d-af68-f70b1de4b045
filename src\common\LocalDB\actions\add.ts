import { ILocalDBDataWithId } from "../LocalDBInterface";

const add = async (
    db: IDBDatabase | undefined,
    collection: string,
    data: ILocalDBDataWithId,
    updateIfKeyExist: boolean = false
): Promise<boolean> => {
    return new Promise((resolve, reject) => {
        const transaction = db?.transaction(collection, "readwrite");
        const store = transaction?.objectStore(collection);
        let request: IDBRequest<IDBValidKey> | undefined = undefined;

        if (updateIfKeyExist) {
            request = store?.put(data);
        } else {
            request = store?.add(data);
        }

        if (request) {
            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = (e: any) => {
                reject(e.target.error);
            };
        }
    });
}

const addMany = async (
    db: IDBDatabase | undefined,
    collection: string,
    data: ILocalDBDataWithId[],
    updateIfKeyExist: boolean = false,
    batchSize: number = 1000
): Promise<boolean> => {
    return new Promise(async (resolve, reject) => {
        if (!data.length) {
            resolve(true);
        }

        const transaction = db?.transaction(collection, "readwrite");
        const store = transaction?.objectStore(collection);
        let i = 0;

        const addNext = () => {
            if (i < data.length) {
                const batch = data.slice(i, i + batchSize);
                i += batchSize;

                batch.forEach((item) => {
                    let request;

                    if (updateIfKeyExist) {
                        request = store?.put(item);
                    } else {
                        request = store?.add(item);
                    }

                    if (request) {
                        request.onsuccess = () => {
                            addNext();
                        };

                        request.onerror = (e: any) => {
                            reject(e.target.error);
                        };
                    }
                });
            } else {
                resolve(true);
            }
        };

        addNext();
    });
};

export const LocalDbAddAction = (
    db: IDBDatabase | undefined,
    collection: string,
    data: ILocalDBDataWithId | ILocalDBDataWithId[],
    updateIfKeyExist: boolean = false,
    batchSize: number = 1000
): Promise<boolean> => {
    if (Array.isArray(data)) {
        return addMany(db, collection, data, updateIfKeyExist, batchSize);
    } else {
        return add(db, collection, data, updateIfKeyExist);
    }
};