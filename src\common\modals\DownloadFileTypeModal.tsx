import DividerComponent from "../components/DividerComponent";
import IconButtonComponent from "../components/IconButtonComponent";
import { TranslateConstants } from "../constants/TranslateConstants";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { RiFileExcel2Line } from "react-icons/ri";
import { FaRegFilePdf } from "react-icons/fa6";
import { FC } from "react";
import useActions from "../redux/data/useActions";

interface IProps {
    onPdf?: () => void;
    onExcel?: () => void;
}

const DownloadFileTypeModal: FC<IProps> = ({
    onPdf,
    onExcel,
}) => {
    const actions = useActions();
    const handleOnClick = (fn?: () => void) => {
        fn?.();
        actions.closeModal();
    }

    return (
        <div className="px-2">
            <div className="text-center font-tajawal-bold my-1">
                {TranslateHelper.t(TranslateConstants.FILE_TYPE)}
            </div>
            <DividerComponent className="mb-2" />
            <div className="flex justify-between gap-2">
                {
                    onPdf && (
                        <IconButtonComponent
                            icon={<FaRegFilePdf />}
                            text={TranslateHelper.t(TranslateConstants.PDF)}
                            className="!h-20"
                            onClick={() => handleOnClick(onPdf)}
                        />
                    )
                }
                {
                    onExcel && (
                        <IconButtonComponent
                            icon={<RiFileExcel2Line />}
                            text={TranslateHelper.t(TranslateConstants.EXCEL)}
                            className="!h-20"
                            onClick={() => handleOnClick(onExcel)}
                        />
                    )
                }
            </div>
        </div>
    );
};

export default DownloadFileTypeModal;
