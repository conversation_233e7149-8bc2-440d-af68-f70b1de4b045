import { deliveryAppsConstant } from "../constants/CommonConstants";
import { deliveryAppsKeyConstant } from "../constants/ConfigConstants";
import { IDeliveryApp } from "../interfaces";
import { IDeliveryAppModel } from "../models/DeliveryAppModel";
import { stringConvert } from "../utils/StringUtils";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class DeliveryAppsHelper {
    static setDeliveryApps(data: IDeliveryAppModel) {
        LocalStorageHelper.set(deliveryAppsKeyConstant, data);
    }

    static getDeliveryApps(): IDeliveryAppModel | undefined {
        return LocalStorageHelper.get(deliveryAppsKeyConstant);
    }

    static getActiveDeliveryApps(
        items?: IDeliveryAppModel
    ): IDeliveryApp[] {
        const deliveryApps = items ?? this.getDeliveryApps();
        if (!deliveryApps) return [];

        return deliveryAppsConstant.filter((item) => (deliveryApps as any)[item.name] > 0);
    }

    static hasDeliveryApps(): boolean {
        const deliveryApps = this.getDeliveryApps();
        return !!deliveryApps && Object.values(deliveryApps).some((item) => item > 0);
    }

    static getDeliverAppKeys = (deliveryApp: string) => {
        const key = stringConvert(deliveryApp);
        const CapitalKey = stringConvert(deliveryApp, "CapitalCamel");
        const isActiveKey = `is${CapitalKey}Active`;

        return {
            key,
            isActiveKey,
        };
    }
}