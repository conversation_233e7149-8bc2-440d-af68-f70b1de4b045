import DrawerComponent from "../components/AdminDrawerComponent";
import GuardedRouteComponent from "../../../common/components/GuardedRouteComponent";
import ScrollToTopComponent from "../../../common/components/ScrollToTopComponent";
import AdminHeaderComponent from "../components/AdminHeaderComponent";
import SetRoutesComponent from "../../../common/components/SetRoutesComponent";
import OnlineStatusGuardComponent from "../../../common/components/OnlineStatusGuardComponent";

const AdminLayout = () => {
    return (
        <OnlineStatusGuardComponent>
            <GuardedRouteComponent authGuard={true}>
                <GuardedRouteComponent isAdminGuard={true}>
                    <>
                        <div className="drawer drawer-mobile">
                            <input
                                id="left-sidebar-drawer"
                                type="checkbox"
                                className="drawer-toggle"
                            />
                            <div className="drawer-content flex flex-col">
                                <AdminHeaderComponent />
                                <ScrollToTopComponent>
                                    <SetRoutesComponent type="admin" />
                                </ScrollToTopComponent>
                            </div>
                            <div className="drawer-side">
                                <label
                                    htmlFor="left-sidebar-drawer"
                                    className="drawer-overlay"
                                ></label>
                                <DrawerComponent />
                            </div>
                        </div>
                    </>
                </GuardedRouteComponent>
            </GuardedRouteComponent>
        </OnlineStatusGuardComponent>
    );
};

export default AdminLayout;
