import { NavigateFunction } from "react-router-dom";
import { RoutsConstants } from "../../../../common/constants/RoutesConstants";
import { ITableModel } from "../../../../common/models/TableModel";
import { IPosActions } from "../../redux/usePosActions";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";

export class PosTablesService {
    static handleViewTable = (
        table?: ITableModel,
        order?: IPosOrderModel,
        posActions?: IPosActions,
        navigate?: NavigateFunction
    ) => {
        const orderDate = table?.order ?? order;
        if (!orderDate) return;
        posActions?.setHome({ view: "categories" });
        posActions?.setOrder(orderDate);
        navigate?.(RoutsConstants.pos.home.fullPath);
    };
}
