import { Content } from "pdfmake/interfaces";
import { DateUtils } from "../../../utils/DateUtils";
import { IAdditionsReportPdfPrinterModel } from "./AdditionsReportPdfPrinterModel";

export const AdditionsReportPdfPrinterContent = (
    model: IAdditionsReportPdfPrinterModel
): Content[] => {
    return [
        {
            text: "تقرير الإضافات",
            style: { fontSize: 14, bold: true },
        },
        {
            text:
                DateUtils.format(model.endDate, "dd-MM-yyyy") +
                " - " +
                DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: ["*", "auto", "*", "auto", "auto", "*"],
                body: [
                    [
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        { text: "الإجمالي الفرعي", style: "tableHeader" },
                        { text: "الخصم", style: "tableHeader" },
                        { text: "الكمية", style: "tableHeader" },
                        { text: "الاسم", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.total,
                        el.vat,
                        el.subTotal,
                        el.discount,
                        el.quantity,
                        el.name,
                    ]),
                ],
            },
        },
    ];
};
