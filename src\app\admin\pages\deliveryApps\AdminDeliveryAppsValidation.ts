import {
    IAdminDeliveryAppInputs,
    IAdminDeliveryAppsAdditionItemProps,
    IAdminDeliveryAppsProductItemProps,
} from "./AdminDeliveryAppsInterface";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import {
    adminDeliveryAppsGetFormattedAdditions,
    adminDeliveryAppsGetFormattedProducts,
} from "./AdminDeliveryAppsUtils";
import { IProductModel } from "../../../../common/models/ProductModel";
import { IAdditionModel } from "../../../../common/models/AdditionsModel";

export class AdminDeliveryAppsValidation {
    static inputsValidation(inputs: IAdminDeliveryAppInputs): boolean {
        if (inputs.percentage === undefined || inputs.percentage < 0) {
            ToastHelper.error(
                TranslateHelper.t(TranslateConstants.PERCENTAGE_FIELD_IS_REQUIRED)
            );
            return false;
        }

        return true;
    }
}

export const isValidProducts = (
    items: IAdminDeliveryAppsProductItemProps[],
    products: IProductModel[] | undefined,
    deliveryApp: string
) => {
    if (!items.length) return false;

    const oldProducts = adminDeliveryAppsGetFormattedProducts(
        products,
        deliveryApp
    );
    if (JSON.stringify(oldProducts) === JSON.stringify(items)) return false;

    return (
        items.some((item) => item.active) &&
        items.every(
            (item) => !item.active || (item.active && !!item.priceInDeliveryApp)
        )
    );
};

export const isValidAdditions = (
    items: IAdminDeliveryAppsAdditionItemProps[],
    additions: IAdditionModel[] | undefined,
    deliveryApp: string
) => {
    if (!items.length) return false;

    const oldAdditions = adminDeliveryAppsGetFormattedAdditions(
        additions,
        deliveryApp
    );
    if (JSON.stringify(oldAdditions) === JSON.stringify(items)) return false;

    return (
        items.some((item) => item.active) &&
        items.every(
            (item) => !item.active || (item.active && !!item.priceInDeliveryApp)
        )
    );
};
