import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminSuppliersReportTableHeaders } from "./AdminSuppliersReportsConstants";
import AdminSuppliersReportTotalInfoFeature from "./features/AdminSuppliersReportTotalInfoFeature";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { useMemo } from "react";
import { AdminSuppliersReportService } from "./AdminSuppliersReportService";
import { SuppliersApiRepo } from "../../../../common/repos/api/SupplierApiRepo";
import { PurchaseInvoiceApiRepo } from "../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { ReturnedPurchaseInvoiceApiRepo } from "../../../../common/repos/api/ReturnedPurchaseInvoiceApiRepo";
import { ISupplierModel } from "../../../../common/models/SuppliersModel";
import { IAdminSuppliersReportDateInterface } from "./AdminSuppliersReportsInterface";
import { maxStringLength } from "../../../../common/utils/CommonUtils";
import { PurchaseInvoicePaymentApiRepo } from "../../../../common/repos/api/PurchaseInvoicePaymentApiRepo";

const AdminSuppliersReportPage = () => {
    const { isXs, isSm } = useScreenSize();

    const {
        data: suppliers,
        isLoading: supplierLoading,
        isError: supplierError,
        refetch: supplierRefetch,
    } = useFetch(
        "report-" + EndPointsEnums.SUPPLIERS,
        (search, limit = 5) => SuppliersApiRepo.getSuppliers(search, limit),
        { resetOnUnmount: true }
    );

    const {
        data: purchaseInvoiceSum,
        isLoading: purchaseInvoiceSumLoading,
        isError: purchaseInvoiceSumError,
        refetch: refetchPurchaseInvoiceSum,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.PURCHASE_INVOICES_SUM,
        (startTime, endTime, supplierId) => PurchaseInvoiceApiRepo.getSum(startTime, endTime, supplierId),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const {
        data: purchaseInvoices,
        isLoading: purchaseInvoicesLoading,
        isError: purchaseInvoicesError,
        refetch: refetchPurchaseInvoices,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.PURCHASE_INVOICES,
        (startTime, endTime, supplierId) => PurchaseInvoiceApiRepo.getPurchaseInvoice({
            startTime,
            endTime,
            supplierId,
            getPurchaseInvoiceProducts: false,
            getSupplier: false,
        }),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const {
        data: returnedPurchaseInvoiceSum,
        isLoading: returnedPurchaseInvoiceSumLoading,
        isError: returnedPurchaseInvoiceSumError,
        refetch: refetchReturnedPurchaseInvoiceSum,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES_SUM,
        (startTime, endTime, supplierId) => ReturnedPurchaseInvoiceApiRepo.getSum(startTime, endTime, supplierId),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const {
        data: returnedPurchaseInvoices,
        isLoading: returnedPurchaseInvoicesLoading,
        isError: returnedPurchaseInvoicesError,
        refetch: refetchReturnedPurchaseInvoices,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.RETURNED_PURCHASE_INVOICES,
        (startTime, endTime, supplierId) => ReturnedPurchaseInvoiceApiRepo.getReturnedPurchaseInvoices({
            startTime,
            endTime,
            supplierId,
            getPurchaseInvoice: false,
            getSuppliers: false,
        }),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const {
        data: purchaseInvoicePaymentsSum,
        isLoading: purchaseInvoicePaymentsSumLoading,
        isError: purchaseInvoicePaymentsSumError,
        refetch: refetchPurchaseInvoicePaymentsSum,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS_SUM,
        (startTime, endTime, supplierId) => PurchaseInvoicePaymentApiRepo.getSum(startTime, endTime, supplierId),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const {
        data: purchaseInvoicePayments,
        isLoading: purchaseInvoicePaymentsLoading,
        isError: purchaseInvoicePaymentsError,
        refetch: refetchPurchaseInvoicePayments,
    } = useFetch(
        "report-suppliers-" + EndPointsEnums.PURCHASE_INVOICE_PAYMENTS,
        (startTime, endTime, supplierId) => PurchaseInvoicePaymentApiRepo.getPurchaseInvoicePayments({
            startTime,
            endTime,
            supplierId,
        }),
        { autoFetchIfEmpty: false, resetOnUnmount: true }
    );

    const onDate = (
        startDate: Date = new Date(),
        endDate: Date = new Date(),
        supplier?: ISupplierModel
    ) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        refetchPurchaseInvoiceSum(start, end, supplier?.id);
        refetchPurchaseInvoices(start, end, supplier?.id);
        refetchReturnedPurchaseInvoiceSum(start, end, supplier?.id);
        refetchReturnedPurchaseInvoices(start, end, supplier?.id);
        refetchPurchaseInvoicePayments(start, end, supplier?.id);
        refetchPurchaseInvoicePaymentsSum(start, end, supplier?.id);
    };

    const data = useMemo(() => {
        return AdminSuppliersReportService.handleData(
            purchaseInvoices,
            returnedPurchaseInvoices,
            purchaseInvoicePayments,
        );
    }, [
        purchaseInvoices,
        returnedPurchaseInvoices,
        purchaseInvoicePayments,
    ]);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                showSearch={true}
                onDate={onDate}
                onSearch={(value) => {
                    if (value) supplierRefetch(value);
                }}
                items={suppliers}
                isSearchLoading={supplierLoading}
                isSearchError={supplierError}
                titleSelector={(item) => item.name}
                defaultValue={suppliers?.[0]?.name}
                defaultItem={suppliers?.[0]}
                subtitleSelector={(item) => item.mobile}
                disableSearchButtonByDefault={false}
            />
            <AdminSuppliersReportTotalInfoFeature
                purchaseInvoiceSum={purchaseInvoiceSum}
                returnedPurchaseInvoiceSum={returnedPurchaseInvoiceSum}
                purchaseInvoicePayments={purchaseInvoicePaymentsSum}
            />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={
                    purchaseInvoiceSumLoading ||
                    purchaseInvoicesLoading ||
                    returnedPurchaseInvoiceSumLoading ||
                    returnedPurchaseInvoicesLoading ||
                    purchaseInvoicePaymentsLoading ||
                    purchaseInvoicePaymentsSumLoading
                }
                isError={
                    purchaseInvoiceSumError ||
                    purchaseInvoicesError ||
                    returnedPurchaseInvoiceSumError ||
                    returnedPurchaseInvoicesError ||
                    purchaseInvoicePaymentsError ||
                    purchaseInvoicePaymentsSumError
                }
                height={isXs ? 14.5 : isSm ? 12.4 : 12.6}
            >
                <TableComponent
                    headers={AdminSuppliersReportTableHeaders}
                    items={data || []}
                    selectors={(item: IAdminSuppliersReportDateInterface) => [
                        DateUtils.format(item.date),
                        item.type,
                        maxStringLength(item.note, 40),
                        item.debit.toFixed(2),
                        item.credit.toFixed(2),
                        item.deferred.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminSuppliersReportPage;
