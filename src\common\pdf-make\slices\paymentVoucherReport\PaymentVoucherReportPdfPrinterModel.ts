export interface IPaymentVoucherReportPdfPrinterItemModel {
    number: string;
    destination: string;
    date: string;
    cash: number;
    network: number;
    note: string;
}

export interface IPaymentVoucherReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IPaymentVoucherReportPdfPrinterItemModel[];
    totals: {
        cash: number;
        network: number;
        count: number;
    };
}