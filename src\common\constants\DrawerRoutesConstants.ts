import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { RoutsConstants } from "./RoutesConstants";
import { TranslateConstants } from "./TranslateConstants";

export const DrawerRoutesConstants = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    return [
        {
            path: RoutsConstants.admin.dashboard.fullPath,
            name: TranslateConstants.DASHBOARD,
        },
        {
            name: TranslateConstants.PRODUCTS_MENU,
            submenu: [
                {
                    path: RoutsConstants.admin.categories.fullPath,
                    name: TranslateConstants.CATEGORIES,
                },
                {
                    path: RoutsConstants.admin.additions.fullPath,
                    name: TranslateConstants.ADDITIONS,
                },
                {
                    path: RoutsConstants.admin.products.fullPath,
                    name: TranslateConstants.PRODUCTS,
                },
            ]
        },
        {
            name: TranslateConstants.ADVANCED_OPTIONS,
            submenu: [
                {
                    path: RoutsConstants.admin.tables.fullPath,
                    name: TranslateConstants.TABLES,
                },
                {
                    path: RoutsConstants.admin.discounts.fullPath,
                    name: TranslateConstants.DISCOUNTS,
                },
                {
                    path: RoutsConstants.admin.customers.fullPath,
                    name: TranslateConstants.CUSTOMERS,
                },
                {
                    path: RoutsConstants.admin.deliveryApps.fullPath,
                    name: TranslateConstants.DELIVERY_APPS,
                },
            ]
        },
        {
            name: TranslateConstants.PURCHASES_AND_SUPPLIERS,
            submenu: [
                {
                    path: RoutsConstants.admin.suppliers.fullPath,
                    name: TranslateConstants.SUPPLIERS,
                },
                {
                    path: RoutsConstants.admin.rawMaterials.fullPath,
                    name: TranslateConstants.RAW_MATERIALS,
                },
                {
                    path: RoutsConstants.admin.purchaseOrder.fullPath,
                    name: TranslateConstants.PURCHASE_ORDER,
                },
                {
                    path: RoutsConstants.admin.purchasesInvoices.fullPath,
                    name: TranslateConstants.PURCHASES_INVOICES,
                },
                {
                    path: RoutsConstants.admin.returnedPurchasesInvoices.fullPath,
                    name: TranslateConstants.RETURNED_PURCHASES_INVOICES,
                },
                {
                    path: RoutsConstants.admin.purchasesReport.fullPath,
                    name: TranslateConstants.PURCHASES_REPORT,
                },
                {
                    path: RoutsConstants.admin.returnedPurchasesInvoicesReport.fullPath,
                    name: TranslateConstants.RETURNED_PURCHASES_INVOICES_REPORT,
                },
                {
                    path: RoutsConstants.admin.suppliersReport.fullPath,
                    name: TranslateConstants.SUPPLIERS_REPORT,
                },
            ]
        },
        {
            name: TranslateConstants.SIMPLE_ACCOUNTING,
            submenu: [
                {
                    path: RoutsConstants.admin.receiptVoucher.fullPath,
                    name: TranslateConstants.RECEIPT_VOUCHER,
                },
                {
                    path: RoutsConstants.admin.paymentVoucher.fullPath,
                    name: TranslateConstants.PAYMENT_VOUCHER,
                },
                {
                    path: RoutsConstants.admin.receiptVoucherReport.fullPath,
                    name: TranslateConstants.RECEIPT_VOUCHER_REPORT,
                },
                {
                    path: RoutsConstants.admin.paymentVoucherReport.fullPath,
                    name: TranslateConstants.PAYMENT_VOUCHER_REPORT,
                },
                {
                    path: RoutsConstants.admin.cashReport.fullPath,
                    name: TranslateConstants.CASH_REPORT,
                },
                {
                    path: RoutsConstants.admin.bankReport.fullPath,
                    name: TranslateConstants.BANK_REPORT,
                },
                {
                    path: RoutsConstants.admin.profitAndLossReport.fullPath,
                    name: TranslateConstants.PROFIT_AND_LOSS_REPORT,
                },
                {
                    path: RoutsConstants.admin.taxReport.fullPath,
                    name: TranslateConstants.TAX_REPORT,
                },
            ]
        },
        {
            name: TranslateConstants.REPORTS,
            submenu: [
                {
                    path: RoutsConstants.admin.shiftReport.fullPath,
                    name: TranslateConstants.SHIFT_REPORT,
                },
                {
                    path: RoutsConstants.admin.salesReport.fullPath,
                    name: TranslateConstants.SALES_REPORT,
                },
                {
                    path: RoutsConstants.admin.returnedSalesReport.fullPath,
                    name: TranslateConstants.RETURNED_SALES_REPORT,
                },
                {
                    path: RoutsConstants.admin.mostSellingProductsReport.fullPath,
                    name: TranslateConstants.MOST_SELLING_PRODUCTS_REPORT,
                },
                {
                    path: RoutsConstants.admin.additionsReport.fullPath,
                    name: TranslateConstants.ADDITIONS_REPORT,
                },
                ...(hasTobaccoTax ? [{
                    path: RoutsConstants.admin.tobaccoTaxReport.fullPath,
                    name: TranslateConstants.TOBACCO_TAX_REPORT,
                }] : []),
                {
                    path: RoutsConstants.admin.customersReport.fullPath,
                    name: TranslateConstants.CUSTOMERS_REPORT,
                },
                {
                    path: RoutsConstants.admin.deliveryAppsReport.fullPath,
                    name: TranslateConstants.DELIVERY_APPS_REPORT,
                },
            ]
        },
        {
            path: RoutsConstants.admin.settings.fullPath,
            name: TranslateConstants.SETTINGS,
        },
    ];
}