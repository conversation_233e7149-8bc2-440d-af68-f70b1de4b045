import { invoiceNumberKeyConstant } from "../constants/ConfigConstants";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class OrderHelper {
    static getNextInvoiceNumber(): string {
        const invoiceNumber: number = LocalStorageHelper.get(invoiceNumberKeyConstant) || 0;
        return (invoiceNumber + 1).toString();
    }

    static setInvoiceNumber(invoiceNumber: number): void {
        LocalStorageHelper.set(invoiceNumberKeyConstant, invoiceNumber);
    }

    static incrementInvoiceNumber(): void {
        const invoiceNumber: number = parseInt(this.getNextInvoiceNumber()) - 1;
        this.setInvoiceNumber(invoiceNumber + 1);
    }
}