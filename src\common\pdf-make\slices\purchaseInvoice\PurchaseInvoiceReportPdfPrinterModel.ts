interface IPurchaseInvoicePdfPrinterItemModel {
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IPurchaseInvoicePdfPrinterModel {
    items?: IPurchaseInvoicePdfPrinterItemModel[];
    note?: string;
    totals: {
        nativeTotal: number;
        productsDiscount: number;
        subTotal: number;
        vat: number;
        total: number;
    };
}