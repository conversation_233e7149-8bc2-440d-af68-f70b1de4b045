import { FC, useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import SplashScreenComponent from "./SplashScreenComponent";
import { RoutsConstants } from "../constants/RoutesConstants";
import { <PERSON>iesHelper } from "../helpers/CookiesHelper";
import { accessTokenKeyConstant } from "../constants/ConfigConstants";
import { OrganizationHelper } from "../helpers/OrganizationHelper";

const AuthGuardComponent = ({
    children,
    authGuard = true,
    notAuthGuard = true,
}: {
    children: JSX.Element;
    authGuard?: boolean;
    notAuthGuard?: boolean;
}) => {
    const [isAuth, setIsAuth] = useState<boolean | null>(null);

    const pathToRedirect = notAuthGuard
        ? RoutsConstants.role.path
        : RoutsConstants.auth.path;

    const checkIsAuth = !!CookiesHelper.get(accessTokenKeyConstant);

    useEffect(() => setIsAuth(checkIsAuth), [checkIsAuth]);

    if (isAuth === null) {
        return <SplashScreenComponent />;
    }

    if ((!isAuth && authGuard) || (isAuth && notAuthGuard)) {
        return <Navigate to={pathToRedirect} />;
    }

    return <>{children}</>;
};

const RoleGuardComponent = ({
    children,
    isAdminGuard = false,
    isPosGuard = false,
}: {
    children: JSX.Element;
    isAdminGuard?: boolean;
    isPosGuard?: boolean;
}) => {
    const isAdmin = OrganizationHelper.isAdmin();
    const isPos = OrganizationHelper.isPos();

    if ((isAdminGuard && !isAdmin) || (isPosGuard && !isPos)) {
        return <Navigate to={RoutsConstants.role.path} />;
    }

    return <>{children}</>;
};

interface GuardedRouteComponentProps {
    children: JSX.Element;
    guard?: boolean;
    authGuard?: boolean;
    notAuthGuard?: boolean;
    pathToRedirect?: string;
    isAdminGuard?: boolean;
    isPosGuard?: boolean;
}

const GuardedRouteComponent: FC<GuardedRouteComponentProps> = ({
    children,
    guard = true,
    authGuard = false,
    notAuthGuard = false,
    pathToRedirect = RoutsConstants.auth.path,
    isAdminGuard = false,
    isPosGuard = false,
}) => {
    if (authGuard || notAuthGuard) {
        return (
            <AuthGuardComponent authGuard={authGuard} notAuthGuard={notAuthGuard}>
                {children}
            </AuthGuardComponent>
        );
    }

    if (isAdminGuard || isPosGuard) {
        return (
            <RoleGuardComponent isAdminGuard={isAdminGuard} isPosGuard={isPosGuard}>
                {children}
            </RoleGuardComponent>
        );
    }

    return <>{guard ? children : <Navigate to={pathToRedirect} replace />}</>;
};

export default GuardedRouteComponent;
