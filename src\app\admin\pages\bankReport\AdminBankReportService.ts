import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import { IPaymentVoucherSumModel } from "../../../../common/models/PaymentVoucherSumModel";
import { IPurchaseInvoicePaymentModel } from "../../../../common/models/PurchaseInvoicePaymentModel";
import { IPurchaseInvoiceSumModel } from "../../../../common/models/PurchaseInvoiceSumModel";
import { IReceiptVoucherModel } from "../../../../common/models/ReceiptVoucherModel";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";
import { IReturnedOrderSumModel } from "../../../../common/models/ReturnedOrderSumModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { BankReportPdfPrinterContent } from "../../../../common/pdf-make/slices/bankReport/BankReportPdfPrinterContent";
import { IBankReportPdfPrinterModel } from "../../../../common/pdf-make/slices/bankReport/BankReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { IBankReportInterface, IBankReportTotalInfoInterface } from "./AdminBankReportInterface";

export class AdminBankReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        data: IBankReportInterface[],
        totals: IBankReportTotalInfoInterface,
    ) {
        const model: IBankReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: data?.map((el) => ({
                date: DateUtils.format(el.date),
                number: el.number,
                type: el.type,
                bank: el.bank,
            })),
            totals: {
                ordersCount: totals.ordersCount,
                returnedCount: totals.returnedCount,
                paymentVoucherCount: totals.paymentVoucherCount,
                receiptVoucherCount: totals.receiptVoucherCount,
                total: totals.total,
            },
        };

        PdfMakeUtils.download(BankReportPdfPrinterContent(model), "Bank Report", {
            isLandScape: true,
            header: await PdfMakeHeaders.normal(true),
        });
    }

    static handleTotalInfo(
        orderSum: IOrderSumModel | undefined,
        returnedOrderSum: IReturnedOrderSumModel | undefined,
        purchaseInvoicePaymentsSum: IPurchaseInvoiceSumModel | undefined,
        paymentVouchersSum: IPaymentVoucherSumModel | undefined,
        receiptVouchersSum: IPaymentVoucherSumModel | undefined,
    ): IBankReportTotalInfoInterface {
        return {
            ordersCount: orderSum?.count ?? 0,
            returnedCount: returnedOrderSum?.count ?? 0,
            paymentVoucherCount: paymentVouchersSum?.count ?? 0,
            receiptVoucherCount: receiptVouchersSum?.count ?? 0,
            total: ((orderSum?.network ?? 0) + (receiptVouchersSum?.network ?? 0) - ((returnedOrderSum?.network ?? 0) + (purchaseInvoicePaymentsSum?.network ?? 0) + (paymentVouchersSum?.network ?? 0))),
        };
    }

    static handleData(
        orders: IOrderModel[] = [],
        returnedOrders: IReturnedOrderModel[] = [],
        purchaseInvoicesPayments: IPurchaseInvoicePaymentModel[] = [],
        paymentVouchers: IPaymentVoucherModel[] = [],
        receiptVouchers: IReceiptVoucherModel[] = [],
    ): IBankReportInterface[] {
        return [
            ...orders.map((el) => ({
                number: el.invoiceNumber,
                type: TranslateHelper.t(TranslateConstants.SELL),
                bank: el.network,
                date: el.startTime,
            })),
            ...returnedOrders.map((el) => ({
                number: el.returnedInvoiceNumber,
                type: TranslateHelper.t(TranslateConstants.RETURNED),
                bank: el.network,
                date: el.startTime,
            })),
            ...purchaseInvoicesPayments.map((el) => ({
                number: el.id,
                type: TranslateHelper.t(TranslateConstants.PURCHASE_INVOICE_PAYMENT) + " - " + el.purchaseInvoiceId,
                bank: el.network,
                date: el.date,
            })),
            ...paymentVouchers.map((el) => ({
                number: el.number,
                type: TranslateHelper.t(TranslateConstants.PAYMENT_VOUCHER) + (el.destination ? (" - " + TranslateHelper.t(el.destination)) : ""),
                bank: el.network,
                date: el.date,
            })),
            ...receiptVouchers.map((el) => ({
                number: el.number,
                type: TranslateHelper.t(TranslateConstants.RECEIPT_VOUCHER) + (el.destination ? (" - " + TranslateHelper.t(el.destination)) : ""),
                bank: el.network,
                date: el.date,
            })),
        ].sort((a, b) => b.date - a.date);
    }
}
