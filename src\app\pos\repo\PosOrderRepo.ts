import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IOrderBody } from "../../../common/interfaces/body/orderBody";
import {
    ILocalDBGetOptionsProperties,
    IOperator,
} from "../../../common/LocalDB/LocalDBInterface";
import { IPosOrderModel } from "../../../common/models/PosOrderModel";
import { debug } from "../../../common/utils/CommonUtils";
import { IPosOrder } from "../interface";
import { OrderStatusEnum } from "../../../common/enums/DataEnums";
import { IPosReturnedOrderModel } from "../../../common/models/PosReturnedOrderModel";

export class PosOrderRepo {
    static addOrder = async (order: IPosOrder): Promise<IPosOrderModel> => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");

            const body: IPosOrderModel = {
                ...order,
                id: AppLocalDB.generateId(),
                shiftId: shift.shiftId,
                deleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            if (!body.orderProducts.length) throw new Error("No products found");
            await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.ORDERS, body);
            return body;
        } catch (error) {
            debug(`PosOrderRepo [addOrder] Error: ${error}`);
            throw error;
        }
    };

    static getAndCountOrders = async (
        where?: [keyof IPosOrderModel, IOperator, any]
    ) => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [], count: 0 };
            }

            const orderWere: [keyof IPosOrderModel, IOperator, any][] = [
                [`shiftId`, "==", shift.shiftId],
            ];
            if (where) orderWere.push(where);

            const res = await AppLocalDB.getAndCount<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                {
                    where: [orderWere],
                }
            );
            return res;
        } catch (error) {
            debug(`PosOrderRepo [getOrdersCount] Error: ${error}`);
            throw error;
        }
    };

    static getShiftOrders = async (): Promise<IPosOrderModel[]> => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) return [];

            const res = await AppLocalDB.get<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                {
                    orderBy: ["orderNumber", "desc"],
                    where: [[[`shiftId`, "===", shift.shiftId]]],
                }
            );
            return res;
        } catch (error) {
            debug(`PosOrderRepo [getOrders] Error: ${error}`);
            throw error;
        }
    };

    static updateOrder = async (
        order: IPosOrderModel
    ): Promise<IPosOrderModel> => {
        try {
            const body: IPosOrderModel = {
                ...order,
                updatedAt: new Date(),
            };
            await AppLocalDB.update(APP_LOCAL_DB_COLLECTIONS.ORDERS, order.id, body);
            return order;
        } catch (error) {
            debug(`PosOrderRepo [updateOrder] Error: ${error}`);
            throw error;
        }
    };

    static async updateOrderStatus(
        order: IPosOrderModel,
        status: OrderStatusEnum = OrderStatusEnum.RETURNED
    ) {
        try {
            await AppLocalDB.update(APP_LOCAL_DB_COLLECTIONS.ORDERS, order.id, {
                ...order,
                status,
                updatedAt: new Date(),
            });
        } catch (error) {
            debug(`PosOrderRepo [updateOrderStatus] Error: ${error}`);
            throw error;
        }
    }

    static async getRelatedReturnedOrder(
        order: IPosOrderModel
    ): Promise<IPosReturnedOrderModel | undefined> {
        try {
            const res = await AppLocalDB.getOne<IPosReturnedOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS,
                ["orderId", "==", order.orderId]
            );

            return res;
        } catch (error) {
            debug(`PosReturnedOrdersRepo [getReturnedOrder] Error: `, error);
            throw error;
        }
    }

    static async uploadOrders(body: IOrderBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsEnums.ORDERS, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosOrderRepo [uploadOrders] Error: `, error);
            throw error;
        }
    }

    static async ordersCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.ORDERS);
        } catch (error) {
            debug(`PosOrderRepo [ordersCount] Error: `, error);
            throw error;
        }
    }

    static async getOrders(
        options?: ILocalDBGetOptionsProperties<IPosOrderModel>
    ) {
        try {
            return await AppLocalDB.get<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                options
            );
        } catch (error) {
            debug(`PosOrderRepo [getOrders] Error: `, error);
            throw error;
        }
    }

    static async deleteAllOrders() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.ORDERS);
        } catch (error) {
            debug(`PosOrderRepo [deleteAllOrders] Error: `, error);
            throw error;
        }
    }

    static async getOrdersCount() {
        try {
            const res = await AxiosHelper.get<number>(
                EndPointsEnums.ORDERS_COUNT
            );

            if (!res.success) throw new Error(res.message);

            return res.data || 0;
        } catch (error) {
            debug(`PosOrderRepo [ordersCount] Error: `, error);
            throw error;
        }
    }

    static async deleteOrder(id: number) {
        try {
            await AppLocalDB.deleteById(APP_LOCAL_DB_COLLECTIONS.ORDERS, id);
        } catch (error) {
            debug(`PosOrderRepo [deleteOrder] Error: `, error);
            throw error;
        }
    }
}
