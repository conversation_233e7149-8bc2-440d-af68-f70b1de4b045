import { FC } from "react";
import TitledCardComponent from "../../../../../common/components/TitledCardComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { Line } from "react-chartjs-2";

interface IProps {
    ordersRushHours?: { hour: number; count: number; }[];
}

const AdminDashboardRushHoursFeature: FC<IProps> = ({
    ordersRushHours,
}) => {
    const lineData = {
        labels: ordersRushHours?.map((el) => el.hour),
        datasets: [
            {
                fill: true,
                data: ordersRushHours?.map((el) => el.count),
                borderColor: "rgb(53, 162, 235)",
                backgroundColor: "rgba(53, 162, 235, 0.5)",
            },
        ],
    }

    return (
        <TitledCardComponent title={TranslateConstants.RUSH_HOURS}>
            <div className="flex justify-center items-center h-full">
                <Line
                    options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false,
                            },
                        },
                    }}
                    data={lineData}
                />
            </div>
        </TitledCardComponent>
    );
}

export default AdminDashboardRushHoursFeature;
