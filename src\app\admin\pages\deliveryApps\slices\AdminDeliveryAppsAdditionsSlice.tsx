import { Dispatch, FC, SetStateAction, useEffect } from "react";
import { IAdditionModel } from "../../../../../common/models/AdditionsModel";
import TableComponent from "../../../../../common/components/TableComponent";
import { AdminDeliveryAppsAdditionsTableHeaders } from "../AdminDeliveryAppsConstants";
import { IAdminDeliveryAppsAdditionItemProps } from "../AdminDeliveryAppsInterface";
import { adminDeliveryAppsGetFormattedAdditions } from "../AdminDeliveryAppsUtils";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    additions?: IAdditionModel[];
    formatAdditions: IAdminDeliveryAppsAdditionItemProps[];
    setFormatAdditions: Dispatch<SetStateAction<IAdminDeliveryAppsAdditionItemProps[]>>;
    deliveryApp: string;
}

const AdminDeliveryAppsAdditionsSlice: FC<IProps> = ({
    additions,
    formatAdditions,
    setFormatAdditions,
    deliveryApp,
}) => {
    useEffect(() => {
        if (!formatAdditions.length) {
            const newAdditions = adminDeliveryAppsGetFormattedAdditions(additions, deliveryApp);
            setFormatAdditions(newAdditions);
        }
    }, [additions]);

    const updateAdditionField = <
        K extends keyof IAdminDeliveryAppsAdditionItemProps
    >(
        item: IAdminDeliveryAppsAdditionItemProps,
        key: K,
        value: IAdminDeliveryAppsAdditionItemProps[K]
    ) => {
        setFormatAdditions((prev) =>
            prev.map((i) => {
                if (i.additionId !== item.additionId) return i;
                return { ...i, [key]: value };
            })
        );
    };

    const handleChangePrice = (
        priceInDeliveryApp: number,
        item: IAdminDeliveryAppsAdditionItemProps
    ) => {
        updateAdditionField(item, "priceInDeliveryApp", priceInDeliveryApp);
        const isActive = !!priceInDeliveryApp && priceInDeliveryApp !== item.priceInDeliveryApp;
        updateAdditionField(item, "active", isActive);
    };

    const handleOnActive = (
        active: boolean,
        item: IAdminDeliveryAppsAdditionItemProps
    ) => {
        updateAdditionField(item, "active", active);
    };

    return (
        <TableComponent
            headers={AdminDeliveryAppsAdditionsTableHeaders}
            items={formatAdditions || []}
            showNumbering={true}
            selectors={(item: IAdminDeliveryAppsAdditionItemProps) => {
                return [
                    item.name,
                    item.price,
                    <input
                        type="number"
                        className="w-28 border border-gray-400 rounded p-2 text-center bg-base-100"
                        placeholder={item.price.toString()}
                        onChange={(e) => handleChangePrice(fixedNumber(e.target.value), item)}
                        value={item.priceInDeliveryApp?.toString() || ""}
                    />,
                ];
            }}
            showActiveButton={true}
            activeSelector={(item: IAdminDeliveryAppsAdditionItemProps) => item.active}
            showActiveButtonBorder={false}
            onActive={(active: boolean, item: IAdminDeliveryAppsAdditionItemProps) => {
                handleOnActive(active, item);
            }}
        />
    );
}

export default AdminDeliveryAppsAdditionsSlice;
