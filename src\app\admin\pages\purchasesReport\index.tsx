import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminPurchasesReportTableHeaders } from "./AdminPurchasesReportsConstants";
import AdminPurchasesReportTotalInfoFeature from "./features/AdminPurchasesReportTotalInfoFeature";
import { AdminPurchasesReportService } from "./PurchasesReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { PurchaseInvoiceApiRepo } from "../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { AdminPurchaseInvoiceService } from "../purchaseInvoice/AdminPurchaseInvoiceService";

const AdminPurchasesReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: purchaseInvoicesSum,
        isLoading: purchaseInvoicesSumLoading,
        isError: purchaseInvoicesSumError,
        refetch: refetchPurchaseInvoicesSum,
    } = useFetch(
        "report-" + EndPointsEnums.PURCHASE_INVOICES_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoiceApiRepo.getSum(startTime, endTime),
        { autoFetchOnMount: true }
    );

    const {
        data: purchaseInvoices,
        isLoading: purchaseInvoicesLoading,
        isError: purchaseInvoicesError,
        refetch: refetchPurchaseInvoices,
    } = useFetch(
        "report-" + EndPointsEnums.PURCHASE_INVOICES,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PurchaseInvoiceApiRepo.getPurchaseInvoice({ startTime, endTime }),
        { autoFetchOnMount: true }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        setDateRange({ startTime: start, endTime: end });
        refetchPurchaseInvoicesSum(start, end);
        refetchPurchaseInvoices(start, end);
    };

    const onDownload = () =>
        AdminPurchasesReportService.handleDownload(
            dateRange,
            purchaseInvoices,
            purchaseInvoicesSum
        );

    const handleOnPrint = (item: IPurchaseInvoiceModel) =>
        AdminPurchaseInvoiceService.handlePreview(item);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!purchaseInvoices?.length || !purchaseInvoicesSum}
            />
            <AdminPurchasesReportTotalInfoFeature
                purchaseInvoicesSum={purchaseInvoicesSum}
            />
            <StatusComponent
                isEmpty={!purchaseInvoices?.length}
                isLoading={purchaseInvoicesSumLoading || purchaseInvoicesLoading}
                isError={purchaseInvoicesSumError || purchaseInvoicesError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminPurchasesReportTableHeaders}
                    items={purchaseInvoices || []}
                    selectors={(item: IPurchaseInvoiceModel) => [
                        item.number.toString(),
                        item.supplier?.name,
                        item.subTotal.toFixed(2),
                        item.productsDiscount.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        item.deferred.toFixed(2),
                    ]}
                    showPrintButton={true}
                    onPrint={handleOnPrint}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminPurchasesReportPage;
