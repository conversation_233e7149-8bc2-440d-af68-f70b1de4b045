export interface IProfitAndLossReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    data: {
        ordersTotal: number;
        returnedOrdersTotal: number;
        purchaseInvoicesTotal: number;
        returnedPurchaseInvoicesTotal: number;
        paymentVouchersTotal: number;
        paymentVouchersSum?: {
            destination: string;
            total: number;
        }[];
    }
}