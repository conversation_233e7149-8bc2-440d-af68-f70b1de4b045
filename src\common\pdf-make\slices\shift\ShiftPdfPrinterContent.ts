import { Content } from 'pdfmake/interfaces';
import { IShiftPdfPrinterBodyModel } from './ShiftPdfPrinterModel';
import { PdfMakeHelper } from '../../PdfMakeHelper';
import { DateUtils } from '../../../utils/DateUtils';
import { DefaultPdfMakePrinterContentOptions } from '../../PdfMakeConstants';
import { IPdfMakePrinterContentOptionsModel } from '../../PdfMakeInterfaces';

export const ShiftPdfPrinterContent = (
    invoice: IShiftPdfPrinterBodyModel,
    options: IPdfMakePrinterContentOptionsModel = DefaultPdfMakePrinterContentOptions,
): Content[] => {
    const defaultOptions: IPdfMakePrinterContentOptionsModel = { ...DefaultPdfMakePrinterContentOptions, ...options };

    return [
        ...PdfMakeHelper.optionalDocItem(!!defaultOptions?.showLogo && !!defaultOptions.logo, [
            {
                image: defaultOptions.logo!,
                width: 150,
                height: 100,
            },
        ]),
        {
            text: 'إغلاق الوردية',
            bold: true,
            fontSize: 12,
            marginBottom: 5,
            marginTop: 5,
        },

        PdfMakeHelper.multiText([
            { text: ". Sales Summary", alignment: "left" },
            { text: ". ملخص المبيعات", alignment: "right" },
        ], true, 10),
        PdfMakeHelper.multiText([
            { text: "Invoices", alignment: "left" },
            { text: invoice.ordersCount.toString(), alignment: "center", width: "auto" },
            { text: "عدد الفواتير", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Returned", alignment: "left" },
            { text: invoice.returnedOrdersCount.toString(), alignment: "center", width: "auto" },
            { text: "عدد المرتجعات", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "T.Discount", alignment: "left" },
            { text: invoice.discountAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي الخصم", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "T.Sales", alignment: "left" },
            { text: invoice.totalAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي المبيعات", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "T.Returned", alignment: "left" },
            { text: invoice.totalReturnedAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي المرتجعات", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Net", alignment: "left" },
            { text: invoice.totalNetAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "الصافي", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Cash", alignment: "left" },
            { text: invoice.cashAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات كاش", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Network", alignment: "left" },
            { text: invoice.networkAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات شبكة", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Deferred", alignment: "left" },
            { text: invoice.deferredAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "اجل", alignment: "right" },
        ]),

        invoice.paymentAmounts.map((payment) => {
            return [
                ...PdfMakeHelper.optionalDocItem(payment.amount > 0, [
                    PdfMakeHelper.printDivider(),
                    PdfMakeHelper.multiText([
                        { text: payment.nameEn, alignment: "left" },
                        { text: payment.amount.toFixed(2), alignment: "center", width: "auto" },
                        { text: payment.name, alignment: "right" },
                    ]),
                ])]
        }),

        PdfMakeHelper.multiText([
            { text: ". Accounting", alignment: "left" },
            { text: ". الحركة المالية", alignment: "right" },
        ], true, 10),
        PdfMakeHelper.multiText([
            { text: "Start Shift", alignment: "left" },
            { text: invoice.startAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد بداية الوردية", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Added To Shift", alignment: "left" },
            { text: invoice.addedToShiftAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إضافة إلى العهدة", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Customer Receipts", alignment: "left" },
            { text: invoice.customerReceiptsAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "قبض عميل", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Pulls", alignment: "left" },
            { text: invoice.pullsAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "السحوبات", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Cash Net", alignment: "left" },
            { text: invoice.cashNetAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "صافي كاش", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Network Net", alignment: "left" },
            { text: invoice.networkNetAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "صافي شبكة", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Close Shift", alignment: "left" },
            { text: invoice.endAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد الإغلاق", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Addition", alignment: "left" },
            { text: invoice.additionAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "الزيادة", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "Shortage", alignment: "left" },
            { text: invoice.shortageAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "العجز", alignment: "right" },
        ]),

        PdfMakeHelper.multiText([
            { text: ". Other", alignment: "left" },
            { text: ". أخرى", alignment: "right" },
        ], true, 10),
        PdfMakeHelper.multiText([
            { text: "Start Time", alignment: "left" },
            {
                text: DateUtils.format(invoice.startTime, "dd-MM-yyyy hh:mm A", true),
                alignment: "center",
                width: "auto"
            },
            { text: "وقت فتح الوردية", alignment: "right" },
        ]),
        PdfMakeHelper.printDivider(),
        PdfMakeHelper.multiText([
            { text: "End Time", alignment: "left" },
            {
                text: DateUtils.format(invoice.endTime, "dd-MM-yyyy hh:mm A", true),
                alignment: "center",
                width: "auto"
            },
            { text: "وقت غلق الوردية", alignment: "right" },
        ]),
    ];
};
