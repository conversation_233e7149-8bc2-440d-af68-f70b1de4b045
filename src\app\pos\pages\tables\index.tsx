import { useNavigate } from "react-router-dom";
import ListComponent from "../../../../common/components/ListComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TableStatusEnum } from "../../../../common/enums/DataEnums";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import { ITableModel } from "../../../../common/models/TableModel";
import { useAppSelector } from "../../../../common/redux/store";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import PosTableCardComponent from "../../components/PosTableCardComponent";
import { posTablesSelector } from "../../redux/selector";
import usePosActions from "../../redux/usePosActions";
import EmptyComponent from "../../../../common/components/EmptyComponent";
import { useMemo } from "react";
import { PosTablesService } from "./PosTablesService";

const PosTablesPage = () => {
    const posActions = usePosActions();
    const { translate } = useTranslate();
    const tables = useAppSelector(posTablesSelector);
    const navigate = useNavigate();

    const handleOnTableClick = (table: ITableModel) =>
        PosTablesService.handleViewTable(table, undefined, posActions, navigate);

    const isTablesEmpty = useMemo(() => {
        return (
            !tables.length ||
            tables.every((table) => table.status !== TableStatusEnum.OCCUPIED)
        );
    }, [tables]);

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="font-tajawal-bold text-2xl">
                {translate(TranslateConstants.TABLES)}
            </div>
            {isTablesEmpty && (
                <div
                    style={{ height: `calc(100svh - 12rem)` }}
                    className="border p-2 rounded-lg bg-base-100 border-slate-400"
                >
                    <EmptyComponent />
                </div>
            )}
            {!isTablesEmpty && (
                <ListComponent
                    cols={6}
                    calcHeight={12}
                    allowScrollBar={false}
                    padding="0 p-2"
                    isBorder={true}
                    className="bg-white"
                >
                    {tables.map((table, index) => {
                        if (table.status !== TableStatusEnum.OCCUPIED) return null;
                        return (
                            <PosTableCardComponent
                                key={index}
                                {...table}
                                onClick={() => handleOnTableClick(table)}
                            />
                        );
                    })}
                </ListComponent>
            )}
            <PosBackButtonComponent />
        </div>
    );
};

export default PosTablesPage;
