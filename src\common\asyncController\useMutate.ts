import { useState } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../redux/store";
import { closeModalAction, setLoadingAction } from "../redux/data/slice";
import { ToastHelper } from "../helpers/ToastHelper";
import { debug } from "../utils/CommonUtils";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { updateCachedData } from "./slice";
import { TranslateConstants } from "../constants/TranslateConstants";
import useOnlineStatus from "../hooks/useOnlineStatus";

interface UseMutateOptions<T> {
    onSuccess?: (params: { data: T; dispatch: AppDispatch; args: any[] }) => void;
    onError?: (params: {
        error: string;
        dispatch: AppDispatch;
        args: any[];
    }) => void;
    beforeStart?: (params: { dispatch: AppDispatch; args: any[] }) => void;
    afterEnd?: (params: { dispatch: AppDispatch; args: any[] }) => void;
    onErrorToast?: string;
    onSuccessToast?: string;
    isShowLoadingPage?: boolean;
    closeModalOnSuccess?: boolean;
    showDefaultErrorToast?: boolean;
    showDefaultSuccessToast?: boolean;
    isOnline?: boolean;
    updateCached?: {
        key: string;
        operation: "add" | "update" | "delete";
        selector?: (data: any) => any;
    };
    beforeStartMiddleware?: (params: { dispatch: AppDispatch; args: any[] }) => boolean;
}

const useMutate = <T>(
    mutateFn: (...args: any[]) => Promise<T>,
    options: UseMutateOptions<T> = {}
) => {
    const {
        onSuccess,
        onError,
        beforeStart,
        afterEnd,
        onErrorToast,
        onSuccessToast,
        isShowLoadingPage = true,
        closeModalOnSuccess = false,
        showDefaultErrorToast = true,
        showDefaultSuccessToast = false,
        isOnline = false,
        updateCached,
        beforeStartMiddleware,
    } = options;

    const dispatch = useDispatch<AppDispatch>();
    const onlineState = useOnlineStatus();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [data, setData] = useState<T | null>(null);

    const mutate = async (...args: any[]) => {
        if (beforeStartMiddleware) {
            const shouldContinue = beforeStartMiddleware({ dispatch, args });
            if (!shouldContinue) return;
        }

        try {
            if (isOnline && !onlineState) {
                ToastHelper.error(
                    TranslateHelper.t(TranslateConstants.ERROR_NO_INTERNET_CONNECTION)
                );
                return;
            }

            setLoading(true);
            setError(null);
            if (isShowLoadingPage) dispatch(setLoadingAction(true));
            if (beforeStart) beforeStart({ dispatch, args });

            const result = await mutateFn(...args);
            setData(result);
            onSuccess?.({ data: result, dispatch, args });

            if (updateCached) {
                dispatch(updateCachedData({ ...updateCached, result }));
            }

            if (onSuccessToast)
                ToastHelper.success(TranslateHelper.t(onSuccessToast));
            else if (showDefaultSuccessToast)
                ToastHelper.success(
                    TranslateHelper.t(TranslateConstants.SUCCESS_TOAST)
                );
            if (closeModalOnSuccess) dispatch(closeModalAction());
        } catch (err: any) {
            if (onErrorToast) ToastHelper.error(TranslateHelper.t(onErrorToast));
            else if (showDefaultErrorToast)
                ToastHelper.error(
                    TranslateHelper.t(TranslateConstants.ERROR_PLEASE_TRY_AGAIN)
                );

            setError(err.message);
            onError?.({ error: err.message, dispatch, args });
            debug(`Error in useMutate: `, err.message);
        } finally {
            setLoading(false);
            if (isShowLoadingPage) dispatch(setLoadingAction(false));
            if (afterEnd) afterEnd({ dispatch, args });
        }
    };

    return { mutate, isLoading: loading, error, data, isError: !!error };
};

export default useMutate;
