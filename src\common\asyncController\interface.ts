export interface FetchState<T> {
    data: T | undefined;
    isLoading: boolean;
    isError: boolean;
    error: string | undefined;
}

export interface FetchArgs<T> {
    key: string;
    promiseFn: () => Promise<T>;
}

export interface UseFetchOptions<T> {
    autoFetchOnMount?: boolean;
    autoFetchIfEmpty?: boolean;
    resetOnUnmount?: boolean;
    onSuccess?: (params: { data: T; dispatch: any, payload: any }) => void;
    onFirstSuccess?: (params: { data: T; dispatch: any, payload: any }) => void;
    onError?: (params: { error: string; dispatch: any }) => void;
    beforeStart?: (params: { dispatch: any }) => void;
    afterEnd?: (params: { dispatch: any }) => void;
    isShowLoadingPage?: boolean;
    onSuccessToast?: string;
    onErrorToast?: string;
    showDefaultErrorToast?: boolean;
    onData?: (data: T | undefined) => void;
    isOnline?: boolean;
}