import { Content, Margins } from "pdfmake/interfaces";

export interface PdfMakeProps {
    isLandScape?: boolean;
    header?: Content;
    headerType?: "normal" | "invoice";
    showFooter?: boolean;
    pageSize?: { width: number ; height: number | 'auto' };
    fontSize?: number;
    isFontBold?: boolean;
    pageMargins?: Margins;
}

export interface IPdfMakePrinterModel {
    printerId: string;
    body: Content;
}

export interface IPdfMakePrinterContentOptionsModel {
    isSvgPlaceholder?: boolean;
    reverseLongText?: boolean;
    showLogo?: boolean;
    logo?: string;
}