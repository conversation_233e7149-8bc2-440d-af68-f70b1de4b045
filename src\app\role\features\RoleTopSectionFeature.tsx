import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { AuthService } from "../../auth/AuthService";
import { TranslateHelper } from "../../../common/helpers/TranslateHelper";
import logo from "/logo.svg";
import { LanguageButtonComponent } from "../../../common/components/LanguageComponent";
import { useTranslate } from "../../../common/hooks/useTranslate";

const RoleTopSectionFeature = () => {
    const navigate = useNavigate();
    const { isArabic } = useTranslate();

    const handleLogout = async () => {
        await AuthService.logout(navigate);
    };

    return (
        <div className="sm:absolute sm:top-0 w-full p-2 sm:p-5">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <img
                        src={logo}
                        alt="logo"
                        className="w-10 h-10 rounded-full"
                    />
                    <div className="flex flex-col">
                        <span className="text-white sm:text-lg font-tajawal-bold">
                            {TranslateHelper.t(isArabic? TranslateConstants.APP_NAME: TranslateConstants.APP_NAME_EN)}
                        </span>
                    </div>
                </div>
                <div className="flex gap-2">
                    <LanguageButtonComponent />
                    <ButtonComponent
                        text={TranslateConstants.LOGOUT}
                        bgColor="warning"
                        className="!w-32"
                        onClick={handleLogout}
                    />
                </div>
            </div>
        </div>
    );
}

export default RoleTopSectionFeature;
