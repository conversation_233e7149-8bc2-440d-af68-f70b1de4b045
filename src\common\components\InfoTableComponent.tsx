import { FC } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { useTranslate } from "../hooks/useTranslate";

interface IBaseItem {
    text?: string;
    value?: number | string;
}

interface IFixedItem extends IBaseItem {
    type?: never;
    onChange?: never;
    placeholder?: never;
}

interface IInputItem extends IBaseItem {
    type?: 'input' | 'inputNumber';
    onChange?: (value: string) => void;
    placeholder?: string;
}

interface IProps {
    items: IFixedItem & IInputItem[];
}

const InfoTableComponent: FC<IProps> = ({ items }) => {
    const { isArabic } = useTranslate();

    return (
        <div className="w-full font-bold flex flex-col justify-end text-center text-slate-500 dark:text-gray-300 border rounded border-gray-400">
            {items.map((item, index) => (
                <div
                    key={index}
                    className={
                        "flex justify-between border-gray-400 text-sm sm:text-base" +
                        " " +
                        (index === items.length - 1 ? "" : "border-b")
                    }
                >
                    <span className="px-2 flex-1 py-1">
                        {!!item.text ? TranslateHelper.t(item.text) : ''}
                    </span>
                    {
                        !item.type ? (
                            <span
                                className={
                                    "flex-1 text-center border-gray-400 px-2 py-1" +
                                    " " +
                                    (isArabic ? "border-r" : "border-l")
                                }
                            >
                                {item.value}
                            </span>
                        ) : (
                            <input
                                type={item.type === 'input' ? "text" : "number"}
                                dir="ltr"
                                className={
                                    "flex-1 w-full text-center border-gray-400 px-2 py-1 focus:outline-none" +
                                    " " +
                                    (isArabic ? "border-r" : "border-l")
                                }
                                value={item.value?.toString() ?? ""}
                                placeholder={item.placeholder}
                                onChange={(e) => item.onChange && item.onChange(e.target.value)}
                            />
                        )
                    }
                </div>
            ))}
        </div>
    );
};

export default InfoTableComponent;
