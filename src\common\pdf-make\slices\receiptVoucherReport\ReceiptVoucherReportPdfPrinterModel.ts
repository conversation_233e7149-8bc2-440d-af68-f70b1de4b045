export interface IReceiptVoucherReportPdfPrinterItemModel {
    number: string;
    destination?: string;
    date: string;
    cash: number;
    network: number;
    note: string;
}

export interface IReceiptVoucherReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IReceiptVoucherReportPdfPrinterItemModel[];
    totals: {
        cash: number;
        network: number;
        count: number;
    };
}