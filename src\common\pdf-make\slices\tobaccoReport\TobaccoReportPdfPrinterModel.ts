interface ITobaccoReportPdfPrinterItemModel {
    invoiceNumber: string;
    name: string;
    price: number;
    quantity: number;
    discount: number;
    subTotal: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: string;
}

export interface ITobaccoReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: ITobaccoReportPdfPrinterItemModel[];
    totals: {
        subTotal: number;
        discount: number;
        tobaccoTax: number;
        vat: number;
        total: number;
    };
}