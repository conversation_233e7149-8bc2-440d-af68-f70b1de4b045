import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import AppSlice from './data/slice'
import { setupListeners } from '@reduxjs/toolkit/query'
import { appApi } from './api/slice'
import PosSlice from '../../app/pos/redux/slice'
import fetchReducer from '../asyncController/slice'

const store = configureStore({
    reducer: {
        app: AppSlice,
        pos: PosSlice,
        fetch: fetchReducer,
        [appApi.reducerPath]: appApi.reducer,
    },
    middleware: (getDefaultMiddleware) => {
        return getDefaultMiddleware({
            serializableCheck: false,
        }).concat(appApi.middleware)
    }
})

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export const useAppDispatch: () => typeof store.dispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector

export const appState = () => store.getState().app;
export const posState = () => store.getState().pos;
export const apiState = () => store.getState().appApi;
export const fetchState = () => store.getState().fetch;

export type AppDispatch = typeof store.dispatch;
export const dispatchAction = store.dispatch;

export default store