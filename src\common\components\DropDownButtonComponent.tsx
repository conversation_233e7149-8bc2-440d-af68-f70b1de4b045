import { FC, useMemo, useState } from "react";
import { useTranslate } from "../hooks/useTranslate";
import { useNavigate } from "react-router-dom";

export interface IDropDownMenuItem {
    icon?: React.ReactNode;
    text: string;
    onClick?: () => void;
    active?: boolean;
    route?: string;
    isDisabled?: boolean;
}

interface IProps {
    icon: React.ReactNode;
    text?: string;
    onClick?: () => void;
    bgColor?: "primary" | "white" | "slate";
    iconColor?: "primary" | "white" | "black" | "red";
    rounded?: boolean;
    width?: "w-full" | "w-1/2" | "w-1/3" | "w-1/4" | "w-1/5" | "w-1/6";
    height?: "h-full" | "h-12" | "h-16" | "h-20" | "";
    padding?: "p-1" | "p-2";
    className?: string;
    textSize?: "text-sm" | "text-lg" | "text-xl" | "text-2xl" | "text-3xl";
    iconSize?: "text-2xl" | "text-3xl" | "text-4xl" | "text-5xl" | "text-6xl";
    items?: IDropDownMenuItem[];
    menuIconSize?: "text-2xl" | "text-3xl";
}

const DropDownButtonComponent: FC<IProps> = ({
    icon,
    text,
    onClick,
    bgColor = "primary",
    iconColor = "white",
    rounded = true,
    width = "w-full",
    height = "h-full",
    padding = "p-1",
    className,
    textSize = "text-sm",
    iconSize = "text-2xl",
    items,
    menuIconSize = "text-3xl",
}) => {
    const { translate } = useTranslate();
    const [showMenu, setShowMenu] = useState(false);
    const navigate = useNavigate();

    const getBgColor = useMemo(() => {
        switch (bgColor) {
            case "primary":
                return "bg-[#226bb2] hover:bg-[#1e5a9d] text-white";
            case "white":
                return "bg-white hover:bg-gray-200";
            case "slate":
                return "bg-slate-600 hover:bg-slate-500 text-white";
        }
    }, [bgColor]);

    const getIconColor = useMemo(() => {
        switch (iconColor) {
            case "primary":
                return "text-[#226bb2]";
            case "white":
                return "text-white";
            case "black":
                return "text-black";
            case "red":
                return "text-red-600";
        }
    }, [iconColor]);

    const handleOnClick = () => {
        setShowMenu(true);
        onClick?.();
    };

    return (
        <div className={"relative" + " " + width + " " + height}>
            {!!items?.length && showMenu && (
                <>
                    <div
                        className="w-s h-s bg-gray-700 opacity-65 fixed top-0 right-0 z-10"
                        onClick={() => setShowMenu(false)}
                    ></div>
                    <div className="bg-white shadow border absolute bottom-[4.74rem] w-full rounded-t flex flex-col font-tajawal-bold z-20">
                        {items?.map((item, index) => (
                            <div
                                key={index}
                                onClick={() => {
                                    if (item.isDisabled) return;
                                    item.onClick?.();
                                    item.route && navigate(item.route);
                                    setShowMenu(false);
                                }}
                                className={
                                    "flex gap-2 items-center w-full border-b py-4 px-2 hover:bg-slate-600 hover:text-white" +
                                    " " + 
                                    (item.active ? "text-[#226bb2]" : "text-slate-600") +
                                    " " +
                                    (item.isDisabled ? "opacity-50 !cursor-not-allowed" : "cursor-pointer active:scale-95") +
                                    " " +
                                    menuIconSize
                                }
                            >
                                {!!item.icon && item.icon}
                                <div className="text-lg">{item.text}</div>
                            </div>
                        ))}
                    </div>
                </>
            )}
            <div
                className={
                    "flex flex-col items-center cursor-pointer group active:scale-95 justify-center w-full h-full" +
                    " " +
                    (rounded ? "rounded" : "") +
                    " " +
                    getBgColor +
                    " " +
                    getIconColor +
                    " " +
                    padding +
                    " " +
                    className
                }
                onClick={handleOnClick}
            >
                <div
                    className={
                        "group-hover:scale-105 transition-transform" + " " + iconSize
                    }
                >
                    {icon}
                </div>
                {text && <div className={textSize}>{translate(text)}</div>}
            </div>
        </div>
    );
};

export default DropDownButtonComponent;
