import { FC } from "react";
import { IAdminReceiptVoucherInputs } from "../AdminReceiptVoucherInterface";
import { AdminReceiptVoucherDestinationsConstant, AdminReceiptVoucherInputs } from "../AdminReceiptVoucherConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import useCustomState from "../../../../../common/hooks/useCustomState";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { AdminReceiptVoucherValidation } from "../AdminReceiptVoucherValidation";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { IReceiptVoucherModel } from "../../../../../common/models/ReceiptVoucherModel";
import { ReceiptVouchersApiRepo } from "../../../../../common/repos/api/ReceiptVouchersApiRepo";

interface IProps {
    isEdit?: boolean;
    item?: IReceiptVoucherModel;
}

const AdminReceiptVoucherModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminReceiptVoucherInputs>(
        AdminReceiptVoucherInputs(),
        { updateState: item, explodeFromReset: ["destination", "customer"] }
    );
    const addReceiptVoucher = useFlatMutate(ReceiptVouchersApiRepo.addReceiptVoucher, {
        updateCached: { key: EndPointsEnums.RECEIPT_VOUCHERS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateReceiptVoucher = useFlatMutate(ReceiptVouchersApiRepo.updateReceiptVoucher, {
        updateCached: {
            key: EndPointsEnums.RECEIPT_VOUCHERS,
            operation: "update",
            selector: (data: IReceiptVoucherModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    // const {
    //     data: customers,
    //     isLoading: customersLoading,
    //     isError: customersError,
    //     refetch: customersRefetch,
    // } = useFetch(
    //     "receipt-voucher-" + EndPointsEnums.CUSTOMERS,
    //     (search, limit = 5) => CustomersApiRepo.getCustomers(search, limit),
    //     { resetOnUnmount: true }
    // );

    const onClick = () => {
        const isValid = AdminReceiptVoucherValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateReceiptVoucher(item.id, state);
        addReceiptVoucher(state);
    };

    return (
        <>
            <form className="px-2">
                <DropDownSearchComponent
                    label={TranslateConstants.RECEIPT_DESTINATION}
                    items={AdminReceiptVoucherDestinationsConstant}
                    titleSelector={(item) => TranslateHelper.t(item.name ?? TranslateConstants.RECEIPT_VOUCHER)}
                    isOutFilter={true}
                    isSearchable={false}
                    defaultValue={TranslateHelper.t(state.destination ?? TranslateConstants.RECEIPT_VOUCHER)}
                    onSelect={(item) => setState({ ...state, destination: item.name, customer: undefined })}
                />
                {/* {
                    state.destination === ReceiptVoucherDestinationEnum.CUSTOMER && (
                        <DropDownSearchComponent
                            label={TranslateConstants.CUSTOMER}
                            onSelect={(customer: ICustomerModel) => setState({ ...state, customer })}
                            isOutFilter={true}
                            onChange={(search) => customersRefetch(search)}
                            items={customers || []}
                            titleSelector={(item) => item.name}
                            subtitleSelector={(item) => item.mobile}
                            isLoading={customersLoading}
                            isError={customersError}
                            defaultValue={state.customer?.name}
                        />
                    )
                } */}
                <InputComponent
                    type="datetime-local"
                    label={TranslateConstants.DATE}
                    onChange={(date) => setState({ ...state, date: new Date(date).getTime() })}
                    value={DateUtils.toIsoString(state.date)}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.CASH}
                    onChange={(cash) => setState({ ...state, cash: Number(cash) })}
                    value={(state.cash || "").toString()}
                    placeholder="0"
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.NETWORK}
                    onChange={(network) => setState({ ...state, network: Number(network) })}
                    value={(state.network || "").toString()}
                    placeholder="0"
                />
                <InputComponent
                    label={TranslateConstants.STATEMENT}
                    type="textarea"
                    value={state.note || ""}
                    onChange={(note) => setState({ ...state, note })}
                    textAreaRows={2}
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminReceiptVoucherModal;
