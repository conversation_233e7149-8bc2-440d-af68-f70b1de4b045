import { FC } from "react";
import IconButtonComponent from "../../../common/components/IconButtonComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { LuForward } from "react-icons/lu";
import { RoutsConstants } from "../../../common/constants/RoutesConstants";

interface IProps {
    route?: string;
    className?: string;
    onClick?: () => void;
}

const PosBackButtonComponent: FC<IProps> = ({
    route = RoutsConstants.pos.home.fullPath,
    className,
    onClick
}) => {
    return (
        <IconButtonComponent
            icon={<LuForward />}
            iconSize="text-4xl"
            text={TranslateConstants.BACK}
            textSize="text-lg"
            bgColor="slate"
            className={"min-h-28 !w-1/6 rounded-xl" + " " + className}
            route={route}
            onClick={onClick}
        />
    );
};

export default PosBackButtonComponent;
