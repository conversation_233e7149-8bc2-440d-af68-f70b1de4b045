export interface IDeliveryAppProductModel {
    additionId?: number;
    productId: number;
    sizeId?: number;
    isHungerStationActive: boolean;
    hungerStation: number;
    isJahezActive: boolean;
    jahez: number;
    isToYouActive: boolean;
    toYou: number;
    isTheChefzActive: boolean;
    theChefz: number;
    isKeetaActive: boolean;
    keeta: number;
    isNinjaActive: boolean;
    ninja: number;
    isMeshwarFoodActive: boolean;
    meshwarFood: number;
    isLocateActive: boolean;
    locate: number;
    isNoonFoodActive: boolean;
    noonFood: number;
    isMarsoolActive: boolean;
    marsool: number;
}
