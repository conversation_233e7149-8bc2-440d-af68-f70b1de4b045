
import { TranslateConstants } from "../../common/constants/TranslateConstants";
import { RegexHelper } from "../../common/helpers/RegexHelper";
import {
    IAuthLoginState,
    IAuthRegisterInputs,
    IAuthRegisterState,
} from "./AuthInterface";

export class AuthValidation {
    static loginValidation = (
        email: string,
        password: string,
        setState: React.Dispatch<React.SetStateAction<IAuthLoginState>>
    ) => {
        let isValid = true;

        if (!RegexHelper.isEmail(email)) {
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PLEASE_PROVIDE_VALID_EMAIL,
            }));
            isValid = false;
        } else if (password.length < 8) {
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PASSWORD_MUST_HAVE_AT_LEAST_8_CHARACTERS,
            }));
            isValid = false;
        }

        return isValid;
    };

    static registerValidation = (
        values: IAuthRegisterInputs,
        setState: React.Dispatch<React.SetStateAction<IAuthRegisterState>>
    ) => {
        let isValid = true;

        if (!values.name) {
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.NAME_FIELD_IS_REQUIRED,
                errorFields: ["name"],
            }));
            isValid = false;
        } else if (!RegexHelper.isEmail(values.email)) {
            isValid = false;
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PLEASE_PROVIDE_VALID_EMAIL,
                errorFields: ["email"],
            }));
        } else if (!RegexHelper.isMobile(values.mobile)) {
            isValid = false;
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PLEASE_PROVIDE_VALID_MOBILE,
                errorFields: ["mobile"],
            }));
        } else if (values.password.length < 8) {
            isValid = false;
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PASSWORD_MUST_HAVE_AT_LEAST_8_CHARACTERS,
                errorFields: ["password"],
            }));
        } else if (values.password.length > 20) {
            isValid = false;
            setState((prev) => ({
                ...prev,
                error: TranslateConstants.PASSWORD_MUST_HAVE_AT_MOST_20_CHARACTERS,
                errorFields: ["password"],
            }));
        }

        return isValid;
    };
}
