import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminCustomersReportTableHeaders } from "./AdminCustomersReportsConstants";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminCustomersReportTotalInfoFeature from "./features/AdminCustomersReportTotalInfoFeature";
import useFetch from "../../../../common/asyncController/useFetch";
import { CustomersApiRepo } from "../../../../common/repos/api/CustomerApiRepo";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { useMemo, useState } from "react";
import { AdminCustomersReportService } from "./AdminCustomersReportService";
import { ReceiptVouchersApiRepo } from "../../../../common/repos/api/ReceiptVouchersApiRepo";

const AdminCustomersReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [selectedCustomer, setSelectedCustomer] = useState<ICustomerModel | undefined>(undefined)
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: customers,
        isLoading: customersLoading,
        isError: customersError,
        refetch: customersRefetch,
    } = useFetch(
        "report-" + EndPointsEnums.CUSTOMERS,
        (search, limit = 5) => CustomersApiRepo.getCustomers(search, limit),
        {
            resetOnUnmount: true,
            onFirstSuccess({ data }) {
                const now = new Date();
                data?.[0].id && onDate(now, now, data?.[0]);
            },
        }
    );

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-customers-" + EndPointsEnums.ORDERS_SUM,
        (startTime, endTime, customerId) => OrdersApiRepo.getSum(startTime, endTime, customerId),
        { autoFetchIfEmpty: false }
    );

    const {
        data: orders,
        isLoading: ordersLoading,
        isError: ordersError,
        refetch: refetchOrders,
    } = useFetch(
        "report-customers-" + EndPointsEnums.ORDERS,
        (startTime, endTime, customerId) => OrdersApiRepo.getOrders(startTime, endTime, customerId),
        { autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-customers-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (startTime, endTime, customerId) => ReturnedOrdersApiRepo.getReturnedOrdersSum(startTime, endTime, customerId),
        { autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrders,
        isLoading: returnedOrdersLoading,
        isError: returnedOrdersError,
        refetch: refetchReturnedOrders,
    } = useFetch(
        "report-customers-" + EndPointsEnums.RETURNED_ORDERS,
        (startTime, endTime, customerId) => ReturnedOrdersApiRepo.getReturnedOrders(startTime, endTime, customerId),
        { autoFetchIfEmpty: false }
    );

    const {
        data: receiptVoucherSum,
        isLoading: receiptVoucherSumLoading,
        isError: receiptVoucherSumError,
        refetch: refetchReceiptVoucherSum,
    } = useFetch(
        "report-customers-" + EndPointsEnums.RECEIPT_VOUCHERS_SUM,
        (startTime, endTime, customerId) => ReceiptVouchersApiRepo.getSum(startTime, endTime, undefined, undefined, undefined, customerId),
        { autoFetchIfEmpty: false }
    );

    const {
        data: receiptVouchers,
        isLoading: receiptVouchersLoading,
        isError: receiptVouchersError,
        refetch: refetchReceiptVouchers,
    } = useFetch(
        "report-customers-" + EndPointsEnums.RECEIPT_VOUCHERS,
        (startTime, endTime, customerId) => ReceiptVouchersApiRepo.getReceiptVouchers({ startTime, endTime, customerId }),
        { autoFetchIfEmpty: false }
    );

    const onDate = (
        startDate: Date = new Date(),
        endDate: Date = new Date(),
        customer?: ICustomerModel
    ) => {
        const start = startDate.getTime();
        const end = endDate.getTime();
        setSelectedCustomer(customer);
        setDateRange({ startTime: start, endTime: end });
        refetchOrderSum(start, end, customer?.id);
        refetchOrders(start, end, customer?.id);
        refetchReturnedOrderSum(start, end, customer?.id);
        refetchReturnedOrders(start, end, customer?.id);
        refetchReceiptVoucherSum(start, end, customer?.id);
        refetchReceiptVouchers(start, end, customer?.id);
    };

    const data = useMemo(() => {
        return AdminCustomersReportService.handleData(
            orders,
            returnedOrders,
            receiptVouchers,
        );
    }, [orders, returnedOrders, receiptVouchers]);

    const totals = useMemo(() =>
        AdminCustomersReportService.handleTotalInfo(
            orderSum,
            returnedOrderSum,
            receiptVoucherSum,
        ), [
        orderSum,
        returnedOrderSum,
        receiptVoucherSum,
    ]);

    const onDownload = () =>
        AdminCustomersReportService.handleDownload(dateRange, data, totals, selectedCustomer);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                showSearch={true}
                onDate={onDate}
                onSearch={(value) => {
                    if (value) customersRefetch(value);
                }}
                items={customers}
                isSearchLoading={customersLoading}
                isSearchError={customersError}
                titleSelector={(item) => item.name}
                subtitleSelector={(item) => item.mobile}
                defaultValue={customers?.[0]?.name}
                defaultItem={customers?.[0]}
                disableSearchButtonByDefault={false}
                onDownload={onDownload}
                isDownloadDisabled={!orders?.length || !orderSum}
            />
            <AdminCustomersReportTotalInfoFeature totals={totals} />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={
                    orderSumLoading ||
                    ordersLoading ||
                    returnedOrderSumLoading ||
                    returnedOrdersLoading ||
                    receiptVoucherSumLoading ||
                    receiptVouchersLoading
                }
                isError={
                    orderSumError ||
                    ordersError ||
                    returnedOrderSumError ||
                    returnedOrdersError ||
                    receiptVoucherSumError ||
                    receiptVouchersError
                }
                height={isXs ? 17.5 : isSm ? 14.6 : 15.6}
            >
                <TableComponent
                    headers={AdminCustomersReportTableHeaders()}
                    items={data || []}
                    selectors={(item: (IOrderModel & { isReturned: boolean, customNumber: string, operationType: string })) => [
                        DateUtils.format(item.startTime),
                        item.customNumber,
                        TranslateHelper.t(item.operationType),
                        TranslateHelper.t(item.type),
                        item.subTotal.toFixed(2),
                        item.discount.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        item.deferred.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminCustomersReportPage;
