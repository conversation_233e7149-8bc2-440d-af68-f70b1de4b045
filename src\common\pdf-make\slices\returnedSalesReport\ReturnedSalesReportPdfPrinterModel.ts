interface IReturnedSalesReportPdfPrinterItemModel {
    returnedInvoiceNumber: string;
    invoiceNumber: string;
    orderType: string;
    subTotal: number;
    discount: number;
    tobaccoTax?: number;
    vat: number;
    total: number;
    cash: number;
    network: number;
    credit: number;
}

export interface IReturnedSalesReportPdfPrinterModel {
    startDate: Date;
    endDate: Date;
    items?: IReturnedSalesReportPdfPrinterItemModel[];
    totals: {
        count: number;
        vat: number;
        total: number;
        cash: number;
        network: number;
        credit: number;
    };
}