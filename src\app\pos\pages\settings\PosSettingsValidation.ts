import { posPrintersKeyConstant } from "../../../../common/constants/ConfigConstants";
import { LocalStorageHelper } from "../../../../common/helpers/LocalStorageHelper";
import { IPrinterData } from "../../../../common/interfaces";

export class PosSettingsValidation {
    static checkPrintersDeeplyEqual = (data: IPrinterData[]) => {
        const printers: IPrinterData[] = LocalStorageHelper.get(posPrintersKeyConstant) || [];
        return !data.length || JSON.stringify(printers) === JSON.stringify(data);
    };
}