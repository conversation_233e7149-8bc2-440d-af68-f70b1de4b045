import { WhatsAppLink } from "../constants/CommonConstants";
import { TranslateConstants } from "../constants/TranslateConstants";
import useOnlineStatus from "../hooks/useOnlineStatus";
import { useTranslate } from "../hooks/useTranslate";
import RandomBackGroundImageComponent from "./RandomBackGroundImageComponent";
import logo from "/logo.svg";
import { MdSignalWifiStatusbarConnectedNoInternet3 } from "react-icons/md";

interface IProps {
    children: React.ReactNode;
    guard?: boolean;
}

const OnlineStatusGuardComponent = ({ children, guard = true }: IProps) => {
    const { translate } = useTranslate();
    const isOnline = useOnlineStatus();

    if (guard && !isOnline) {
        return (
            <RandomBackGroundImageComponent>
                <div className="h-s w-screen flex flex-col items-center justify-center gap-6 p-4 text-center">
                    <div className="flex flex-col justify-center items-center">
                        <img src={logo} alt="logo" className="animate-h_spin w-28 mb-2" />
                        <h1 className={`text-2xl text-white`}>
                            {translate(TranslateConstants.APP_NAME)}
                        </h1>
                    </div>
                    <div className="bg-white p-2 py-5 rounded-md flex flex-col items-center justify-center gap-2 w-full sm:w-1/2 lg:w-1/3">
                        <MdSignalWifiStatusbarConnectedNoInternet3 className="text-7xl text-red-500" />
                        <div className="flex flex-col items-center justify-center gap-1">
                            <h1 className="text-xl font-bold mb-5">
                                {translate(TranslateConstants.NO_INTERNET_CONNECTION)}
                            </h1>
                            <p className="text-gray-500">
                                {translate(
                                    TranslateConstants.PLEASE_CHECK_YOUR_INTERNET_CONNECTION
                                )}
                            </p>
                            <p className="text-gray-500">
                                {translate(
                                    TranslateConstants.IF_THE_PROBLEM_PERSISTS_PLEASE_CONTACT_THE_TECHNICAL_SUPPORT
                                )}
                            </p>
                        </div>
                    </div>
                    <p className="text-white flex gap-2">
                        <span>
                            {translate(TranslateConstants.FOR_CONTACT_THE_TECHNICAL_SUPPORT)}
                        </span>
                        <a
                            href={WhatsAppLink(
                                "مرحباً لدي مشكلة في تطبيق مطعم الرؤيا الرقمية. (لا يوجد اتصال بالانترنت)"
                            )}
                            className="text-blue-400 underline"
                            target="_blank"
                        >
                            {translate(TranslateConstants.TAP_HERE)}
                        </a>
                    </p>
                </div>
            </RandomBackGroundImageComponent>
        );
    }

    return children;
};

export default OnlineStatusGuardComponent;
