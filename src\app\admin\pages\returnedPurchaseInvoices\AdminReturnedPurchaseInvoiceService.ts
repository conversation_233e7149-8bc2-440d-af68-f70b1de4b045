import { UpdateFetch } from "../../../../common/asyncController/updateFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { IReturnedPurchaseInvoiceModel } from "../../../../common/models/ReturnedPurchaseInvoiceModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { PurchaseInvoicePdfPrinterContent } from "../../../../common/pdf-make/slices/purchaseInvoice/PurchaseInvoicePdfPrinterContent";
import { IReturnedPurchaseInvoicePdfPrinterModel } from "../../../../common/pdf-make/slices/returnedPurchaseInvoice/ReturnedPurchaseInvoiceReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { fixedNumber } from "../../../../common/utils/numberUtils";

export class AdminReturnedPurchaseInvoiceService {
    static async handlePreview(returnedPurchaseInvoice: IReturnedPurchaseInvoiceModel) {
        const model: IReturnedPurchaseInvoicePdfPrinterModel = {
            items: returnedPurchaseInvoice.returnedPurchaseInvoiceProducts?.map((el) => ({
                name: el.name,
                quantity: fixedNumber(el.quantity),
                price: fixedNumber(el.price),
                discount: fixedNumber(el.discount),
                subTotal: el.subTotal,
                vat: el.vat,
                total: fixedNumber(el.total),
            })),
            note: returnedPurchaseInvoice.note,
            totals: {
                nativeTotal: returnedPurchaseInvoice.nativeTotal,
                productsDiscount: returnedPurchaseInvoice.productsDiscount,
                subTotal: returnedPurchaseInvoice.subTotal,
                vat: returnedPurchaseInvoice.vat,
                total: returnedPurchaseInvoice.total,
            },
        };

        PdfMakeUtils.preview(PurchaseInvoicePdfPrinterContent(model), {
            headerType: "invoice",
            header: await PdfMakeHeaders.invoice({
                InvoiceNumber: returnedPurchaseInvoice.number.toString(),
                titleAr: "فاتورة مرتجع مشتريات",
                titleEn: "Returned Purchase Invoice",
                startDate: DateUtils.format(returnedPurchaseInvoice.date, "dd/MM/yyyy"),
                supplierName: returnedPurchaseInvoice.supplier?.name || "",
                supplierMobile: returnedPurchaseInvoice.supplier?.mobile || "",
                supplierTaxNumber: returnedPurchaseInvoice.supplier?.taxNumber || "",
                supplierAddress: returnedPurchaseInvoice.supplier?.address || "",
            }),
        });
    }

    static async updatePurchaseInvoiceReturn(purchaseInvoiceId?: number) {
        UpdateFetch.dynamicUpdate(EndPointsEnums.PURCHASE_INVOICES, async (data: IPurchaseInvoiceModel[]) => {
            return data?.map((item: IPurchaseInvoiceModel) => {
                if (item.id === purchaseInvoiceId) return { ...item, isReturned: true };
                return item;
            });
        });
    }
}
