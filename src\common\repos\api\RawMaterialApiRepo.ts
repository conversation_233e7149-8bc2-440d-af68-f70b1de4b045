import { IAdminRawMaterialInputs } from "../../../app/admin/pages/rawMaterial/AdminRawMaterialsInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IRawMaterialModel } from "../../models/RawMaterialModel";
import { debug } from "../../utils/CommonUtils";

export class RawMaterialApiRepo {
    static async getRawMaterials(
        search?: string, 
        limit?: number,
        getSuppliers?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IRawMaterialModel[]>(
                EndPointsEnums.RAW_MATERIALS,
                { params: { search, limit, getSuppliers } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`RawMaterialsApiRepo [getRawMaterials] Error: `, error);
            throw error;
        }
    }

    static async addRawMaterial(body: IAdminRawMaterialInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.post<IRawMaterialModel>(
                EndPointsEnums.RAW_MATERIALS,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`RawMaterialsApiRepo [addRawMaterial] Error: `, error);
            throw error;
        }
    }

    static async updateRawMaterial(id: number, body: IAdminRawMaterialInputs, updateMetaData = true) {
        try {
            const res = await AxiosHelper.patch<IRawMaterialModel>(
                EndPointsEnums.RAW_MATERIALS,
                id,
                body,
                { params: { updateMetaData } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`RawMaterialsApiRepo [updateRawMaterial] Error: `, error);
            throw error;
        }
    }
}
