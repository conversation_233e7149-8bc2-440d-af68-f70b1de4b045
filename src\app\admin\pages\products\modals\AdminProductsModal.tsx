import { FC, useEffect, useMemo } from "react";
import {
    IAdminProductInputs,
    IAdminProductSizeInputs,
} from "../AdminProductsInterface";
import {
    AdminProductInputs,
    AdminProductSizeHeaders,
    AdminProductSizeInputs,
    AdminProductSizeTypeOptions,
} from "../AdminProductsConstants";
import { IProductModel } from "../../../../../common/models/ProductModel";
import { ICategoryModel } from "../../../../../common/models/CategoryModel";
import UploadFileComponent from "../../../../../common/components/UploadImageComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import InputComponent from "../../../../../common/components/InputComponent";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { CategoriesApiRepo } from "../../../../../common/repos/api/CategoriesApiRepo";
import useFetch from "../../../../../common/asyncController/useFetch";
import useCustomState from "../../../../../common/hooks/useCustomState";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminProductsValidation } from "../AdminProductsValidation";
import { ProductsApiRepo } from "../../../../../common/repos/api/ProductsApiRepo";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { ProductSizeTypeEnum } from "../../../../../common/enums/DataEnums";
import MultiInputsComponent from "../../../../../common/components/MultiInputsComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import useActions from "../../../../../common/redux/data/useActions";
import DividerComponent from "../../../../../common/components/DividerComponent";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";
import { fixedNumber } from "../../../../../common/utils/numberUtils";
import { OrganizationHelper } from "../../../../../common/helpers/OrganizationHelper";

interface IProps {
    item?: IProductModel;
}

const AdminProductsModal: FC<IProps> = ({ item }) => {
    const actions = useActions();
    const { isSm } = useScreenSize();
    const [state, setState, resetState] = useCustomState<IAdminProductInputs>(
        AdminProductInputs,
        {
            updateState: item,
            explodeFromReset: ["categoryId"],
            explodeFromUpdate: ["image"],
        }
    );
    const { data, isLoading } = useFetch(
        EndPointsEnums.CATEGORIES,
        CategoriesApiRepo.getCategories
    );

    const hasTobaccoTax = useMemo(() => {
        return (
            state.productSizeType === ProductSizeTypeEnum.FIXED &&
            OrganizationHelper.hasTobaccoTax()
        );
    }, [state.productSizeType]);

    const isMultiple = useMemo(() => {
        return state.productSizeType === ProductSizeTypeEnum.MULTIPLE;
    }, [state.productSizeType]);

    const defaultCategory = useMemo<ICategoryModel | undefined>(() => {
        const cat = !!item
            ? data?.find((e: ICategoryModel) => e.id === item?.categoryId)
            : data?.[0];
        if (cat) setState({ ...state, categoryId: cat.id });
        return cat;
    }, [data, item]);

    const addProduct = useFlatMutate(ProductsApiRepo.addProduct, {
        updateCached: { key: EndPointsEnums.PRODUCTS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateProduct = useFlatMutate(ProductsApiRepo.updateProduct, {
        updateCached: {
            key: EndPointsEnums.PRODUCTS,
            operation: "update",
            selector: (data: IProductModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminProductsValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateProduct(item.id, state);
        addProduct(state);
    };

    useEffect(() => {
        actions.openModal({
            size: isMultiple && !isSm ? "3xl" : "sm",
        });
    }, [state.productSizeType, isSm]);

    return (
        <>
            <ListComponent
                padding="0 px-2"
                cols={isMultiple && !isSm ? 2 : 1}
                calcHeight={isMultiple && !isSm ? 28 : 0}
            >
                <div className="flex flex-col max-h-min">
                    <UploadFileComponent
                        label={TranslateConstants.IMAGE}
                        containerClassName="m-auto !h-28"
                        image={item?.image}
                        onFileChange={(image) => setState({ ...state, image })}
                        file={state.image}
                    />
                    <DropDownSearchComponent
                        items={data || []}
                        label={TranslateConstants.CATEGORY}
                        titleSelector={(item: ICategoryModel) => item.name}
                        defaultValue={defaultCategory?.name}
                        onSelect={(item: ICategoryModel) =>
                            setState({ ...state, categoryId: item.id })
                        }
                        isLoading={isLoading}
                        isSearchable={false}
                    />
                    <InputComponent
                        label={TranslateConstants.NAME}
                        onChange={(value) => setState({ ...state, name: value })}
                        value={state.name}
                    />
                    <InputComponent
                        label={TranslateConstants.SECOND_NAME}
                        onChange={(secondName) => setState({ ...state, secondName })}
                        value={state.secondName || ""}
                    />
                    <DropDownSearchComponent
                        items={AdminProductSizeTypeOptions}
                        label={TranslateConstants.PRODUCT_TYPE}
                        titleSelector={(item) => TranslateHelper.t(item.label)}
                        defaultValue={TranslateHelper.t(
                            AdminProductSizeTypeOptions.find(
                                (e) => e.value === state.productSizeType
                            )?.label || ""
                        )}
                        onSelect={(item) =>
                            setState({ ...state, productSizeType: item.value })
                        }
                        isSearchable={false}
                    />
                    {state.productSizeType === ProductSizeTypeEnum.FIXED && (
                        <InputComponent
                            type="number"
                            label={TranslateConstants.PRICE}
                            onChange={(value) =>
                                setState({ ...state, price: fixedNumber(value) })
                            }
                            value={state.price?.toString() ?? ""}
                            placeholder="0.00"
                        />
                    )}
                    <div
                        className={
                            "grid gap-5 pt-2" +
                            " " +
                            (hasTobaccoTax ? "my-1 sm:grid-cols-2" : "grid-cols-2")
                        }
                    >
                        <ToggleButtonComponent
                            onChange={(isSubjectToTobaccoTax) =>
                                setState({ ...state, isSubjectToTobaccoTax })
                            }
                            label={TranslateConstants.SUBJECT_TO_TOBACCO_TAX}
                            value={state.isSubjectToTobaccoTax}
                            className={
                                !hasTobaccoTax || state.isIncludingAdditions ? "hidden" : ""
                            }
                        />
                        <ToggleButtonComponent
                            onChange={(isIncludingAdditions) =>
                                setState({ ...state, isIncludingAdditions })
                            }
                            label={TranslateConstants.INCLUDING_ADDITIONS}
                            value={state.isIncludingAdditions}
                            className={
                                (!hasTobaccoTax ? "col-start-2 mr-auto" : "sm:mr-auto") +
                                " " +
                                (state.isSubjectToTobaccoTax ? "hidden" : "")
                            }
                        />
                        <ToggleButtonComponent
                            onChange={(active) => setState({ ...state, active })}
                            label={TranslateConstants.ACTIVATION}
                            value={state.active}
                            className={
                                !hasTobaccoTax ||
                                    state.isSubjectToTobaccoTax ||
                                    state.isIncludingAdditions
                                    ? "col-start-1 row-start-1"
                                    : ""
                            }
                        />
                    </div>
                </div>
                {state.productSizeType === ProductSizeTypeEnum.MULTIPLE && (
                    <div className="flex gap-2">
                        {!isSm && <DividerComponent isVertical={true} />}
                        <ListComponent padding="0" calcHeight={isSm ? 0 : 28}>
                            <MultiInputsComponent<IAdminProductSizeInputs>
                                showMobileView={true}
                                buttonText={TranslateConstants.ADD_SIZE}
                                headers={AdminProductSizeHeaders}
                                inputs={AdminProductSizeInputs}
                                onResult={(sizes) => setState({ ...state, sizes })}
                                defaultValues={state.sizes}
                                productButtonClassName="max-h-min mt-auto"
                            />
                        </ListComponent>
                    </div>
                )}
            </ListComponent>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminProductsModal;
