import { IAuthLoginState, IAuthRegisterState, IAuthState } from "./AuthInterface";

export const AuthLoginInitialState: IAuthLoginState = {
    inputs: {
        email: "",
        password: "",
    },
    loading: false,
    error: "",
};

export const AuthRegisterInitialState: IAuthRegisterState = {
    inputs: {
        name: "",
        email: "",
        mobile: "",
        password: "",
    },
    loading: false,
    error: "",
};

export const AuthInitialState: IAuthState = {
    view: "login",
}
